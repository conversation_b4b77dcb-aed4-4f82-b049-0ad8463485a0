<!-- Community Projects Widget -->
<div class="bg-white shadow-lg rounded-lg p-6 mb-8">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-900">🌍 Community Projects</h2>
        <a href="{{ route('register') }}" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm font-medium">
            Join Community
        </a>
    </div>

    <!-- Project Type Selector (Admin Configurable) -->
    @if(isset($projectType) && $projectType !== 'all')
        <div class="mb-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {{ ucfirst(str_replace('_', ' ', $projectType)) }} Projects
            </span>
        </div>
    @endif

    <!-- Projects Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        @forelse($projects as $project)
        <div class="bg-gray-50 rounded-lg overflow-hidden border border-gray-200 hover:shadow-md transition-shadow">
            <!-- Project Image/Gradient -->
            <div class="h-32 bg-gradient-to-r {{ $project['gradient'] ?? 'from-green-400 to-blue-500' }}"></div>
            
            <div class="p-4">
                <!-- Project Type Badge -->
                <div class="flex items-center justify-between mb-2">
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium {{ $project['badge_class'] ?? 'bg-green-100 text-green-800' }}">
                        {{ $project['type_label'] ?? 'Project' }}
                    </span>
                    @if($project['featured'] ?? false)
                        <span class="text-yellow-500">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </span>
                    @endif
                </div>

                <!-- Project Title -->
                <h3 class="font-bold text-gray-900 mb-2 line-clamp-2">{{ $project['title'] }}</h3>
                
                <!-- Project Description -->
                <p class="text-gray-600 text-sm mb-3 line-clamp-3">{{ $project['description'] }}</p>
                
                <!-- Project Stats -->
                <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                    @if(isset($project['participants']))
                        <span>👥 {{ $project['participants'] }} participants</span>
                    @endif
                    @if(isset($project['goal_amount']))
                        <span>💰 ${{ number_format($project['goal_amount']) }} goal</span>
                    @endif
                    @if(isset($project['votes']))
                        <span>👍 {{ $project['votes'] }} votes</span>
                    @endif
                </div>

                <!-- Progress Bar (if applicable) -->
                @if(isset($project['progress']))
                <div class="mb-3">
                    <div class="flex justify-between text-xs text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{{ $project['progress'] }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: {{ $project['progress'] }}%"></div>
                    </div>
                </div>
                @endif

                <!-- Action Button -->
                <a href="{{ route('register') }}" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 transition-colors">
                    Join to Participate
                </a>
            </div>
        </div>
        @empty
        <div class="col-span-3 text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No projects available</h3>
            <p class="mt-1 text-sm text-gray-500">Check back soon for new community projects!</p>
        </div>
        @endforelse
    </div>

    <!-- View All Projects Link -->
    <div class="text-center">
        <a href="{{ route('register') }}" class="inline-flex items-center text-blue-600 hover:text-blue-500 font-medium">
            View All Projects & Join Community
            <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </a>
    </div>
</div>

<!-- Leaderboard Widget (if enabled) -->
@if(isset($showLeaderboard) && $showLeaderboard)
<div class="bg-white shadow-lg rounded-lg p-6 mb-8">
    <h3 class="text-xl font-bold text-gray-900 mb-4">🏆 Top Contributors</h3>
    <div class="space-y-3">
        @foreach($leaderboard ?? [] as $index => $leader)
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                    {{ $index + 1 }}
                </div>
                <div>
                    <p class="font-medium text-gray-900">{{ $leader['name'] }}</p>
                    <p class="text-sm text-gray-500">{{ $leader['contribution_type'] }}</p>
                </div>
            </div>
            <div class="text-right">
                <p class="font-bold text-green-600">${{ number_format($leader['amount']) }}</p>
                <p class="text-xs text-gray-500">contributed</p>
            </div>
        </div>
        @endforeach
    </div>
</div>
@endif

<!-- Stage Activation Pricing Widget -->
@if(isset($showStageActivation) && $showStageActivation)
<div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg p-6 text-white">
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold mb-2">🚀 Join Our Mission</h2>
        <p class="text-purple-100">Choose your activation level and start making a difference</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        @foreach($stageActivationPlans ?? [] as $plan)
        <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 text-center">
            <h3 class="text-lg font-bold mb-2">{{ $plan['name'] }}</h3>
            <div class="text-2xl font-bold mb-2">${{ $plan['price'] }}</div>
            <p class="text-sm text-purple-100 mb-4">{{ $plan['description'] }}</p>
            <ul class="text-sm text-purple-100 mb-4 space-y-1">
                @foreach($plan['features'] as $feature)
                <li>✓ {{ $feature }}</li>
                @endforeach
            </ul>
            <a href="{{ route('login') }}" class="w-full inline-flex items-center justify-center px-4 py-2 bg-white text-purple-600 font-medium rounded hover:bg-purple-50 transition-colors">
                Activate Now
            </a>
        </div>
        @endforeach
    </div>

    <div class="text-center mt-6">
        <p class="text-sm text-purple-100">
            <a href="{{ route('register') }}" class="underline hover:text-white">Create account</a> to access all features
        </p>
    </div>
</div>
@endif

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
