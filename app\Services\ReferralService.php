<?php

namespace App\Services;

use App\Models\User;
use App\Models\Referral;
use App\Models\MembershipStage;
use Illuminate\Support\Facades\DB;

class ReferralService
{
    /**
     * Process a new user registration with referral code.
     */
    public function processReferralRegistration(User $newUser, ?string $referralCode = null): bool
    {
        if (!$referralCode) {
            return true; // No referral code provided
        }

        $referrer = User::where('referral_code', $referralCode)->first();
        
        if (!$referrer) {
            return false; // Invalid referral code
        }

        if ($referrer->id === $newUser->id) {
            return false; // Cannot refer yourself
        }

        // Update the new user's referred_by field
        $newUser->referred_by = $referrer->id;
        $newUser->save();

        // Create referral relationship
        Referral::create([
            'referrer_id' => $referrer->id,
            'referred_id' => $newUser->id,
            'status' => 'pending',
        ]);

        return true;
    }

    /**
     * Activate a user and process referral rewards.
     */
    public function activateUser(User $user): bool
    {
        if ($user->is_active) {
            return true; // Already active
        }

        DB::transaction(function () use ($user) {
            // Activate the user
            $user->is_active = true;
            $user->activated_at = now();
            $user->save();

            // If this user was referred, activate the referral
            $referral = Referral::where('referred_id', $user->id)
                ->where('status', 'pending')
                ->first();

            if ($referral) {
                $referral->activate();
            }
        });

        return true;
    }

    /**
     * Get referral statistics for a user.
     */
    public function getReferralStats(User $user): array
    {
        $activeReferrals = $user->activeReferrals()->count();
        $totalReferrals = $user->referrals()->count();
        $pendingReferrals = $user->referrals()->where('is_active', false)->count();
        
        $totalCommissions = $user->commissions()->paid()->sum('amount');
        $pendingCommissions = $user->commissions()->pending()->sum('amount');
        
        $currentStage = null; // Multi-stage system - no single current stage
        $nextStage = null; // Multi-stage system - no single next stage
        
        $referralsToNextStage = $nextStage 
            ? max(0, $nextStage->min_referrals - $activeReferrals)
            : 0;

        return [
            'active_referrals' => $activeReferrals,
            'total_referrals' => $totalReferrals,
            'pending_referrals' => $pendingReferrals,
            'total_commissions' => $totalCommissions,
            'pending_commissions' => $pendingCommissions,
            'current_stage' => $currentStage,
            'next_stage' => $nextStage,
            'referrals_to_next_stage' => $referralsToNextStage,
            'can_upgrade' => $user->canUpgradeStage(),
        ];
    }

    /**
     * Get referral tree for a user (direct referrals with their stats).
     */
    public function getReferralTree(User $user, int $depth = 1): array
    {
        $referrals = $user->referrals()
            ->with(['referrals' => function ($query) use ($depth) {
                if ($depth > 1) {
                    $query->with('referrals');
                }
            }])
            ->get();

        return $referrals->map(function ($referral) use ($depth) {
            $data = [
                'id' => $referral->id,
                'name' => $referral->name,
                'email' => $referral->email,
                'membership_stage' => $referral->membership_stage,
                'is_active' => $referral->is_active,
                'activated_at' => $referral->activated_at,
                'total_referrals' => $referral->total_referrals,
                'total_earnings' => $referral->total_earnings,
            ];

            if ($depth > 1) {
                $data['sub_referrals'] = $this->getReferralTree($referral, $depth - 1);
            }

            return $data;
        })->toArray();
    }

    /**
     * Calculate potential earnings for a stage.
     */
    public function calculatePotentialEarnings(MembershipStage $stage, int $referralCount): float
    {
        return $stage->commission_rate * $referralCount + $stage->activation_bonus;
    }

    /**
     * Get all membership stages with progression info.
     */
    public function getMembershipStagesWithProgression(User $user): array
    {
        $stages = MembershipStage::active()->ordered()->get();
        $userReferrals = $user->total_active_referrals;

        return $stages->map(function ($stage) use ($user, $userReferrals) {
            return [
                'id' => $stage->id,
                'name' => $stage->name,
                'slug' => $stage->slug,
                'min_referrals' => $stage->min_referrals,
                'max_referrals' => $stage->max_referrals,
                'commission_rate' => $stage->commission_rate,
                'activation_bonus' => $stage->activation_bonus,
                'description' => $stage->description,
                'benefits' => $stage->benefits,
                'requirements_text' => $stage->requirements_text,
                'is_current' => $user->hasActivatedStage($stage->slug),
                'is_achieved' => $userReferrals >= $stage->min_referrals,
                'is_qualified' => $stage->userQualifies($user),
                'progress_percentage' => min(100, ($userReferrals / $stage->min_referrals) * 100),
            ];
        })->toArray();
    }

    /**
     * Validate referral code.
     */
    public function validateReferralCode(string $code): ?User
    {
        return User::where('referral_code', $code)
            ->where('is_active', true)
            ->first();
    }
}
