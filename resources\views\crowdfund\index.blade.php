@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Crowdfunding</h1>
                <p class="mt-2 text-gray-600">Support innovative projects and startups in our community</p>
            </div>
            @auth
            <a href="{{ route('crowdfund.create') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                </svg>
                Create Campaign
            </a>
            @endauth
        </div>

        <!-- Unified Navigation Menu -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4">

                <nav class="flex space-x-8 overflow-x-auto">
                    <a href="{{ route('community.index', ['tab' => 'projects']) }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 whitespace-nowrap">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                        </svg>
                        Projects
                    </a>
                    <a href="{{ route('community.index', ['tab' => 'crowdfund']) }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 whitespace-nowrap">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                        Crowdfund
                    </a>
                    <a href="{{ route('community.index', ['tab' => 'discussions']) }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 whitespace-nowrap">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                        </svg>
                        Discussions
                    </a>
                    <a href="{{ route('community.index', ['tab' => 'events']) }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 whitespace-nowrap">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        Events
                    </a>
                    <a href="{{ route('community.index', ['tab' => 'leaderboard']) }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 whitespace-nowrap">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        Leaderboard
                    </a>
                </nav>
            </div>
        </div>

        <!-- Crowdfund Sub Navigation -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4">
                <nav class="flex space-x-8 overflow-x-auto">
                    <a href="{{ route('crowdfund.index', ['tab' => 'campaigns']) }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md {{ $tab === 'campaigns' ? 'text-indigo-700 bg-indigo-100' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100' }} whitespace-nowrap">
                        All Campaigns
                    </a>
                    <a href="{{ route('crowdfund.index', ['tab' => 'active']) }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md {{ $tab === 'active' ? 'text-indigo-700 bg-indigo-100' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100' }} whitespace-nowrap">
                        Active
                    </a>
                    <a href="{{ route('crowdfund.index', ['tab' => 'completed']) }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md {{ $tab === 'completed' ? 'text-indigo-700 bg-indigo-100' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100' }} whitespace-nowrap">
                        Completed
                    </a>
                </nav>
            </div>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Campaigns</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['total_campaigns'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['active_campaigns'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Raised</dt>
                            <dd class="text-lg font-medium text-gray-900">${{ number_format($stats['total_raised'], 0) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Contributors</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['total_contributors'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Tabs -->
    <div class="bg-white shadow rounded-lg">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <a href="{{ route('crowdfund.index', ['tab' => $tab, 'type' => 'all']) }}"
                   class="py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap {{ $type === 'all' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    All Types
                </a>
                <a href="{{ route('crowdfund.index', ['tab' => $tab, 'type' => 'donation']) }}"
                   class="py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap {{ $type === 'donation' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    Donations
                </a>
                <a href="{{ route('crowdfund.index', ['tab' => $tab, 'type' => 'investment']) }}"
                   class="py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap {{ $type === 'investment' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    Investments
                </a>
                <a href="{{ route('crowdfund.index', ['tab' => $tab, 'type' => 'loan']) }}"
                   class="py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap {{ $type === 'loan' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    Loans
                </a>
            </nav>
        </div>
    </div>

    <!-- Campaigns Grid -->
    @if($campaigns->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
        @foreach($campaigns as $campaign)
        <div class="bg-white shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200">
            <!-- Campaign Image -->
            @if($campaign->images && count($campaign->images) > 0)
            <div class="h-48 bg-gray-200 overflow-hidden">
                <img src="{{ Storage::url($campaign->images[0]) }}" 
                     alt="{{ $campaign->title }}" 
                     class="w-full h-full object-cover">
            </div>
            @else
            <div class="h-48 bg-gradient-to-r from-indigo-400 to-purple-500 flex items-center justify-center">
                <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            @endif

            <div class="p-6">
                <!-- Campaign Header -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="{{ route('crowdfund.show', $campaign) }}" class="hover:text-indigo-600">
                                {{ $campaign->title }}
                            </a>
                        </h3>
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                {{ ucfirst($campaign->campaign_type) }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{ $campaign->category }}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">{{ Str::limit($campaign->description, 100) }}</p>
                    </div>
                </div>

                <!-- Campaign Progress -->
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm mb-2">
                        <span class="font-medium text-gray-700">${{ number_format($campaign->raised_amount) }} raised</span>
                        <span class="text-gray-500">{{ $campaign->days_remaining }} days left</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-indigo-600 h-2 rounded-full" style="width: {{ $campaign->progress_percentage }}%"></div>
                    </div>
                    <div class="flex items-center justify-between text-xs text-gray-500 mt-1">
                        <span>Goal: ${{ number_format($campaign->target_amount) }}</span>
                        <span>{{ $campaign->contributors_count }} contributors</span>
                    </div>
                </div>

                <!-- Campaign Footer -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8">
                            <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-xs font-medium text-gray-700">{{ substr($campaign->user->name, 0, 2) }}</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900">{{ $campaign->user->name }}</p>
                            <p class="text-xs text-gray-500">{{ $campaign->created_at->diffForHumans() }}</p>
                        </div>
                    </div>
                    <a href="{{ route('crowdfund.show', $campaign) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                        View Details
                    </a>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="mt-8">
        {{ $campaigns->appends(request()->query())->links() }}
    </div>
    @else
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No campaigns found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new crowdfunding campaign.</p>
        @auth
        <div class="mt-6">
            <a href="{{ route('crowdfund.create') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                </svg>
                Create Campaign
            </a>
        </div>
        @endauth
    </div>
    @endif
</div>
@endsection
