<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\ReferralService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class RegisterController extends Controller
{
    protected $redirectTo = '/dashboard';
    protected $referralService;

    public function __construct(ReferralService $referralService)
    {
        $this->referralService = $referralService;
    }

    /**
     * Show the registration form.
     */
    public function showRegistrationForm(Request $request)
    {
        $referralCode = $request->get('ref');
        $referrer = null;
        
        if ($referralCode) {
            $referrer = $this->referralService->validateReferralCode($referralCode);
        }

        return view('auth.register', compact('referralCode', 'referrer'));
    }

    /**
     * Get a validator for an incoming registration request.
     */
    protected function validator(array $data)
    {
        $rules = [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'address' => ['nullable', 'string', 'max:255'],
            'city' => ['nullable', 'string', 'max:100'],
            'state' => ['nullable', 'string', 'max:100'],
            'country' => ['required', 'string', 'max:3'],
        ];

        // Add referral code validation if provided
        if (!empty($data['referral_code'])) {
            $rules['referral_code'] = ['string', 'max:10', 'exists:users,referral_code'];
        }

        return Validator::make($data, $rules);
    }

    /**
     * Create a new user instance after a valid registration.
     */
    protected function create(array $data)
    {
        $user = User::create([
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'name' => $data['first_name'] . ' ' . $data['last_name'], // Keep for backward compatibility
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'country' => $data['country'],
            'membership_tier' => 'earthfriendly',
            'membership_stage' => null,
            'is_active' => true,
            'activated_at' => now(),
        ]);

        // Process referral if referral code was provided
        if (!empty($data['referral_code'])) {
            $this->referralService->processReferralRegistration($user, $data['referral_code']);
        } else {
            // If no referral code provided, automatically assign Top Admin as referrer
            $topAdmin = User::where('is_top_admin', true)->first();
            if ($topAdmin && $topAdmin->id !== $user->id) {
                $this->referralService->processReferralRegistration($user, $topAdmin->referral_code);
            }
        }

        // Activate user immediately (you might want to add email verification)
        $this->referralService->activateUser($user);

        return $user;
    }

    /**
     * Handle a registration request for the application.
     */
    public function register(Request $request)
    {
        $this->validator($request->all())->validate();

        $user = $this->create($request->all());

        Auth::login($user);

        // Send email verification notification
        $user->sendEmailVerificationNotification();

        // Check if email verification is required
        if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && !$user->hasVerifiedEmail()) {
            return redirect()->route('verification.notice')
                ->with('success', 'Registration successful! Please check your email to verify your account.');
        }

        return redirect($this->redirectTo)
            ->with('success', 'Registration successful! Welcome to your dashboard.');
    }

    /**
     * The user has been registered.
     */
    protected function registered(Request $request, $user)
    {
        // You can add additional logic here after successful registration
        return null;
    }
}
