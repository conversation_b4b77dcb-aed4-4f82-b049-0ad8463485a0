<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAchievement extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'achievement_id',
        'achieved_at',
        'achievement_data',
        'completion_count',
    ];

    protected $casts = [
        'achieved_at' => 'datetime',
        'achievement_data' => 'array',
    ];

    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the achievement.
     */
    public function achievement()
    {
        return $this->belongsTo(Achievement::class);
    }

    /**
     * Check if user is millionaire.
     */
    public static function checkMillionaireAchievement($userId)
    {
        $user = User::find($userId);
        if (!$user) return false;

        $totalEarnings = $user->total_earnings + $user->available_balance + $user->pending_balance;

        if ($totalEarnings >= 1000000) {
            $achievement = Achievement::where('slug', 'millionaire')->first();
            if ($achievement && !$achievement->hasUserAchieved($userId)) {
                self::create([
                    'user_id' => $userId,
                    'achievement_id' => $achievement->id,
                    'achieved_at' => now(),
                    'achievement_data' => [
                        'total_earnings' => $totalEarnings,
                        'achieved_amount' => 1000000,
                    ],
                ]);

                // Award the achievement rewards
                if ($achievement->reward_amount) {
                    $user->increment('available_balance', $achievement->reward_amount);
                }
                if ($achievement->reward_points) {
                    $user->increment('reward_points', $achievement->reward_points);
                }

                return true;
            }
        }

        return false;
    }
}
