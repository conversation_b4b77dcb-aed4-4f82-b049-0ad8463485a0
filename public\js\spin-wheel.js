// Spin Wheel JavaScript

let isSpinning = false;

// Spin function
function spin(type) {
    if (isSpinning) return;
    
    isSpinning = true;
    
    // Disable buttons during spin
    const buttons = document.querySelectorAll('button[onclick*="spin"]');
    buttons.forEach(btn => btn.disabled = true);
    
    // Get CSRF token
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    
    // Show spinning animation
    const wheel = document.getElementById('spinWheel');
    const randomRotation = Math.floor(Math.random() * 360) + 1440; // At least 4 full rotations
    
    wheel.style.transform = `rotate(${randomRotation}deg)`;
    
    // Send spin request to server
    fetch('/wallet/spin', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': token
        },
        body: JSON.stringify({
            spin_type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        // Wait for animation to complete
        setTimeout(() => {
            showResult(data);
            isSpinning = false;
            
            // Re-enable appropriate buttons
            if (data.success) {
                // Reload page to update status
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } else {
                buttons.forEach(btn => btn.disabled = false);
            }
        }, 3000);
    })
    .catch(error => {
        console.error('Error:', error);
        isSpinning = false;
        buttons.forEach(btn => btn.disabled = false);
        showError('An error occurred. Please try again.');
    });
}

// Show spin result
function showResult(data) {
    const modal = document.getElementById('spinResultModal');
    const icon = document.getElementById('resultIcon');
    const title = document.getElementById('resultTitle');
    const message = document.getElementById('resultMessage');
    
    if (data.success) {
        if (data.is_winner) {
            icon.textContent = '🎉';
            title.textContent = 'Congratulations!';
            message.innerHTML = `
                <p class="font-semibold text-green-600">You won: ${data.prize_name}!</p>
                <p class="text-sm mt-2">${data.prize_description}</p>
                <p class="text-xs text-gray-500 mt-2">Prize delivery: ${data.delivery_method}</p>
            `;
        } else {
            icon.textContent = '😔';
            title.textContent = 'Better luck next time!';
            message.innerHTML = `
                <p>No prize this time, but keep spinning!</p>
                <p class="text-sm mt-2">Spins until next guaranteed prize: ${data.spins_to_win}</p>
            `;
        }
    } else {
        icon.textContent = '❌';
        title.textContent = 'Spin Failed';
        message.textContent = data.message || 'Something went wrong. Please try again.';
    }
    
    modal.classList.remove('hidden');
}

// Show error message
function showError(message) {
    const modal = document.getElementById('spinResultModal');
    const icon = document.getElementById('resultIcon');
    const title = document.getElementById('resultTitle');
    const messageEl = document.getElementById('resultMessage');
    
    icon.textContent = '❌';
    title.textContent = 'Error';
    messageEl.textContent = message;
    
    modal.classList.remove('hidden');
}

// Close modal
function closeModal() {
    const modal = document.getElementById('spinResultModal');
    modal.classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('spinResultModal');
    if (event.target === modal) {
        closeModal();
    }
});

// Escape key to close modal
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
    }
});

// Initialize wheel segments with prizes
document.addEventListener('DOMContentLoaded', function() {
    const wheel = document.getElementById('spinWheel');
    if (wheel) {
        // Create wheel segments with prize labels
        const segments = [
            { color: '#ff6b6b', label: '🎁', prize: 'Gift Card' },
            { color: '#4ecdc4', label: '💰', prize: 'Cash Prize' },
            { color: '#45b7d1', label: '🏆', prize: 'Trophy' },
            { color: '#96ceb4', label: '🎫', prize: 'Event Ticket' },
            { color: '#ffeaa7', label: '💎', prize: 'Points' },
            { color: '#dda0dd', label: '🎪', prize: 'Experience' },
            { color: '#98d8c8', label: '🛍️', prize: 'Shopping' },
            { color: '#f7dc6f', label: '🍕', prize: 'Food Voucher' }
        ];
        
        // Clear existing content
        wheel.innerHTML = '';
        
        // Create segments
        segments.forEach((segment, index) => {
            const segmentDiv = document.createElement('div');
            segmentDiv.className = 'absolute inset-0 flex items-center justify-center';
            segmentDiv.style.transform = `rotate(${index * 45}deg)`;
            segmentDiv.style.transformOrigin = 'center';
            
            const label = document.createElement('div');
            label.className = 'text-white font-bold text-lg';
            label.style.transform = `translateY(-80px) rotate(22.5deg)`;
            label.textContent = segment.label;
            
            segmentDiv.appendChild(label);
            wheel.appendChild(segmentDiv);
        });
        
        // Add center circle
        const center = document.createElement('div');
        center.className = 'absolute inset-0 flex items-center justify-center';
        center.innerHTML = '<div class="w-16 h-16 bg-white rounded-full flex items-center justify-center text-gray-800 font-bold text-sm border-4 border-gray-300">SPIN</div>';
        wheel.appendChild(center);
    }
});

// Add some visual feedback for buttons
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('button[onclick*="spin"]');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            if (!this.disabled && !isSpinning) {
                this.style.transform = 'scale(1.05)';
            }
        });
        
        button.addEventListener('mouseleave', function() {
            if (!this.disabled && !isSpinning) {
                this.style.transform = 'scale(1)';
            }
        });
    });
});
