@extends('layouts.admin')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Stage</h1>
                <p class="mt-2 text-gray-600">Modify stage details, pricing, and benefits</p>
            </div>
            <a href="{{ route('admin.stages.show', $stage) }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Stage
            </a>
        </div>
    </div>

    <form action="{{ route('admin.stages.update', $stage) }}" method="POST" class="space-y-8">
        @csrf
        @method('PUT')

        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Stage Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Stage Name</label>
                    <input type="text" name="name" id="name" required
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                           value="{{ old('name', $stage->name) }}">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Sort Order -->
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700">Sort Order</label>
                    <input type="number" name="sort_order" id="sort_order" required min="0"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                           value="{{ old('sort_order', $stage->sort_order) }}">
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Color -->
                <div>
                    <label for="color" class="block text-sm font-medium text-gray-700">Color</label>
                    <input type="color" name="color" id="color"
                           class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                           value="{{ old('color', $stage->color ?? '#6366f1') }}">
                    @error('color')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Icon -->
                <div>
                    <label for="icon" class="block text-sm font-medium text-gray-700">Icon (Heroicon name)</label>
                    <input type="text" name="icon" id="icon"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                           value="{{ old('icon', $stage->icon) }}"
                           placeholder="e.g., star, heart, lightning-bolt">
                    @error('icon')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                <textarea name="description" id="description" rows="4" required
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">{{ old('description', $stage->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Status -->
            <div class="mt-6">
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" id="is_active" value="1"
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                           {{ old('is_active', $stage->is_active) ? 'checked' : '' }}>
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Stage is active and available for activation
                    </label>
                </div>
            </div>
        </div>

        <!-- Pricing Configuration -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Pricing Configuration</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Pricing Amount -->
                <div>
                    <label for="pricing_amount" class="block text-sm font-medium text-gray-700">Pricing Amount ($)</label>
                    <input type="number" name="pricing_amount" id="pricing_amount" required min="0" step="0.01"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                           value="{{ old('pricing_amount', $stage->pricing_amount ?? 0) }}">
                    @error('pricing_amount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Referral Requirement Min -->
                <div>
                    <label for="referral_requirement_min" class="block text-sm font-medium text-gray-700">Min Referrals Required</label>
                    <input type="number" name="referral_requirement_min" id="referral_requirement_min" required min="0"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                           value="{{ old('referral_requirement_min', explode('-', str_replace(' referrals', '', $stage->referral_requirement ?? '0'))[0]) }}">
                    @error('referral_requirement_min')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Referral Requirement Max -->
                <div>
                    <label for="referral_requirement_max" class="block text-sm font-medium text-gray-700">Max Referrals Required</label>
                    <input type="number" name="referral_requirement_max" id="referral_requirement_max" required min="0"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                           value="{{ old('referral_requirement_max', explode('-', str_replace(' referrals', '', $stage->referral_requirement ?? '0'))[1] ?? explode('-', str_replace(' referrals', '', $stage->referral_requirement ?? '0'))[0]) }}">
                    @error('referral_requirement_max')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-4 p-4 bg-blue-50 rounded-md">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>Pricing Amount:</strong> The cost users pay to activate this stage.<br>
                            <strong>Referral Requirements:</strong> Set min and max to the same value for exact requirement (e.g., "5 referrals"), or different values for a range (e.g., "3-7 referrals").
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Benefits Configuration -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Stage Benefits</h3>
            
            <div id="benefits-container">
                @php
                    $benefits = json_decode($stage->benefits ?? '[]', true);
                    if (empty($benefits)) $benefits = [''];
                @endphp
                
                @foreach($benefits as $index => $benefit)
                <div class="benefit-item flex items-center space-x-3 mb-3">
                    <input type="text" name="benefits[]" 
                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                           value="{{ old('benefits.' . $index, $benefit) }}"
                           placeholder="Enter stage benefit">
                    <button type="button" onclick="removeBenefit(this)"
                            class="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
                        Remove
                    </button>
                </div>
                @endforeach
            </div>

            <button type="button" onclick="addBenefit()"
                    class="mt-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                </svg>
                Add Benefit
            </button>
        </div>

        <!-- Submit Button -->
        <div class="flex items-center justify-end space-x-3">
            <a href="{{ route('admin.stages.show', $stage) }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Cancel
            </a>
            <button type="submit"
                    class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z"></path>
                </svg>
                Update Stage
            </button>
        </div>
    </form>
</div>

<script>
function addBenefit() {
    const container = document.getElementById('benefits-container');
    const div = document.createElement('div');
    div.className = 'benefit-item flex items-center space-x-3 mb-3';
    div.innerHTML = `
        <input type="text" name="benefits[]" 
               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
               placeholder="Enter stage benefit">
        <button type="button" onclick="removeBenefit(this)"
                class="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
            Remove
        </button>
    `;
    container.appendChild(div);
}

function removeBenefit(button) {
    const container = document.getElementById('benefits-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

// Sync referral requirement fields
document.getElementById('referral_requirement_min').addEventListener('input', function() {
    const max = document.getElementById('referral_requirement_max');
    if (parseInt(max.value) < parseInt(this.value)) {
        max.value = this.value;
    }
});
</script>
@endsection
