@extends('layouts.app')

@section('title', 'Pay Invoice')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Pay Invoice</h1>
                <p class="text-gray-600 mt-1">Enter your invoice details to make a payment</p>
            </div>
            <a href="{{ route('wallet.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L4.414 9H17a1 1 0 110 2H4.414l5.293 5.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Wallet
            </a>
        </div>
    </div>

    <!-- Payment Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="POST" action="{{ route('wallet.process-pay') }}">
            @csrf
            
            <!-- Invoice Number -->
            <div class="mb-6">
                <label for="invoice_number" class="block text-sm font-medium text-gray-700 mb-2">
                    Invoice Number or Tracking Code
                </label>
                <input type="text" 
                       id="invoice_number" 
                       name="invoice_number" 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                       placeholder="Enter invoice number or tracking code"
                       required>
                @error('invoice_number')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Payment Method -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label class="relative">
                        <input type="radio" name="payment_method" value="stripe" class="sr-only" required>
                        <div class="border-2 border-gray-300 rounded-lg p-4 cursor-pointer hover:border-indigo-500 transition-colors">
                            <div class="flex items-center justify-center mb-2">
                                <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z"/>
                                </svg>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-gray-900">Credit Card</div>
                                <div class="text-sm text-gray-500">Visa, Mastercard, etc.</div>
                            </div>
                        </div>
                    </label>

                    <label class="relative">
                        <input type="radio" name="payment_method" value="paypal" class="sr-only">
                        <div class="border-2 border-gray-300 rounded-lg p-4 cursor-pointer hover:border-indigo-500 transition-colors">
                            <div class="flex items-center justify-center mb-2">
                                <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 7.201-9.138 7.201h-2.19a.563.563 0 0 0-.556.479l-1.187 7.527h-.506l-.506 3.218a.641.641 0 0 0 .633.74h4.25c.445 0 .823-.308.923-.728l.04-.207 1.122-7.108.072-.387c.1-.42.478-.728.923-.728h.58c3.606 0 6.43-1.466 7.25-5.704.343-1.77.133-3.257-.693-4.416z"/>
                                </svg>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-gray-900">PayPal</div>
                                <div class="text-sm text-gray-500">Secure online payment</div>
                            </div>
                        </div>
                    </label>

                    <label class="relative">
                        <input type="radio" name="payment_method" value="bank_transfer" class="sr-only">
                        <div class="border-2 border-gray-300 rounded-lg p-4 cursor-pointer hover:border-indigo-500 transition-colors">
                            <div class="flex items-center justify-center mb-2">
                                <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-gray-900">Bank Transfer</div>
                                <div class="text-sm text-gray-500">Manual approval required</div>
                            </div>
                        </div>
                    </label>
                </div>
                @error('payment_method')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Invoice Lookup -->
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="text-sm font-medium text-blue-900 mb-2">Don't have an invoice?</h3>
                <p class="text-sm text-blue-700 mb-3">
                    Invoices are automatically generated when you:
                </p>
                <ul class="text-sm text-blue-700 space-y-1 ml-4">
                    <li>• Activate a new stage</li>
                    <li>• Purchase marketplace items</li>
                    <li>• Subscribe to premium features</li>
                    <li>• Make any platform purchase</li>
                </ul>
                <p class="text-sm text-blue-700 mt-3">
                    Check your email for invoice details or contact support if you need assistance.
                </p>
            </div>

            <!-- Submit Button -->
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                    Secure payment processing
                </div>
                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
                    </svg>
                    Process Payment
                </button>
            </div>
        </form>
    </div>

    <!-- Recent Invoices -->
    <div class="mt-8 bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Invoices</h3>
        <div class="space-y-3">
            <!-- Sample Invoice -->
            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                    <div class="font-medium text-gray-900">Stage Activation - Light Member</div>
                    <div class="text-sm text-gray-500">Invoice #INV-2024-001 • Dec 15, 2024</div>
                    <div class="text-sm text-gray-500">Tracking: TRK-ABC123XYZ</div>
                </div>
                <div class="text-right">
                    <div class="font-bold text-gray-900">$29.00</div>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Paid
                    </span>
                </div>
            </div>

            <!-- Sample Pending Invoice -->
            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                    <div class="font-medium text-gray-900">Marketplace Purchase</div>
                    <div class="text-sm text-gray-500">Invoice #INV-2024-002 • Dec 18, 2024</div>
                    <div class="text-sm text-gray-500">Tracking: TRK-DEF456ABC</div>
                </div>
                <div class="text-right">
                    <div class="font-bold text-gray-900">$15.99</div>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle payment method selection
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            // Remove selected class from all
            document.querySelectorAll('input[name="payment_method"]').forEach(input => {
                input.closest('label').querySelector('div').classList.remove('border-indigo-500', 'bg-indigo-50');
                input.closest('label').querySelector('div').classList.add('border-gray-300');
            });
            
            // Add selected class to current
            if (this.checked) {
                this.closest('label').querySelector('div').classList.remove('border-gray-300');
                this.closest('label').querySelector('div').classList.add('border-indigo-500', 'bg-indigo-50');
            }
        });
    });
});
</script>
@endsection
