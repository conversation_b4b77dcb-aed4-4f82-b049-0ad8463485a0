<?php

namespace App\Services;

use App\Models\User;
use App\Models\MembershipStage;
use App\Models\UserStageActivation;
use App\Models\Commission;
use Carbon\Carbon;

class StageAnalyticsService
{
    /**
     * Get comprehensive analytics for a user's stage.
     */
    public function getStageAnalytics(User $user, string $stageSlug): array
    {
        $stage = MembershipStage::where('slug', $stageSlug)->first();
        $activation = $user->getStageActivation($stageSlug);

        if (!$stage || !$activation) {
            return [];
        }

        return [
            'overview' => $this->getOverviewMetrics($user, $stage, $activation),
            'earnings' => $this->getEarningsAnalytics($user, $stage, $activation),
            'progress' => $this->getProgressMetrics($user, $stage, $activation),
            'charts' => $this->getChartData($user, $stage, $activation),
            'goals' => $this->getStageGoals($stage),
            'insights' => $this->getPersonalizedInsights($user, $stage, $activation),
        ];
    }

    /**
     * Get overview metrics for the stage.
     */
    private function getOverviewMetrics(User $user, MembershipStage $stage, UserStageActivation $activation): array
    {
        $daysActive = $activation->activated_at->diffInDays(now());
        $commissions = $activation->commissions();

        return [
            'days_active' => $daysActive,
            'total_earned' => $commissions->sum('amount') + $activation->activation_bonus_paid,
            'commission_earned' => $commissions->sum('amount'),
            'activation_bonus' => $activation->activation_bonus_paid,
            'referrals_count' => $commissions->count(),
            'average_daily_earnings' => $daysActive > 0 ? ($commissions->sum('amount') / $daysActive) : 0,
            'commission_rate' => $stage->commission_rate,
            'stage_rank' => $this->getStageRank($user, $stage),
        ];
    }

    /**
     * Get earnings analytics with trends.
     */
    private function getEarningsAnalytics(User $user, MembershipStage $stage, UserStageActivation $activation): array
    {
        $commissions = $activation->commissions();
        $thisMonth = $commissions->whereMonth('created_at', now()->month)->sum('amount');
        $lastMonth = $commissions->whereMonth('created_at', now()->subMonth()->month)->sum('amount');
        $thisWeek = $commissions->where('created_at', '>=', now()->startOfWeek())->sum('amount');
        $lastWeek = $commissions->whereBetween('created_at', [
            now()->subWeek()->startOfWeek(),
            now()->subWeek()->endOfWeek()
        ])->sum('amount');

        return [
            'this_month' => $thisMonth,
            'last_month' => $lastMonth,
            'month_growth' => $lastMonth > 0 ? (($thisMonth - $lastMonth) / $lastMonth) * 100 : 0,
            'this_week' => $thisWeek,
            'last_week' => $lastWeek,
            'week_growth' => $lastWeek > 0 ? (($thisWeek - $lastWeek) / $lastWeek) * 100 : 0,
            'best_month' => $this->getBestMonth($activation),
            'earning_streak' => $this->getEarningStreak($activation),
        ];
    }

    /**
     * Get progress metrics specific to the stage.
     */
    private function getProgressMetrics(User $user, MembershipStage $stage, UserStageActivation $activation): array
    {
        $stageGoals = $this->getStageGoals($stage);
        $currentMetrics = $this->getCurrentMetrics($user, $activation);

        $progress = [];
        foreach ($stageGoals as $goal) {
            $current = $currentMetrics[$goal['metric']] ?? 0;
            $target = $goal['target'];
            $progress[] = [
                'name' => $goal['name'],
                'current' => $current,
                'target' => $target,
                'percentage' => min(100, ($current / $target) * 100),
                'status' => $current >= $target ? 'completed' : 'in_progress',
            ];
        }

        return $progress;
    }

    /**
     * Get chart data for visualizations.
     */
    private function getChartData(User $user, MembershipStage $stage, UserStageActivation $activation): array
    {
        return [
            'earnings_trend' => $this->getEarningsTrendData($activation),
            'referral_activity' => $this->getReferralActivityData($activation),
            'stage_comparison' => $this->getStageComparisonData($user),
            'progress_over_time' => $this->getProgressOverTimeData($activation),
        ];
    }

    /**
     * Get stage-specific goals based on life journey stage.
     */
    private function getStageGoals(MembershipStage $stage): array
    {
        $goals = [
            'starter' => [
                ['name' => 'First Referrals', 'metric' => 'referrals', 'target' => 5],
                ['name' => 'Foundation Building', 'metric' => 'days_active', 'target' => 30],
                ['name' => 'Initial Earnings', 'metric' => 'total_earned', 'target' => 100],
            ],
            'development' => [
                ['name' => 'Skill Development', 'metric' => 'referrals', 'target' => 15],
                ['name' => 'Consistent Activity', 'metric' => 'days_active', 'target' => 60],
                ['name' => 'Growing Income', 'metric' => 'total_earned', 'target' => 500],
            ],
            'action' => [
                ['name' => 'Active Implementation', 'metric' => 'referrals', 'target' => 30],
                ['name' => 'Sustained Effort', 'metric' => 'days_active', 'target' => 90],
                ['name' => 'Meaningful Results', 'metric' => 'total_earned', 'target' => 1500],
            ],
            'management' => [
                ['name' => 'System Management', 'metric' => 'referrals', 'target' => 50],
                ['name' => 'Long-term Commitment', 'metric' => 'days_active', 'target' => 180],
                ['name' => 'Substantial Income', 'metric' => 'total_earned', 'target' => 5000],
            ],
            'abundance' => [
                ['name' => 'Abundant Network', 'metric' => 'referrals', 'target' => 100],
                ['name' => 'Mastery Period', 'metric' => 'days_active', 'target' => 365],
                ['name' => 'Abundant Earnings', 'metric' => 'total_earned', 'target' => 15000],
            ],
            'retirement' => [
                ['name' => 'Legacy Building', 'metric' => 'referrals', 'target' => 200],
                ['name' => 'Lifetime Journey', 'metric' => 'days_active', 'target' => 730],
                ['name' => 'Retirement Income', 'metric' => 'total_earned', 'target' => 50000],
            ],
        ];

        return $goals[$stage->slug] ?? [];
    }

    /**
     * Get personalized insights based on user's progress.
     */
    private function getPersonalizedInsights(User $user, MembershipStage $stage, UserStageActivation $activation): array
    {
        $insights = [];
        $metrics = $this->getCurrentMetrics($user, $activation);
        $goals = $this->getStageGoals($stage);

        // Performance insights
        if ($metrics['average_daily_earnings'] > $stage->commission_rate) {
            $insights[] = [
                'type' => 'positive',
                'title' => 'Excellent Performance',
                'message' => 'Your daily earnings exceed the stage commission rate. You\'re building momentum!',
            ];
        }

        // Goal progress insights
        foreach ($goals as $goal) {
            $current = $metrics[$goal['metric']] ?? 0;
            $progress = ($current / $goal['target']) * 100;
            
            if ($progress >= 80) {
                $insights[] = [
                    'type' => 'success',
                    'title' => 'Goal Almost Achieved',
                    'message' => "You're {$progress}% towards your {$goal['name']} goal. Keep going!",
                ];
            } elseif ($progress < 25) {
                $insights[] = [
                    'type' => 'suggestion',
                    'title' => 'Focus Opportunity',
                    'message' => "Consider focusing on {$goal['name']} to accelerate your progress.",
                ];
            }
        }

        // Stage-specific insights
        $stageInsights = $this->getStageSpecificInsights($stage, $metrics);
        $insights = array_merge($insights, $stageInsights);

        return array_slice($insights, 0, 5); // Limit to 5 insights
    }

    /**
     * Get stage-specific insights based on life journey.
     */
    private function getStageSpecificInsights(MembershipStage $stage, array $metrics): array
    {
        $insights = [];

        switch ($stage->slug) {
            case 'starter':
                if ($metrics['days_active'] > 14 && $metrics['referrals'] == 0) {
                    $insights[] = [
                        'type' => 'tip',
                        'title' => 'Ready for First Referral',
                        'message' => 'You\'ve been active for 2 weeks. Consider reaching out to friends about sustainable living.',
                    ];
                }
                break;

            case 'development':
                if ($metrics['referrals'] >= 10) {
                    $insights[] = [
                        'type' => 'positive',
                        'title' => 'Development Milestone',
                        'message' => 'Great progress! You\'re developing a solid foundation for sustainable living.',
                    ];
                }
                break;

            case 'action':
                if ($metrics['average_daily_earnings'] > 5) {
                    $insights[] = [
                        'type' => 'success',
                        'title' => 'Action Paying Off',
                        'message' => 'Your active approach is generating consistent daily earnings.',
                    ];
                }
                break;

            case 'management':
                if ($metrics['referrals'] >= 40) {
                    $insights[] = [
                        'type' => 'positive',
                        'title' => 'Management Excellence',
                        'message' => 'You\'re successfully managing a substantial network.',
                    ];
                }
                break;

            case 'abundance':
                if ($metrics['total_earned'] >= 10000) {
                    $insights[] = [
                        'type' => 'success',
                        'title' => 'Living in Abundance',
                        'message' => 'You\'ve achieved significant financial abundance through sustainable living.',
                    ];
                }
                break;

            case 'retirement':
                $insights[] = [
                    'type' => 'celebration',
                    'title' => 'Retirement Stage',
                    'message' => 'Congratulations on reaching the retirement stage of your sustainable living journey!',
                ];
                break;
        }

        return $insights;
    }

    /**
     * Helper methods for data calculations.
     */
    private function getCurrentMetrics(User $user, UserStageActivation $activation): array
    {
        $commissions = $activation->commissions();
        $daysActive = $activation->activated_at->diffInDays(now());

        return [
            'referrals' => $commissions->count(),
            'days_active' => $daysActive,
            'total_earned' => $commissions->sum('amount') + $activation->activation_bonus_paid,
            'average_daily_earnings' => $daysActive > 0 ? ($commissions->sum('amount') / $daysActive) : 0,
        ];
    }

    private function getEarningsTrendData(UserStageActivation $activation): array
    {
        $commissions = $activation->commissions()
            ->selectRaw("date(created_at) as date, SUM(amount) as daily_total")
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        if ($commissions->isEmpty()) {
            return [];
        }

        return $commissions->map(function ($item) {
            return [
                'date' => $item->date,
                'amount' => (float) $item->daily_total,
            ];
        })->toArray();
    }

    private function getReferralActivityData(UserStageActivation $activation): array
    {
        $commissions = $activation->commissions()
            ->selectRaw("strftime('%W', created_at) as week, COUNT(*) as referral_count")
            ->groupBy('week')
            ->orderBy('week')
            ->get();

        return $commissions->map(function ($item) {
            return [
                'week' => $item->week,
                'count' => $item->referral_count,
            ];
        })->toArray();
    }

    private function getStageComparisonData(User $user): array
    {
        return $user->activeStageActivations()->get()->map(function ($activation) {
            return [
                'stage' => $activation->membershipStage->name,
                'earnings' => $activation->commissions()->sum('amount'),
                'referrals' => $activation->commissions()->count(),
            ];
        })->toArray();
    }

    private function getProgressOverTimeData(UserStageActivation $activation): array
    {
        $commissions = $activation->commissions()
            ->selectRaw("date(created_at) as date, COUNT(*) as cumulative_referrals")
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $cumulative = 0;
        return $commissions->map(function ($item) use (&$cumulative) {
            $cumulative += $item->cumulative_referrals;
            return [
                'date' => $item->date,
                'cumulative_referrals' => $cumulative,
            ];
        })->toArray();
    }

    private function getBestMonth(UserStageActivation $activation): array
    {
        $bestMonth = $activation->commissions()
            ->selectRaw("strftime('%Y', created_at) as year, strftime('%m', created_at) as month, SUM(amount) as total")
            ->groupBy('year', 'month')
            ->orderBy('total', 'desc')
            ->first();

        if (!$bestMonth || !$bestMonth->total) {
            return ['month' => null, 'year' => null, 'amount' => 0];
        }

        return [
            'month' => (int) $bestMonth->month,
            'year' => (int) $bestMonth->year,
            'amount' => (float) $bestMonth->total,
        ];
    }

    private function getEarningStreak(UserStageActivation $activation): int
    {
        $recentDays = $activation->commissions()
            ->selectRaw('DATE(created_at) as date')
            ->distinct()
            ->orderBy('date', 'desc')
            ->take(30)
            ->pluck('date')
            ->toArray();

        $streak = 0;
        $currentDate = now()->format('Y-m-d');

        foreach ($recentDays as $date) {
            if ($date === $currentDate) {
                $streak++;
                $currentDate = Carbon::parse($currentDate)->subDay()->format('Y-m-d');
            } else {
                break;
            }
        }

        return $streak;
    }

    private function getStageRank(User $user, MembershipStage $stage): int
    {
        $userActivation = $user->getStageActivation($stage->slug);
        if (!$userActivation) {
            return 1;
        }

        $userEarnings = $userActivation->commissions()->sum('amount');

        // Get all activations for this stage with their earnings
        $stageActivations = UserStageActivation::where('membership_stage_id', $stage->id)
            ->where('is_active', true)
            ->with('commissions')
            ->get();

        $betterPerformers = 0;
        foreach ($stageActivations as $activation) {
            $activationEarnings = $activation->commissions->sum('amount');
            if ($activationEarnings > $userEarnings) {
                $betterPerformers++;
            }
        }

        return $betterPerformers + 1;
    }
}
