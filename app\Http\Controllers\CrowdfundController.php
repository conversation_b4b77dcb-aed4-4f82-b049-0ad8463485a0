<?php

namespace App\Http\Controllers;

use App\Models\CrowdfundCampaign;
use App\Models\CrowdfundContribution;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CrowdfundController extends Controller
{
    public function index(Request $request)
    {
        $tab = $request->get('tab', 'campaigns');
        $type = $request->get('type', 'all');
        
        $campaigns = CrowdfundCampaign::with(['user', 'contributions'])
            ->when($type !== 'all', function ($query) use ($type) {
                return $query->where('campaign_type', $type);
            })
            ->when($tab === 'active', function ($query) {
                return $query->where('status', 'active');
            })
            ->when($tab === 'completed', function ($query) {
                return $query->where('status', 'completed');
            })
            ->latest()
            ->paginate(12);

        $stats = [
            'total_campaigns' => CrowdfundCampaign::count(),
            'active_campaigns' => CrowdfundCampaign::where('status', 'active')->count(),
            'total_raised' => CrowdfundCampaign::sum('raised_amount'),
            'total_contributors' => CrowdfundContribution::distinct('user_id')->count(),
        ];

        return view('crowdfund.index', compact('campaigns', 'stats', 'tab', 'type'));
    }

    public function create()
    {
        // Check if user has activated action stage
        $user = Auth::user();
        $hasActionStage = $user->stageActivations()
            ->whereHas('membershipStage', function ($query) {
                $query->where('name', 'like', '%action%');
            })
            ->where('approval_status', 'approved')
            ->exists();

        if (!$hasActionStage) {
            return redirect()->route('crowdfund.index')
                ->with('error', 'You need to activate an Action stage to create crowdfunding campaigns.');
        }

        return view('crowdfund.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string',
            'target_amount' => 'required|numeric|min:1',
            'campaign_type' => 'required|in:donation,investment,loan',
            'profit_percentage' => 'nullable|numeric|min:0|max:100',
            'duration_days' => 'required|integer|min:1|max:365',
            'images.*' => 'nullable|image|max:2048',
            'documents.*' => 'nullable|file|max:5120',
        ]);

        $validated['user_id'] = Auth::id();
        $validated['start_date'] = now();
        $validated['end_date'] = now()->addDays($validated['duration_days']);
        $validated['status'] = 'pending';

        // Handle file uploads
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $image) {
                $images[] = $image->store('crowdfund/images', 'public');
            }
            $validated['images'] = $images;
        }

        if ($request->hasFile('documents')) {
            $documents = [];
            foreach ($request->file('documents') as $document) {
                $documents[] = $document->store('crowdfund/documents', 'public');
            }
            $validated['documents'] = $documents;
        }

        CrowdfundCampaign::create($validated);

        return redirect()->route('crowdfund.index')
            ->with('success', 'Crowdfunding campaign submitted for review.');
    }

    public function show(CrowdfundCampaign $campaign)
    {
        $campaign->load(['user', 'contributions.user', 'approver']);
        
        $userContribution = null;
        if (Auth::check()) {
            $userContribution = $campaign->contributions()
                ->where('user_id', Auth::id())
                ->first();
        }

        return view('crowdfund.show', compact('campaign', 'userContribution'));
    }

    public function contribute(Request $request, CrowdfundCampaign $campaign)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:1',
            'contribution_type' => 'required|in:donation,investment,loan',
            'is_anonymous' => 'boolean',
            'message' => 'nullable|string|max:500',
        ]);

        $validated['crowdfund_campaign_id'] = $campaign->id;
        $validated['user_id'] = Auth::id();
        $validated['status'] = 'pending';

        // Calculate expected return for investments
        if ($validated['contribution_type'] === 'investment' && $campaign->profit_percentage) {
            $validated['expected_return'] = $validated['amount'] * (1 + $campaign->profit_percentage / 100);
            $validated['expected_return_date'] = $campaign->end_date->addMonths(6); // Default 6 months
        }

        DB::transaction(function () use ($validated, $campaign) {
            CrowdfundContribution::create($validated);
            
            // Update campaign raised amount
            $campaign->increment('raised_amount', $validated['amount']);
        });

        return redirect()->route('crowdfund.show', $campaign)
            ->with('success', 'Your contribution has been submitted.');
    }
}
