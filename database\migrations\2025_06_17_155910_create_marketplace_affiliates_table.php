<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketplace_affiliates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained('marketplace_products')->onDelete('cascade');
            $table->string('affiliate_code')->unique();
            $table->decimal('commission_rate', 5, 2)->default(5.00); // Percentage
            $table->decimal('total_earnings', 10, 2)->default(0);
            $table->integer('total_clicks')->default(0);
            $table->integer('total_sales')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_click_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
            $table->index(['product_id', 'is_active']);
            $table->index('affiliate_code');
        });

        // Affiliate clicks tracking
        Schema::create('marketplace_affiliate_clicks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('affiliate_id')->constrained('marketplace_affiliates')->onDelete('cascade');
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('referrer')->nullable();
            $table->timestamp('clicked_at');
            $table->timestamps();

            $table->index(['affiliate_id', 'clicked_at']);
        });

        // Affiliate commissions
        Schema::create('marketplace_affiliate_commissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('affiliate_id')->constrained('marketplace_affiliates')->onDelete('cascade');
            $table->foreignId('order_id')->constrained('marketplace_orders')->onDelete('cascade');
            $table->decimal('commission_amount', 10, 2);
            $table->decimal('commission_rate', 5, 2);
            $table->enum('status', ['pending', 'approved', 'paid', 'cancelled'])->default('pending');
            $table->timestamp('earned_at');
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->index(['affiliate_id', 'status']);
            $table->index(['order_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketplace_affiliate_commissions');
        Schema::dropIfExists('marketplace_affiliate_clicks');
        Schema::dropIfExists('marketplace_affiliates');
    }
};
