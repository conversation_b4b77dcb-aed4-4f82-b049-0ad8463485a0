@extends('layouts.admin')

@section('title', 'Admin Notification Alerts')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Admin Notification Alerts</h1>
                <p class="text-gray-600 mt-1">Monitor system updates and user activities</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="markAllAsRead()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Mark All Read
                </button>
                <button onclick="configureAlerts()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Configure Alerts
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">23</h3>
                    <p class="text-sm text-gray-600">Unread Alerts</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">47</h3>
                    <p class="text-sm text-gray-600">New Users Today</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">12</h3>
                    <p class="text-sm text-gray-600">New Achievements</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">8</h3>
                    <p class="text-sm text-gray-600">Pending Approvals</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6 mb-8">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Filter Notifications</h3>
            <div class="flex items-center space-x-4">
                <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="">All Types</option>
                    <option value="user_registration">User Registration</option>
                    <option value="stage_activation">Stage Activation</option>
                    <option value="achievement_unlock">Achievement Unlock</option>
                    <option value="profile_request">Profile Request</option>
                    <option value="club_invitation">Club Invitation</option>
                    <option value="system_alert">System Alert</option>
                </select>
                <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="">All Priority</option>
                    <option value="high">High Priority</option>
                    <option value="medium">Medium Priority</option>
                    <option value="low">Low Priority</option>
                </select>
                <input type="date" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Notifications</h3>
        </div>
        <div class="divide-y divide-gray-200">
            <!-- High Priority Alert -->
            <div class="p-6 bg-red-50 border-l-4 border-red-400">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-red-800">System Alert - High Priority</h4>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                    High Priority
                                </span>
                                <span class="text-xs text-gray-500">2 minutes ago</span>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-red-700">Multiple failed login attempts detected for admin account. Security review recommended.</p>
                        <div class="mt-3 flex items-center space-x-3">
                            <button onclick="viewAlert(1)" class="text-red-600 hover:text-red-900 text-sm font-medium">View Details</button>
                            <button onclick="markAsRead(1)" class="text-gray-600 hover:text-gray-900 text-sm">Mark as Read</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Registration -->
            <div class="p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900">New User Registration</h4>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    User Registration
                                </span>
                                <span class="text-xs text-gray-500">15 minutes ago</span>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-600">Sarah Johnson (<EMAIL>) has registered and completed email verification.</p>
                        <div class="mt-3 flex items-center space-x-3">
                            <button onclick="viewUser(2)" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">View User</button>
                            <button onclick="markAsRead(2)" class="text-gray-600 hover:text-gray-900 text-sm">Mark as Read</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Achievement Unlock -->
            <div class="p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900">Achievement Unlocked</h4>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Achievement
                                </span>
                                <span class="text-xs text-gray-500">1 hour ago</span>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-600">Mike Davis earned his first White Star by completing all 6 stages!</p>
                        <div class="mt-3 flex items-center space-x-3">
                            <button onclick="viewAchievement(3)" class="text-green-600 hover:text-green-900 text-sm font-medium">View Achievement</button>
                            <button onclick="markAsRead(3)" class="text-gray-600 hover:text-gray-900 text-sm">Mark as Read</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Request -->
            <div class="p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900">Profile Information Request</h4>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Profile Request
                                </span>
                                <span class="text-xs text-gray-500">2 hours ago</span>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-600">Alex Thompson requested access to view John Smith's private profile information.</p>
                        <div class="mt-3 flex items-center space-x-3">
                            <button onclick="approveRequest(4)" class="text-green-600 hover:text-green-900 text-sm font-medium">Approve</button>
                            <button onclick="rejectRequest(4)" class="text-red-600 hover:text-red-900 text-sm font-medium">Reject</button>
                            <button onclick="markAsRead(4)" class="text-gray-600 hover:text-gray-900 text-sm">Mark as Read</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Club Invitation -->
            <div class="p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900">Elite Club Invitation Request</h4>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                    Club Invitation
                                </span>
                                <span class="text-xs text-gray-500">3 hours ago</span>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-600">Emma Wilson (Stage 6) has requested an invitation to the Elite Club.</p>
                        <div class="mt-3 flex items-center space-x-3">
                            <button onclick="approveClubInvitation(5)" class="text-green-600 hover:text-green-900 text-sm font-medium">Approve</button>
                            <button onclick="rejectClubInvitation(5)" class="text-red-600 hover:text-red-900 text-sm font-medium">Reject</button>
                            <button onclick="markAsRead(5)" class="text-gray-600 hover:text-gray-900 text-sm">Mark as Read</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function markAllAsRead() {
    if (confirm('Mark all notifications as read?')) {
        alert('Mark all as read functionality will be implemented');
    }
}

function configureAlerts() {
    alert('Configure alerts functionality will be implemented');
}

function viewAlert(id) {
    alert(`View alert ${id} functionality will be implemented`);
}

function markAsRead(id) {
    alert(`Mark notification ${id} as read functionality will be implemented`);
}

function viewUser(id) {
    alert(`View user ${id} functionality will be implemented`);
}

function viewAchievement(id) {
    alert(`View achievement ${id} functionality will be implemented`);
}

function approveRequest(id) {
    if (confirm('Approve this profile information request?')) {
        alert(`Approve request ${id} functionality will be implemented`);
    }
}

function rejectRequest(id) {
    if (confirm('Reject this profile information request?')) {
        alert(`Reject request ${id} functionality will be implemented`);
    }
}

function approveClubInvitation(id) {
    if (confirm('Approve this Elite Club invitation request?')) {
        alert(`Approve club invitation ${id} functionality will be implemented`);
    }
}

function rejectClubInvitation(id) {
    if (confirm('Reject this Elite Club invitation request?')) {
        alert(`Reject club invitation ${id} functionality will be implemented`);
    }
}
</script>
@endsection
