<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update stage names to reflect life journey stages
        $stageUpdates = [
            'bronze' => [
                'name' => 'Starter',
                'slug' => 'starter',
                'description' => 'Beginning your sustainable life journey with basic foundations',
            ],
            'silver' => [
                'name' => 'Development',
                'slug' => 'development',
                'description' => 'Developing sustainable habits and expanding your knowledge',
            ],
            'gold' => [
                'name' => 'Action',
                'slug' => 'action',
                'description' => 'Taking active steps towards a fully sustainable lifestyle',
            ],
            'platinum' => [
                'name' => 'Management',
                'slug' => 'management',
                'description' => 'Managing and optimizing your sustainable living systems',
            ],
            'diamond' => [
                'name' => 'Abundance',
                'slug' => 'abundance',
                'description' => 'Living in abundance while maintaining sustainable practices',
            ],
            'elite' => [
                'name' => 'Retirement',
                'slug' => 'retirement',
                'description' => 'Enjoying the fruits of a lifetime of sustainable living',
            ],
        ];

        foreach ($stageUpdates as $oldSlug => $newData) {
            DB::table('membership_stages')
                ->where('slug', $oldSlug)
                ->update([
                    'name' => $newData['name'],
                    'slug' => $newData['slug'],
                    'description' => $newData['description'],
                    'updated_at' => now(),
                ]);
        }

        // Update commission table references
        $slugMappings = [
            'bronze' => 'starter',
            'silver' => 'development',
            'gold' => 'action',
            'platinum' => 'management',
            'diamond' => 'abundance',
            'elite' => 'retirement',
        ];

        foreach ($slugMappings as $oldSlug => $newSlug) {
            DB::table('commissions')
                ->where('stage_slug', $oldSlug)
                ->update(['stage_slug' => $newSlug]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the stage name updates
        $stageReverts = [
            'starter' => [
                'name' => 'Bronze',
                'slug' => 'bronze',
                'description' => 'Entry level membership with basic benefits',
            ],
            'development' => [
                'name' => 'Silver',
                'slug' => 'silver',
                'description' => 'Intermediate membership with enhanced benefits',
            ],
            'action' => [
                'name' => 'Gold',
                'slug' => 'gold',
                'description' => 'Advanced membership with premium benefits',
            ],
            'management' => [
                'name' => 'Platinum',
                'slug' => 'platinum',
                'description' => 'Premium membership with exclusive benefits',
            ],
            'abundance' => [
                'name' => 'Diamond',
                'slug' => 'diamond',
                'description' => 'Elite membership with maximum benefits',
            ],
            'retirement' => [
                'name' => 'Elite',
                'slug' => 'elite',
                'description' => 'Ultimate membership with all benefits',
            ],
        ];

        foreach ($stageReverts as $currentSlug => $oldData) {
            DB::table('membership_stages')
                ->where('slug', $currentSlug)
                ->update([
                    'name' => $oldData['name'],
                    'slug' => $oldData['slug'],
                    'description' => $oldData['description'],
                    'updated_at' => now(),
                ]);
        }

        // Revert commission table references
        $slugMappings = [
            'starter' => 'bronze',
            'development' => 'silver',
            'action' => 'gold',
            'management' => 'platinum',
            'abundance' => 'diamond',
            'retirement' => 'elite',
        ];

        foreach ($slugMappings as $currentSlug => $oldSlug) {
            DB::table('commissions')
                ->where('stage_slug', $currentSlug)
                ->update(['stage_slug' => $oldSlug]);
        }
    }
};
