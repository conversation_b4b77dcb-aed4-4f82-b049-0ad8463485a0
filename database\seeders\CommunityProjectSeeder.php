<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CommunityProject;
use App\Models\ProjectVote;
use App\Models\ProjectDonation;
use App\Models\ProjectVolunteer;
use App\Models\User;
use Carbon\Carbon;

class CommunityProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        if ($users->count() === 0) {
            $this->command->info('No users found. Please run UserSeeder first.');
            return;
        }

        // Create petition projects
        $petitionProjects = [
            [
                'title' => 'Community Solar Garden Initiative',
                'description' => 'Establish a community solar garden to provide renewable energy for local households and reduce carbon footprint.',
                'requirements' => 'Solar panels, installation equipment, permits, electrical infrastructure, and community coordination.',
                'amount_needed' => 25000,
                'project_type' => 'environmentalist',
                'project_duration_days' => 120,
                'status' => 'petition'
            ],
            [
                'title' => 'Urban Bee Conservation Project',
                'description' => 'Create bee-friendly spaces throughout the city to support pollinator populations and biodiversity.',
                'requirements' => 'Native plants, beehives, educational materials, and volunteer coordinators.',
                'amount_needed' => 8500,
                'project_type' => 'conservationist',
                'project_duration_days' => 90,
                'status' => 'petition'
            ],
            [
                'title' => 'Zero Waste Community Workshop',
                'description' => 'Organize workshops to teach community members about zero waste living and sustainable practices.',
                'requirements' => 'Workshop space, educational materials, expert speakers, and promotional resources.',
                'amount_needed' => 3200,
                'project_type' => 'education',
                'project_duration_days' => 60,
                'status' => 'petition'
            ]
        ];

        foreach ($petitionProjects as $index => $projectData) {
            $user = $users->random();
            $project = CommunityProject::create(array_merge($projectData, [
                'user_id' => $user->id,
                'voting_deadline' => Carbon::now()->addDays(30),
                'votes_required' => 10
            ]));

            // Add some votes
            $availableVoters = $users->where('id', '!=', $user->id);
            $voterCount = min(rand(2, 8), $availableVoters->count());
            if ($voterCount > 0) {
                $voters = $availableVoters->random($voterCount);
                foreach ($voters as $voter) {
                    ProjectVote::create([
                        'project_id' => $project->id,
                        'user_id' => $voter->id,
                        'vote_type' => rand(1, 10) > 2 ? 'support' : 'against',
                        'comment' => rand(1, 3) === 1 ? 'Great initiative! I support this project.' : null
                    ]);
                }
            }

            $project->updateVotesCount();
        }

        // Create featured projects
        $featuredProjects = [
            [
                'title' => 'Community Composting Hub',
                'description' => 'Build a centralized composting facility to process organic waste from local households and businesses.',
                'requirements' => 'Composting equipment, land lease, maintenance tools, and educational signage.',
                'amount_needed' => 15000,
                'project_type' => 'environmentalist',
                'project_duration_days' => 90,
                'status' => 'featured'
            ],
            [
                'title' => 'River Cleanup & Restoration',
                'description' => 'Organize regular river cleanup events and implement restoration measures to improve water quality.',
                'requirements' => 'Cleanup supplies, restoration materials, volunteer coordination, and monitoring equipment.',
                'amount_needed' => 12000,
                'project_type' => 'conservationist',
                'project_duration_days' => 180,
                'status' => 'featured'
            ]
        ];

        foreach ($featuredProjects as $projectData) {
            $user = $users->random();
            $project = CommunityProject::create(array_merge($projectData, [
                'user_id' => $user->id,
                'voting_deadline' => Carbon::now()->subDays(10),
                'project_deadline' => Carbon::now()->addDays($projectData['project_duration_days']),
                'approved_at' => Carbon::now()->subDays(5),
                'featured_at' => Carbon::now()->subDays(5),
                'votes_required' => 10,
                'votes_count' => rand(12, 25)
            ]));

            // Add donations
            $donorCount = min(rand(1, 3), $users->count());
            if ($donorCount > 0) {
                $donors = $users->random($donorCount);
                foreach ($donors as $donor) {
                    ProjectDonation::create([
                        'project_id' => $project->id,
                        'user_id' => $donor->id,
                        'amount' => rand(50, 500),
                        'is_anonymous' => rand(1, 4) === 1,
                        'donor_name' => $donor->name,
                        'status' => 'completed'
                    ]);
                }
            }

            // Add volunteers
            $volunteerCount = min(rand(1, 2), $users->count());
            if ($volunteerCount > 0) {
                $volunteers = $users->random($volunteerCount);
                foreach ($volunteers as $volunteer) {
                    ProjectVolunteer::create([
                        'project_id' => $project->id,
                        'user_id' => $volunteer->id,
                        'skills_offered' => 'General labor, coordination, and community outreach',
                        'availability' => 'Weekends and evenings',
                        'status' => 'approved',
                        'approved_at' => Carbon::now()->subDays(rand(1, 4))
                    ]);
                }
            }

            $project->updateAmountRaised();
            $project->updateVolunteerCount();
        }

        // Create closed projects
        $closedProjects = [
            [
                'title' => 'Community Garden Expansion',
                'description' => 'Successfully expanded the community garden with new plots and improved irrigation system.',
                'requirements' => 'Garden plots, irrigation equipment, soil amendments, and community coordination.',
                'amount_needed' => 8000,
                'project_type' => 'stewards',
                'project_duration_days' => 120,
                'status' => 'closed'
            ]
        ];

        foreach ($closedProjects as $projectData) {
            $user = $users->random();
            $project = CommunityProject::create(array_merge($projectData, [
                'user_id' => $user->id,
                'voting_deadline' => Carbon::now()->subDays(150),
                'project_deadline' => Carbon::now()->subDays(30),
                'approved_at' => Carbon::now()->subDays(140),
                'featured_at' => Carbon::now()->subDays(140),
                'closed_at' => Carbon::now()->subDays(30),
                'votes_required' => 10,
                'votes_count' => rand(15, 30),
                'amount_raised' => $projectData['amount_needed'] * 0.95, // 95% funded
                'credibility_score' => rand(80, 100)
            ]));
        }

        $this->command->info('Community projects seeded successfully!');
    }
}
