<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'symbol',
        'exchange_rate',
        'is_active',
        'is_default',
        'decimal_places',
    ];

    protected $casts = [
        'exchange_rate' => 'decimal:6',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'decimal_places' => 'integer',
    ];

    /**
     * Get wallets for this currency.
     */
    public function wallets()
    {
        return $this->hasMany(Wallet::class);
    }

    /**
     * Get the default currency.
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Get active currencies.
     */
    public static function getActive()
    {
        return static::where('is_active', true)->orderBy('code')->get();
    }

    /**
     * Convert amount from this currency to another.
     */
    public function convertTo(Currency $toCurrency, $amount)
    {
        // Convert to USD first, then to target currency
        $usdAmount = $amount / $this->exchange_rate;
        return $usdAmount * $toCurrency->exchange_rate;
    }

    /**
     * Convert amount from USD to this currency.
     */
    public function convertFromUsd($usdAmount)
    {
        return $usdAmount * $this->exchange_rate;
    }

    /**
     * Convert amount to USD from this currency.
     */
    public function convertToUsd($amount)
    {
        return $amount / $this->exchange_rate;
    }

    /**
     * Format amount with currency symbol.
     */
    public function formatAmount($amount)
    {
        return $this->symbol . number_format($amount, $this->decimal_places);
    }

    /**
     * Get exchange rate display.
     */
    public function getExchangeRateDisplayAttribute()
    {
        return "1 USD = {$this->exchange_rate} {$this->code}";
    }
}
