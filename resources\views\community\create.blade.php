@extends('layouts.app')

@section('title', 'Create Community Project')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Create Community Project</h1>
                <p class="mt-2 text-gray-600">Submit your project petition for community voting</p>
            </div>
            <a href="{{ route('community.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Projects
            </a>
        </div>
    </div>

    <!-- Project Creation Form -->
    <div class="bg-white shadow rounded-lg">
        <form method="POST" action="{{ route('community.store') }}" enctype="multipart/form-data" class="space-y-6 p-6">
            @csrf

            <!-- Basic Information -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Project Information</h3>
                
                <div class="grid grid-cols-1 gap-6">
                    <!-- Project Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700">Project Title</label>
                        <input type="text" 
                               name="title" 
                               id="title" 
                               value="{{ old('title') }}"
                               required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                               placeholder="Enter a compelling project title">
                        @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Project Type -->
                    <div>
                        <label for="project_type" class="block text-sm font-medium text-gray-700">Project Type</label>
                        <select name="project_type" 
                                id="project_type" 
                                required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select project type</option>
                            @foreach($projectTypes as $key => $label)
                            <option value="{{ $key }}" {{ old('project_type') === $key ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                        @error('project_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Project Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Project Description</label>
                        <textarea name="description" 
                                  id="description" 
                                  rows="4" 
                                  required
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                  placeholder="Describe your project, its goals, and expected impact">{{ old('description') }}</textarea>
                        @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Project Requirements -->
                    <div>
                        <label for="requirements" class="block text-sm font-medium text-gray-700">What do you need to complete this project?</label>
                        <textarea name="requirements" 
                                  id="requirements" 
                                  rows="4" 
                                  required
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                  placeholder="List resources, materials, skills, or support needed">{{ old('requirements') }}</textarea>
                        @error('requirements')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Financial Information -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Financial Details</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Amount Needed -->
                    <div>
                        <label for="amount_needed" class="block text-sm font-medium text-gray-700">Amount Needed ($)</label>
                        <input type="number" 
                               name="amount_needed" 
                               id="amount_needed" 
                               value="{{ old('amount_needed') }}"
                               min="0"
                               step="0.01"
                               required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                               placeholder="0.00">
                        @error('amount_needed')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Project Duration -->
                    <div>
                        <label for="project_duration_days" class="block text-sm font-medium text-gray-700">Project Duration (Days)</label>
                        <select name="project_duration_days" 
                                id="project_duration_days" 
                                required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select duration</option>
                            <option value="30" {{ old('project_duration_days') == '30' ? 'selected' : '' }}>30 days</option>
                            <option value="60" {{ old('project_duration_days') == '60' ? 'selected' : '' }}>60 days</option>
                            <option value="90" {{ old('project_duration_days') == '90' ? 'selected' : '' }}>90 days</option>
                            <option value="120" {{ old('project_duration_days') == '120' ? 'selected' : '' }}>120 days</option>
                            <option value="180" {{ old('project_duration_days') == '180' ? 'selected' : '' }}>180 days</option>
                            <option value="365" {{ old('project_duration_days') == '365' ? 'selected' : '' }}>1 year</option>
                        </select>
                        @error('project_duration_days')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Media Uploads -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Project Media</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Images -->
                    <div>
                        <label for="images" class="block text-sm font-medium text-gray-700">Images</label>
                        <input type="file" 
                               name="images[]" 
                               id="images" 
                               multiple
                               accept="image/*"
                               class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                        <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB each</p>
                        @error('images.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Videos -->
                    <div>
                        <label for="videos" class="block text-sm font-medium text-gray-700">Videos</label>
                        <input type="file" 
                               name="videos[]" 
                               id="videos" 
                               multiple
                               accept="video/*"
                               class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                        <p class="mt-1 text-xs text-gray-500">MP4, AVI, MOV up to 10MB each</p>
                        @error('videos.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Documents -->
                    <div>
                        <label for="documents" class="block text-sm font-medium text-gray-700">Documents</label>
                        <input type="file" 
                               name="documents[]" 
                               id="documents" 
                               multiple
                               accept=".pdf,.doc,.docx"
                               class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                        <p class="mt-1 text-xs text-gray-500">PDF, DOC, DOCX up to 5MB each</p>
                        @error('documents.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Important Information -->
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Important Information</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>Your project will enter a voting period where community members can support it</li>
                                <li>A minimum number of votes is required for admin approval</li>
                                <li>If approved, your project will be featured for fundraising and volunteer recruitment</li>
                                <li>Projects that don't meet voting requirements within the deadline will be automatically denied</li>
                                <li>You can delete denied projects and resubmit with improvements</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('community.index') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" 
                        class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    Create Project Petition
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
