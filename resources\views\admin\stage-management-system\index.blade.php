@extends('layouts.admin')

@section('title', 'Stage Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Stage Management System</h1>
                <p class="text-gray-600 mt-1">Configure membership stages, pricing, benefits, and progression rules</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportStageData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Data
                </button>
                <button onclick="saveAllStages()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                    Save All Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Stage Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">6</h3>
                    <p class="text-sm text-gray-600">Total Stages</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">12,847</h3>
                    <p class="text-sm text-gray-600">Active Members</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">1,234</h3>
                    <p class="text-sm text-gray-600">Recent Activations</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">$456,789</h3>
                    <p class="text-sm text-gray-600">Total Revenue</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'stages' }">
                <button @click="activeTab = 'stages'; window.showNotification('🎯 Stage Configuration loaded!', 'success')" :class="activeTab === 'stages' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Stage Configuration
                </button>
                <button @click="activeTab = 'pricing'; window.showNotification('💰 Pricing & Benefits loaded!', 'success')" :class="activeTab === 'pricing' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Pricing & Benefits
                </button>
                <button @click="activeTab = 'progression'; window.showNotification('📈 Progression Rules loaded!', 'success')" :class="activeTab === 'progression' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Progression Rules
                </button>
                <button @click="activeTab = 'analytics'; window.showNotification('📊 Stage Analytics loaded!', 'success')" :class="activeTab === 'analytics' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Stage Analytics
                </button>
                <button @click="activeTab = 'members'; window.showNotification('👥 Member Management loaded!', 'success')" :class="activeTab === 'members' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Member Management
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'stages' }">
        <!-- Stage Configuration Tab -->
        <div x-show="activeTab === 'stages'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- Stage 1 -->
                <div class="bg-white shadow rounded-lg p-6 border-l-4 border-blue-500">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Stage 1 - Starter</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Active</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                            <input type="text" value="Starter" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                            <input type="number" value="99" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Commission Rate (%)</label>
                            <input type="number" value="10" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Enable Stage</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>
                        <button onclick="editStage(1)" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Edit Stage Details
                        </button>
                    </div>
                </div>

                <!-- Stage 2 -->
                <div class="bg-white shadow rounded-lg p-6 border-l-4 border-green-500">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Stage 2 - Growth</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                            <input type="text" value="Growth" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                            <input type="number" value="299" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Commission Rate (%)</label>
                            <input type="number" value="15" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Enable Stage</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>
                        <button onclick="editStage(2)" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            Edit Stage Details
                        </button>
                    </div>
                </div>

                <!-- Stage 3 -->
                <div class="bg-white shadow rounded-lg p-6 border-l-4 border-purple-500">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Stage 3 - Professional</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Active</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                            <input type="text" value="Professional" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                            <input type="number" value="599" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Commission Rate (%)</label>
                            <input type="number" value="20" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Enable Stage</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>
                        <button onclick="editStage(3)" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                            Edit Stage Details
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Add New Stage</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Stage Name</label>
                        <input type="text" placeholder="Enter stage name" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Activation Price</label>
                        <input type="number" placeholder="0.00" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Commission Rate (%)</label>
                        <input type="number" placeholder="0" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                    <div class="flex items-end">
                        <button onclick="addNewStage()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                            Add Stage
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pricing & Benefits Tab -->
        <div x-show="activeTab === 'pricing'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Stage Benefits Configuration</h3>
                <div class="space-y-6">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-3">Stage 1 - Starter Benefits</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Basic Dashboard Access</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Referral System</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Priority Support</span>
                                </label>
                            </div>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Monthly Reports</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Advanced Analytics</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Custom Branding</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-3">Stage 2 - Growth Benefits</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">All Stage 1 Benefits</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Priority Support</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Advanced Analytics</span>
                                </label>
                            </div>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">Team Management</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">API Access</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-purple-600">
                                    <span class="ml-2 text-sm text-gray-700">White Label Options</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progression Rules Tab -->
        <div x-show="activeTab === 'progression'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Stage Progression Rules</h3>
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Automatic Stage Progression</h4>
                            <p class="text-sm text-gray-500">Allow users to automatically progress to next stage</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Time Between Progressions (days)</label>
                            <input type="number" value="30" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Required Referrals for Next Stage</label>
                            <input type="number" value="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="text-sm font-medium text-gray-900">Progression Requirements</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Require Previous Stage Completion</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Require Admin Approval</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Allow Stage Skipping</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stage Analytics Tab -->
        <div x-show="activeTab === 'analytics'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Stage Distribution</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Stage 1 - Starter</span>
                            <span class="text-sm font-medium text-gray-900">5,234 members (40.7%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 40.7%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Stage 2 - Growth</span>
                            <span class="text-sm font-medium text-gray-900">3,891 members (30.3%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 30.3%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Stage 3 - Professional</span>
                            <span class="text-sm font-medium text-gray-900">2,456 members (19.1%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: 19.1%"></div>
                        </div>
                    </div>
                </div>

                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Revenue by Stage</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Stage 1 Revenue</span>
                            <span class="text-sm font-medium text-gray-900">$156,789</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Stage 2 Revenue</span>
                            <span class="text-sm font-medium text-gray-900">$189,234</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Stage 3 Revenue</span>
                            <span class="text-sm font-medium text-gray-900">$110,766</span>
                        </div>
                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-900">Total Revenue</span>
                                <span class="text-lg font-bold text-purple-600">$456,789</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Member Management Tab -->
        <div x-show="activeTab === 'members'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Stage Member Management</h3>
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>All Stages</option>
                            <option>Stage 1 - Starter</option>
                            <option>Stage 2 - Growth</option>
                            <option>Stage 3 - Professional</option>
                        </select>
                        <input type="text" placeholder="Search members..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                    <button onclick="bulkStageUpdate()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                        Bulk Update
                    </button>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stage</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=John+Doe&background=4F46E5&color=fff" alt="">
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">John Doe</div>
                                            <div class="text-sm text-gray-500"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Stage 1</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$299.00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="promoteUser('<EMAIL>')" class="text-green-600 hover:text-green-900 mr-3">Promote</button>
                                    <button onclick="viewMemberDetails('<EMAIL>')" class="text-indigo-600 hover:text-indigo-900">View</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportStageData() {
    alert('Export stage data functionality will be implemented');
}

function saveAllStages() {
    alert('Save all stages functionality will be implemented');
}

function editStage(stageId) {
    alert(`Edit stage ${stageId} functionality will be implemented`);
}

function addNewStage() {
    alert('Add new stage functionality will be implemented');
}

function bulkStageUpdate() {
    alert('Bulk stage update functionality will be implemented');
}

function promoteUser(email) {
    if (confirm(`Promote user ${email} to next stage?`)) {
        alert(`Promote user ${email} functionality will be implemented`);
    }
}

function viewMemberDetails(email) {
    fetch(`/admin/stage-management-system/members/${email}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMemberDetailsModal(data.data);
            }
        });
}

// Stage Management System
const stageManagement = {
    data: {
        stages: [],
        analytics: {},
        progressionRules: {},
        members: []
    },

    charts: {
        distribution: null,
        revenue: null,
        progression: null
    },

    init() {
        this.loadStageData();
        this.setupEventListeners();
        this.initializeCharts();
        this.startRealTimeUpdates();
    },

    loadStageData() {
        Promise.all([
            this.loadStages(),
            this.loadAnalytics(),
            this.loadProgressionRules()
        ]).then(() => {
            this.updateDashboard();
        });
    },

    loadStages() {
        return fetch('/admin/stage-management-system/stages')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.stages = data.data;
                    this.updateStagesDisplay();
                }
            });
    },

    loadAnalytics(period = '30d') {
        return fetch(`/admin/stage-management-system/analytics?period=${period}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.analytics = data.data;
                    this.updateAnalyticsDisplay();
                }
            });
    },

    loadProgressionRules() {
        return fetch('/admin/stage-management-system/progression-rules')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.progressionRules = data.data;
                    this.populateProgressionForm();
                }
            });
    },

    setupEventListeners() {
        // Auto-save stage changes
        document.addEventListener('input', (e) => {
            if (e.target.matches('[data-stage-setting]')) {
                this.debounce(() => this.saveStageSettings(e.target.dataset.stageId), 1000);
            }
        });

        // Progression rules changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-progression-rule]')) {
                this.debounce(() => this.saveProgressionRules(), 1000);
            }
        });

        // Member search
        document.addEventListener('input', (e) => {
            if (e.target.matches('#member-search')) {
                this.debounce(() => this.searchMembers(e.target.value), 300);
            }
        });
    },

    initializeCharts() {
        this.initDistributionChart();
        this.initRevenueChart();
        this.initProgressionChart();
    },

    startRealTimeUpdates() {
        // Update stage statistics every 2 minutes
        setInterval(() => {
            this.loadAnalytics();
        }, 120000);

        // Update member counts every 30 seconds
        setInterval(() => {
            this.updateMemberCounts();
        }, 30000);
    },

    updateDashboard() {
        this.updateStagesDisplay();
        this.updateAnalyticsDisplay();
        this.updateCharts();
    },

    debounce(func, wait) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(func, wait);
    }
};

// Stage Configuration Functions
function saveStageSettings(stageId) {
    const formData = new FormData(document.querySelector(`#stage-form-${stageId}`));

    fetch(`/admin/stage-management-system/stages/${stageId}`, {
        method: 'PUT',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Stage settings saved!', 'success');
            stageManagement.loadStages();
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function editStage(stageId) {
    const stage = stageManagement.data.stages.find(s => s.id === stageId);
    if (stage) {
        showStageEditModal(stage);
    }
}

function addNewStage() {
    const formData = {
        name: document.querySelector('#new-stage-name').value,
        activation_price: document.querySelector('#new-stage-price').value,
        commission_rate: document.querySelector('#new-stage-commission').value,
        position: stageManagement.data.stages.length + 1
    };

    if (!formData.name || !formData.activation_price || !formData.commission_rate) {
        showNotification('Please fill in all required fields', 'warning');
        return;
    }

    fetch('/admin/stage-management-system/stages', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Stage created successfully!', 'success');
            stageManagement.loadStages();
            clearNewStageForm();
        } else {
            showNotification(data.message || 'Creation failed', 'error');
        }
    });
}

function deleteStage(stageId) {
    if (confirm('Are you sure you want to delete this stage? This action cannot be undone.')) {
        fetch(`/admin/stage-management-system/stages/${stageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Stage deleted successfully!', 'success');
                stageManagement.loadStages();
            }
        });
    }
}

// Progression Rules Functions
function saveProgressionRules() {
    const formData = new FormData(document.querySelector('#progression-rules-form'));

    fetch('/admin/stage-management-system/progression-rules', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Progression rules updated!', 'success');
        } else {
            showNotification(data.message || 'Update failed', 'error');
        }
    });
}

function validateProgression(userId, targetStageId) {
    fetch('/admin/stage-management-system/validate-progression', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            user_id: userId,
            target_stage_id: targetStageId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showProgressionValidationResults(data.data);
        }
    });
}

// Member Management Functions
function promoteUser(email) {
    const targetStage = prompt('Enter target stage ID:');
    const reason = prompt('Reason for promotion (optional):');

    if (targetStage) {
        fetch('/admin/stage-management-system/promote-user', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_email: email,
                target_stage_id: targetStage,
                reason: reason,
                skip_requirements: false
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('User promoted successfully!', 'success');
                stageManagement.loadStages();
            } else {
                showNotification(data.message || 'Promotion failed', 'error');
            }
        });
    }
}

function demoteUser(email) {
    const targetStage = prompt('Enter target stage ID:');
    const reason = prompt('Reason for demotion:');

    if (targetStage && reason) {
        fetch('/admin/stage-management-system/demote-user', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_email: email,
                target_stage_id: targetStage,
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('User demoted successfully!', 'success');
                stageManagement.loadStages();
            } else {
                showNotification(data.message || 'Demotion failed', 'error');
            }
        });
    }
}

function bulkStageUpdate() {
    const selectedUsers = getSelectedUsers();
    const action = document.querySelector('#bulk-action').value;
    const targetStage = document.querySelector('#bulk-target-stage').value;

    if (selectedUsers.length === 0) {
        showNotification('Please select users to update', 'warning');
        return;
    }

    if (!action) {
        showNotification('Please select an action', 'warning');
        return;
    }

    if ((action === 'promote' || action === 'demote') && !targetStage) {
        showNotification('Please select target stage', 'warning');
        return;
    }

    fetch('/admin/stage-management-system/bulk-update', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            user_ids: selectedUsers,
            action: action,
            target_stage_id: targetStage,
            reason: 'Bulk update operation'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Bulk ${action} completed successfully!`, 'success');
            stageManagement.loadStages();
            clearUserSelection();
        } else {
            showNotification(data.message || 'Bulk update failed', 'error');
        }
    });
}

// Export Functions
function exportStageData() {
    const type = document.querySelector('#export-type')?.value || 'all';
    const format = document.querySelector('#export-format')?.value || 'csv';

    window.open(`/admin/stage-management-system/export?type=${type}&format=${format}`, '_blank');
    showNotification('Export started. Download will begin shortly.', 'info');
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function showStageEditModal(stage) {
    // Implementation for showing stage edit modal
    alert(`Edit stage ${stage.name} - Modal implementation needed`);
}

function showMemberDetailsModal(member) {
    // Implementation for showing member details modal
    alert(`Member details for ${member.email} - Modal implementation needed`);
}

function showProgressionValidationResults(results) {
    // Implementation for showing progression validation results
    alert(`Progression validation results - Modal implementation needed`);
}

function getSelectedUsers() {
    const checkboxes = document.querySelectorAll('input[name="selected_users"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function clearUserSelection() {
    const checkboxes = document.querySelectorAll('input[name="selected_users"]');
    checkboxes.forEach(cb => cb.checked = false);
}

function clearNewStageForm() {
    document.querySelector('#new-stage-name').value = '';
    document.querySelector('#new-stage-price').value = '';
    document.querySelector('#new-stage-commission').value = '';
}

// Stage Tab Management System
const stageTabManager = {
    currentTab: 'configuration',

    init() {
        this.setupTabListeners();
        this.loadTabContent(this.currentTab);
    },

    setupTabListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-stage-tab]')) {
                e.preventDefault();
                const tabId = e.target.dataset.stageTab;
                this.switchTab(tabId);
            }
        });
    },

    switchTab(tabId) {
        // Update active tab
        document.querySelectorAll('[data-stage-tab]').forEach(tab => {
            tab.classList.remove('border-blue-500', 'text-blue-600');
            tab.classList.add('border-transparent', 'text-gray-500');
        });

        const activeTab = document.querySelector(`[data-stage-tab="${tabId}"]`);
        if (activeTab) {
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-blue-500', 'text-blue-600');
        }

        // Hide all tab content
        document.querySelectorAll('[data-stage-content]').forEach(content => {
            content.classList.add('hidden');
        });

        // Show selected tab content
        const activeContent = document.querySelector(`[data-stage-content="${tabId}"]`);
        if (activeContent) {
            activeContent.classList.remove('hidden');
        }

        this.currentTab = tabId;
        this.loadTabContent(tabId);
    },

    loadTabContent(tabId) {
        switch(tabId) {
            case 'configuration':
                this.loadConfigurationContent();
                break;
            case 'progression':
                this.loadProgressionContent();
                break;
            case 'members':
                this.loadMembersContent();
                break;
            case 'analytics':
                this.loadAnalyticsContent();
                break;
            case 'export':
                this.loadExportContent();
                break;
        }
    },

    loadConfigurationContent() {
        fetch('/admin/stage-management-system/configuration')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateConfigurationDisplay(data.data);
                }
            });
    },

    loadProgressionContent() {
        fetch('/admin/stage-management-system/progression-rules')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateProgressionDisplay(data.data);
                }
            });
    },

    loadMembersContent() {
        fetch('/admin/stage-management-system/members-overview')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateMembersDisplay(data.data);
                }
            });
    },

    loadAnalyticsContent() {
        fetch('/admin/stage-management-system/analytics-data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateAnalyticsDisplay(data.data);
                }
            });
    },

    loadExportContent() {
        fetch('/admin/stage-management-system/export-options')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateExportDisplay(data.data);
                }
            });
    },

    updateConfigurationDisplay(data) {
        // Update stage configuration forms
        if (data.stages) {
            this.renderStageConfiguration(data.stages);
        }
    },

    updateProgressionDisplay(data) {
        // Update progression rules form
        Object.keys(data).forEach(key => {
            const element = document.querySelector(`[name="progression_${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = data[key];
                } else {
                    element.value = data[key];
                }
            }
        });
    },

    updateMembersDisplay(data) {
        // Update members overview
        const container = document.querySelector('#members-overview-container');
        if (container && data.members) {
            container.innerHTML = this.generateMembersHTML(data.members);
        }
    },

    updateAnalyticsDisplay(data) {
        // Update analytics charts
        if (data.analytics) {
            this.updateStageAnalyticsCharts(data.analytics);
        }
    },

    updateExportDisplay(data) {
        // Update export options
        const container = document.querySelector('#export-options-container');
        if (container && data.options) {
            container.innerHTML = this.generateExportOptionsHTML(data.options);
        }
    },

    renderStageConfiguration(stages) {
        const container = document.querySelector('#stage-configuration-container');
        if (!container) return;

        container.innerHTML = stages.map(stage => `
            <div class="border border-gray-200 rounded-lg p-4 mb-4" data-stage-id="${stage.id}">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="text-lg font-medium">${stage.name}</h4>
                    <div class="flex space-x-2">
                        <button onclick="editStageConfig('${stage.id}')" class="text-sm bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">Edit</button>
                        <button onclick="deleteStageConfig('${stage.id}')" class="text-sm bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600">Delete</button>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Activation Price</label>
                        <input type="number" value="${stage.activation_price}" class="mt-1 block w-full rounded-md border-gray-300" data-field="activation_price">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Commission Rate (%)</label>
                        <input type="number" value="${stage.commission_rate}" class="mt-1 block w-full rounded-md border-gray-300" data-field="commission_rate">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Max Members</label>
                        <input type="number" value="${stage.max_members || ''}" class="mt-1 block w-full rounded-md border-gray-300" data-field="max_members">
                    </div>
                </div>
                <div class="mt-3">
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    <textarea class="mt-1 block w-full rounded-md border-gray-300" rows="2" data-field="description">${stage.description || ''}</textarea>
                </div>
                <div class="mt-3 flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="checkbox" ${stage.is_active ? 'checked' : ''} class="rounded border-gray-300" data-field="is_active">
                        <span class="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" ${stage.auto_upgrade ? 'checked' : ''} class="rounded border-gray-300" data-field="auto_upgrade">
                        <span class="ml-2 text-sm text-gray-700">Auto Upgrade</span>
                    </label>
                </div>
            </div>
        `).join('');
    },

    generateMembersHTML(members) {
        return members.map(member => `
            <div class="border border-gray-200 rounded-lg p-4 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium">${member.name}</span>
                    <span class="text-sm px-2 py-1 rounded ${this.getStageClass(member.current_stage)}">
                        ${member.current_stage}
                    </span>
                </div>
                <div class="text-sm text-gray-600">
                    Email: ${member.email} |
                    Joined: ${formatDate(member.joined_at)} |
                    Revenue: ${formatCurrency(member.total_revenue)}
                </div>
                <div class="mt-2 flex space-x-2">
                    <button onclick="viewMemberDetails('${member.id}')" class="text-xs text-blue-600 hover:text-blue-900">View Details</button>
                    <button onclick="promoteUser('${member.email}')" class="text-xs text-green-600 hover:text-green-900">Promote</button>
                    <button onclick="demoteUser('${member.email}')" class="text-xs text-red-600 hover:text-red-900">Demote</button>
                </div>
            </div>
        `).join('');
    },

    generateExportOptionsHTML(options) {
        return options.map(option => `
            <div class="border border-gray-200 rounded-lg p-4 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium">${option.name}</span>
                    <span class="text-sm text-gray-500">${option.format}</span>
                </div>
                <div class="text-sm text-gray-600 mb-2">${option.description}</div>
                <div class="flex space-x-2">
                    <button onclick="exportStageData('${option.type}')" class="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">Export Now</button>
                    <button onclick="scheduleExport('${option.type}')" class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">Schedule</button>
                </div>
            </div>
        `).join('');
    },

    getStageClass(stage) {
        const stageColors = {
            'Stage 1': 'bg-gray-100 text-gray-800',
            'Stage 2': 'bg-blue-100 text-blue-800',
            'Stage 3': 'bg-green-100 text-green-800',
            'Stage 4': 'bg-yellow-100 text-yellow-800',
            'Stage 5': 'bg-purple-100 text-purple-800',
            'Stage 6': 'bg-red-100 text-red-800'
        };
        return stageColors[stage] || 'bg-gray-100 text-gray-800';
    },

    updateStageAnalyticsCharts(analytics) {
        // Update various analytics charts
        if (window.stageDistributionChart && analytics.distribution) {
            window.stageDistributionChart.data.datasets[0].data = analytics.distribution;
            window.stageDistributionChart.update();
        }

        if (window.stageRevenueChart && analytics.revenue) {
            window.stageRevenueChart.data.datasets[0].data = analytics.revenue;
            window.stageRevenueChart.update();
        }
    }
};

// Stage sub-menu specific functions
function saveStageConfiguration() {
    const stageConfigs = [];
    document.querySelectorAll('[data-stage-id]').forEach(stageElement => {
        const stageId = stageElement.dataset.stageId;
        const config = {
            id: stageId,
            activation_price: stageElement.querySelector('[data-field="activation_price"]').value,
            commission_rate: stageElement.querySelector('[data-field="commission_rate"]').value,
            max_members: stageElement.querySelector('[data-field="max_members"]').value,
            description: stageElement.querySelector('[data-field="description"]').value,
            is_active: stageElement.querySelector('[data-field="is_active"]').checked,
            auto_upgrade: stageElement.querySelector('[data-field="auto_upgrade"]').checked
        };
        stageConfigs.push(config);
    });

    fetch('/admin/stage-management-system/save-configuration', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ stages: stageConfigs })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Stage configuration saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function saveProgressionRules() {
    const formData = new FormData(document.querySelector('#progression-rules-form'));

    fetch('/admin/stage-management-system/progression-rules', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Progression rules saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function editStageConfig(stageId) {
    // Implementation for editing stage configuration
    showNotification(`Editing stage ${stageId} configuration`, 'info');
}

function deleteStageConfig(stageId) {
    if (confirm('Are you sure you want to delete this stage configuration?')) {
        fetch(`/admin/stage-management-system/stages/${stageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Stage configuration deleted successfully!', 'success');
                stageTabManager.loadConfigurationContent();
            } else {
                showNotification(data.message || 'Delete failed', 'error');
            }
        });
    }
}

function scheduleExport(exportType) {
    const frequency = prompt('Enter schedule frequency (daily, weekly, monthly):');
    const email = prompt('Enter email address for delivery:');

    if (frequency && email) {
        fetch('/admin/stage-management-system/schedule-export', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                export_type: exportType,
                frequency: frequency,
                email: email
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Export scheduled successfully!', 'success');
            } else {
                showNotification(data.message || 'Scheduling failed', 'error');
            }
        });
    }
}

function runStageAnalysis() {
    showNotification('Running stage analysis...', 'info');

    fetch('/admin/stage-management-system/run-analysis', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Stage analysis completed!', 'success');
            displayStageAnalysisResults(data.results);
        } else {
            showNotification(data.message || 'Analysis failed', 'error');
        }
    });
}

function displayStageAnalysisResults(results) {
    const resultsContainer = document.querySelector('#stage-analysis-results');
    if (!resultsContainer) return;

    resultsContainer.innerHTML = `
        <div class="bg-white rounded-lg p-6 mt-4">
            <h3 class="text-lg font-medium mb-4">Stage Analysis Results</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">${results.total_members || 0}</div>
                    <div class="text-sm text-blue-700">Total Members</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">${results.active_stages || 0}</div>
                    <div class="text-sm text-green-700">Active Stages</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">${results.progression_rate || 0}%</div>
                    <div class="text-sm text-yellow-700">Progression Rate</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">${formatCurrency(results.total_revenue || 0)}</div>
                    <div class="text-sm text-purple-700">Total Revenue</div>
                </div>
            </div>
            <div class="space-y-3">
                ${(results.insights || []).map(insight => `
                    <div class="flex items-center p-3 rounded-lg bg-gray-50">
                        <div class="flex-1">
                            <div class="font-medium">${insight.title}</div>
                            <div class="text-sm text-gray-600">${insight.description}</div>
                        </div>
                        <div class="ml-4 text-2xl">
                            ${insight.type === 'positive' ? '📈' : insight.type === 'negative' ? '📉' : 'ℹ️'}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Missing functions for tab functionality
function loadMemberData() {
    showNotification('Loading member data...', 'info');
    fetch('/admin/stage-management-system/members-overview')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Member data loaded successfully!', 'success');
            }
        });
}

// Add missing showNotification function
window.showNotification = function(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    stageManagement.init();
    stageTabManager.init();

    // Test notification
    setTimeout(() => {
        showNotification('Stage Management loaded successfully!', 'success');
    }, 1000);
});
</script>
@endsection
