<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Message extends Model
{
    protected $fillable = [
        'sender_id',
        'recipient_id',
        'subject',
        'message',
        'read',
        'sender_deleted',
        'recipient_deleted',
        'read_at'
    ];

    protected $casts = [
        'read' => 'boolean',
        'sender_deleted' => 'boolean',
        'recipient_deleted' => 'boolean',
        'read_at' => 'datetime'
    ];

    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function recipient(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recipient_id');
    }

    public function markAsRead()
    {
        $this->update([
            'read' => true,
            'read_at' => now()
        ]);
    }

    public function scopeUnread($query)
    {
        return $query->where('read', false);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where(function($q) use ($userId) {
            $q->where('sender_id', $userId)->where('sender_deleted', false)
              ->orWhere('recipient_id', $userId)->where('recipient_deleted', false);
        });
    }

    public function scopeInbox($query, $userId)
    {
        return $query->where('recipient_id', $userId)->where('recipient_deleted', false);
    }

    public function scopeSent($query, $userId)
    {
        return $query->where('sender_id', $userId)->where('sender_deleted', false);
    }
}
