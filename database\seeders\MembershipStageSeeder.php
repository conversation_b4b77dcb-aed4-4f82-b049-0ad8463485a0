<?php

namespace Database\Seeders;

use App\Models\MembershipStage;
use Illuminate\Database\Seeder;

class MembershipStageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stages = [
            [
                'name' => 'Bronze',
                'slug' => 'bronze',
                'min_referrals' => 1,
                'max_referrals' => 5,
                'commission_rate' => 5.00,
                'activation_bonus' => 10.00,
                'description' => 'Entry level membership with basic benefits',
                'benefits' => [
                    'Basic dashboard access',
                    '$5 commission per referral',
                    '$10 activation bonus'
                ],
                'sort_order' => 1,
            ],
            [
                'name' => 'Silver',
                'slug' => 'silver',
                'min_referrals' => 6,
                'max_referrals' => 15,
                'commission_rate' => 10.00,
                'activation_bonus' => 25.00,
                'description' => 'Silver level with enhanced earning potential',
                'benefits' => [
                    'Enhanced dashboard features',
                    '$10 commission per referral',
                    '$25 activation bonus',
                    'Priority support'
                ],
                'sort_order' => 2,
            ],
            [
                'name' => 'Gold',
                'slug' => 'gold',
                'min_referrals' => 16,
                'max_referrals' => 30,
                'commission_rate' => 15.00,
                'activation_bonus' => 50.00,
                'description' => 'Gold level with premium benefits',
                'benefits' => [
                    'Premium dashboard access',
                    '$15 commission per referral',
                    '$50 activation bonus',
                    'Priority support',
                    'Monthly bonus opportunities'
                ],
                'sort_order' => 3,
            ],
            [
                'name' => 'Platinum',
                'slug' => 'platinum',
                'min_referrals' => 31,
                'max_referrals' => 50,
                'commission_rate' => 20.00,
                'activation_bonus' => 100.00,
                'description' => 'Platinum level with exclusive perks',
                'benefits' => [
                    'Exclusive dashboard features',
                    '$20 commission per referral',
                    '$100 activation bonus',
                    'VIP support',
                    'Monthly bonus opportunities',
                    'Exclusive webinars'
                ],
                'sort_order' => 4,
            ],
            [
                'name' => 'Diamond',
                'slug' => 'diamond',
                'min_referrals' => 51,
                'max_referrals' => 100,
                'commission_rate' => 25.00,
                'activation_bonus' => 200.00,
                'description' => 'Diamond level with top-tier benefits',
                'benefits' => [
                    'Full dashboard access',
                    '$25 commission per referral',
                    '$200 activation bonus',
                    'VIP support',
                    'Weekly bonus opportunities',
                    'Exclusive webinars',
                    'Personal account manager'
                ],
                'sort_order' => 5,
            ],
            [
                'name' => 'Elite',
                'slug' => 'elite',
                'min_referrals' => 101,
                'max_referrals' => null,
                'commission_rate' => 30.00,
                'activation_bonus' => 500.00,
                'description' => 'Elite level - the highest tier with maximum benefits',
                'benefits' => [
                    'Complete platform access',
                    '$30 commission per referral',
                    '$500 activation bonus',
                    'Dedicated support team',
                    'Daily bonus opportunities',
                    'Exclusive events and webinars',
                    'Personal account manager',
                    'Revenue sharing opportunities'
                ],
                'sort_order' => 6,
            ],
        ];

        foreach ($stages as $stage) {
            MembershipStage::create($stage);
        }
    }
}
