<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Activity extends Model
{
    use HasFactory;

    protected $fillable = [
        'stage_id',
        'type',
        'title',
        'description',
        'content',
        'requirements',
        'points',
        'duration_minutes',
        'difficulty_level',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'requirements' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Activity types
     */
    const TYPES = [
        'leadership' => 'Leadership Activities',
        'knowledge' => 'Knowledge Activities',
        'action' => 'Action Activities',
        'awareness' => 'Awareness Activities',
    ];

    /**
     * Difficulty levels
     */
    const DIFFICULTY_LEVELS = [
        'beginner' => 'Beginner',
        'intermediate' => 'Intermediate',
        'advanced' => 'Advanced',
    ];

    /**
     * Get the stage that owns the activity.
     */
    public function stage(): BelongsTo
    {
        return $this->belongsTo(MembershipStage::class, 'stage_id');
    }

    /**
     * Get the activity resources.
     */
    public function resources(): HasMany
    {
        return $this->hasMany(ActivityResource::class);
    }

    /**
     * Get the user completions for this activity.
     */
    public function completions(): HasMany
    {
        return $this->hasMany(UserActivityCompletion::class);
    }

    /**
     * Scope for active activities.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for activities by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for activities by stage.
     */
    public function scopeByStage($query, $stageId)
    {
        return $query->where('stage_id', $stageId);
    }

    /**
     * Get activities ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('title');
    }

    /**
     * Get the type display name.
     */
    public function getTypeDisplayAttribute(): string
    {
        return self::TYPES[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Get the difficulty display name.
     */
    public function getDifficultyDisplayAttribute(): string
    {
        return self::DIFFICULTY_LEVELS[$this->difficulty_level] ?? ucfirst($this->difficulty_level);
    }

    /**
     * Get the difficulty color class.
     */
    public function getDifficultyColorAttribute(): string
    {
        return match ($this->difficulty_level) {
            'beginner' => 'bg-green-100 text-green-800',
            'intermediate' => 'bg-yellow-100 text-yellow-800',
            'advanced' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get the type icon.
     */
    public function getTypeIconAttribute(): string
    {
        return match ($this->type) {
            'leadership' => 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
            'knowledge' => 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
            'action' => 'M13 10V3L4 14h7v7l9-11h-7z',
            'awareness' => 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z',
            default => 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
        };
    }

    /**
     * Get the type color class.
     */
    public function getTypeColorAttribute(): string
    {
        return match ($this->type) {
            'leadership' => 'bg-purple-100 text-purple-800',
            'knowledge' => 'bg-blue-100 text-blue-800',
            'action' => 'bg-orange-100 text-orange-800',
            'awareness' => 'bg-green-100 text-green-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Check if user has completed this activity.
     */
    public function isCompletedByUser(User $user): bool
    {
        return $this->completions()
            ->where('user_id', $user->id)
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Get user's completion for this activity.
     */
    public function getUserCompletion(User $user): ?UserActivityCompletion
    {
        return $this->completions()
            ->where('user_id', $user->id)
            ->first();
    }

    /**
     * Get completion rate for this activity.
     */
    public function getCompletionRate(): float
    {
        $totalUsers = $this->stage->activeUsers()->count();
        if ($totalUsers === 0) {
            return 0;
        }

        $completedUsers = $this->completions()
            ->where('status', 'completed')
            ->count();

        return ($completedUsers / $totalUsers) * 100;
    }

    /**
     * Create default activities for a stage.
     */
    public static function createDefaultActivitiesForStage(MembershipStage $stage): void
    {
        $defaultActivities = [
            // Leadership Activities
            [
                'type' => 'leadership',
                'title' => 'Community Leadership Challenge',
                'description' => 'Lead a community initiative focused on sustainable living practices.',
                'content' => 'Organize and lead a community project that promotes sustainable living. This could include organizing a community garden, leading a recycling drive, or coordinating a sustainability workshop.',
                'requirements' => ['Complete at least one knowledge activity', 'Have active membership for 30 days'],
                'points' => 100,
                'duration_minutes' => 480,
                'difficulty_level' => 'intermediate',
            ],
            [
                'type' => 'leadership',
                'title' => 'Mentorship Program',
                'description' => 'Mentor new members in their sustainability journey.',
                'content' => 'Guide and support new community members by sharing your knowledge and experience in sustainable living practices.',
                'requirements' => ['Complete at least 3 activities', 'Be a member for 60 days'],
                'points' => 150,
                'duration_minutes' => 720,
                'difficulty_level' => 'advanced',
            ],

            // Knowledge Activities
            [
                'type' => 'knowledge',
                'title' => 'Sustainable Living Fundamentals',
                'description' => 'Learn the basic principles of sustainable living and environmental conservation.',
                'content' => 'Study the core concepts of sustainability, including resource conservation, waste reduction, and eco-friendly practices.',
                'requirements' => [],
                'points' => 50,
                'duration_minutes' => 120,
                'difficulty_level' => 'beginner',
            ],
            [
                'type' => 'knowledge',
                'title' => 'Climate Change and Personal Impact',
                'description' => 'Understand climate change and how individual actions can make a difference.',
                'content' => 'Explore the science of climate change and learn practical ways to reduce your carbon footprint.',
                'requirements' => ['Complete Sustainable Living Fundamentals'],
                'points' => 75,
                'duration_minutes' => 180,
                'difficulty_level' => 'intermediate',
            ],

            // Action Activities
            [
                'type' => 'action',
                'title' => 'Zero Waste Week Challenge',
                'description' => 'Implement zero waste practices for one week and document your journey.',
                'content' => 'Commit to producing zero waste for one week. Document your strategies, challenges, and successes.',
                'requirements' => ['Complete at least one knowledge activity'],
                'points' => 80,
                'duration_minutes' => 10080, // 1 week
                'difficulty_level' => 'intermediate',
            ],
            [
                'type' => 'action',
                'title' => 'Energy Efficiency Audit',
                'description' => 'Conduct a comprehensive energy audit of your home and implement improvements.',
                'content' => 'Assess your home\'s energy usage and implement at least 3 energy-saving measures.',
                'requirements' => [],
                'points' => 60,
                'duration_minutes' => 240,
                'difficulty_level' => 'beginner',
            ],

            // Awareness Activities
            [
                'type' => 'awareness',
                'title' => 'Social Media Sustainability Campaign',
                'description' => 'Create and share sustainability content on social media to raise awareness.',
                'content' => 'Develop a week-long social media campaign promoting sustainable living practices.',
                'requirements' => ['Complete at least one knowledge activity'],
                'points' => 70,
                'duration_minutes' => 300,
                'difficulty_level' => 'beginner',
            ],
            [
                'type' => 'awareness',
                'title' => 'Community Workshop Presentation',
                'description' => 'Organize and present a sustainability workshop in your local community.',
                'content' => 'Plan and deliver a presentation on sustainable living to your local community or workplace.',
                'requirements' => ['Complete at least 2 knowledge activities', 'Complete at least 1 action activity'],
                'points' => 120,
                'duration_minutes' => 360,
                'difficulty_level' => 'advanced',
            ],
        ];

        foreach ($defaultActivities as $index => $activityData) {
            $activityData['stage_id'] = $stage->id;
            $activityData['sort_order'] = $index + 1;
            self::create($activityData);
        }
    }
}
