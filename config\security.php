<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security settings for the Divine Lights platform.
    | These settings help protect against common security vulnerabilities.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Authentication Security
    |--------------------------------------------------------------------------
    */
    'auth' => [
        'max_login_attempts' => env('MAX_LOGIN_ATTEMPTS', 5),
        'lockout_duration' => env('LOCKOUT_DURATION', 900), // 15 minutes
        'password_min_length' => env('PASSWORD_MIN_LENGTH', 8),
        'require_strong_passwords' => env('REQUIRE_STRONG_PASSWORDS', true),
        'session_timeout' => env('SESSION_TIMEOUT', 1800), // 30 minutes
        'two_factor_enabled' => env('TWO_FACTOR_ENABLED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'api_requests_per_minute' => env('API_RATE_LIMIT', 60),
        'login_attempts_per_minute' => env('LOGIN_RATE_LIMIT', 5),
        'registration_attempts_per_hour' => env('REGISTRATION_RATE_LIMIT', 3),
        'password_reset_attempts_per_hour' => env('PASSWORD_RESET_RATE_LIMIT', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Protection
    |--------------------------------------------------------------------------
    */
    'data_protection' => [
        'encrypt_sensitive_data' => env('ENCRYPT_SENSITIVE_DATA', true),
        'hash_user_ips' => env('HASH_USER_IPS', true),
        'log_sensitive_actions' => env('LOG_SENSITIVE_ACTIONS', true),
        'gdpr_compliance' => env('GDPR_COMPLIANCE', true),
        'data_retention_days' => env('DATA_RETENTION_DAYS', 365),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    */
    'headers' => [
        'force_https' => env('FORCE_HTTPS', true),
        'hsts_max_age' => env('HSTS_MAX_AGE', 31536000), // 1 year
        'content_security_policy' => env('CSP_ENABLED', true),
        'x_frame_options' => 'DENY',
        'x_content_type_options' => 'nosniff',
        'x_xss_protection' => '1; mode=block',
        'referrer_policy' => 'strict-origin-when-cross-origin',
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Security
    |--------------------------------------------------------------------------
    */
    'file_uploads' => [
        'max_file_size' => env('MAX_FILE_SIZE', 10240), // 10MB in KB
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'scan_for_malware' => env('SCAN_UPLOADS_FOR_MALWARE', false),
        'quarantine_suspicious_files' => env('QUARANTINE_SUSPICIOUS_FILES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Security
    |--------------------------------------------------------------------------
    */
    'database' => [
        'encrypt_connections' => env('DB_ENCRYPT_CONNECTIONS', true),
        'log_queries' => env('LOG_DATABASE_QUERIES', false),
        'prevent_sql_injection' => true,
        'use_prepared_statements' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Admin Security
    |--------------------------------------------------------------------------
    */
    'admin' => [
        'require_2fa' => env('ADMIN_REQUIRE_2FA', true),
        'ip_whitelist_enabled' => env('ADMIN_IP_WHITELIST_ENABLED', false),
        'allowed_ips' => env('ADMIN_ALLOWED_IPS', ''),
        'session_timeout' => env('ADMIN_SESSION_TIMEOUT', 900), // 15 minutes
        'log_all_actions' => env('LOG_ADMIN_ACTIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Financial Security
    |--------------------------------------------------------------------------
    */
    'financial' => [
        'encrypt_financial_data' => env('ENCRYPT_FINANCIAL_DATA', true),
        'require_confirmation_for_withdrawals' => env('REQUIRE_WITHDRAWAL_CONFIRMATION', true),
        'max_daily_withdrawal' => env('MAX_DAILY_WITHDRAWAL', 10000),
        'suspicious_activity_threshold' => env('SUSPICIOUS_ACTIVITY_THRESHOLD', 5000),
        'audit_all_transactions' => env('AUDIT_ALL_TRANSACTIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Alerts
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'failed_login_alert_threshold' => env('FAILED_LOGIN_ALERT_THRESHOLD', 10),
        'suspicious_activity_alerts' => env('SUSPICIOUS_ACTIVITY_ALERTS', true),
        'security_scan_frequency' => env('SECURITY_SCAN_FREQUENCY', 'daily'),
        'log_retention_days' => env('SECURITY_LOG_RETENTION_DAYS', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Security
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'encrypt_backups' => env('ENCRYPT_BACKUPS', true),
        'backup_frequency' => env('BACKUP_FREQUENCY', 'daily'),
        'offsite_backup_enabled' => env('OFFSITE_BACKUP_ENABLED', true),
        'backup_retention_days' => env('BACKUP_RETENTION_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Content Security
    |--------------------------------------------------------------------------
    */
    'content' => [
        'sanitize_user_input' => env('SANITIZE_USER_INPUT', true),
        'validate_file_types' => env('VALIDATE_FILE_TYPES', true),
        'prevent_xss' => env('PREVENT_XSS', true),
        'content_filtering_enabled' => env('CONTENT_FILTERING_ENABLED', true),
    ],
];
