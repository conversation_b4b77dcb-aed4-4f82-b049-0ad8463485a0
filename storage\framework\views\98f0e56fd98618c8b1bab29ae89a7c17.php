<?php $__env->startSection('title', $user->name . ' - Profile'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <h1 class="text-3xl font-bold text-gray-900"><?php echo e($user->name); ?>'s Profile</h1>
            <?php if(Auth::id() === $user->id): ?>
            <a href="<?php echo e(url('/profile/edit')); ?>"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Edit Profile
            </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Info -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">About</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-6">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center">
                            <span class="text-2xl font-bold text-indigo-600"><?php echo e(substr($user->name, 0, 1)); ?></span>
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900"><?php echo e($user->name); ?></h4>
                            <p class="text-gray-600">
                                <?php if($user->isEarthFriendlyMember()): ?>
                                    Earth-Friendly Member
                                <?php else: ?>
                                    Light Member
                                <?php endif; ?>
                            </p>
                            <?php if($user->location): ?>
                            <p class="text-sm text-gray-500 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e($user->location); ?>

                            </p>
                            <?php endif; ?>

                            <!-- Divine Lights Stars -->
                            <div class="mt-3">
                                <div class="text-xs text-gray-500 mb-1">Divine Lights Progress</div>
                                <?php if (isset($component)) { $__componentOriginalda3b72888c1a99858652a21cb00b9c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalda3b72888c1a99858652a21cb00b9c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.divine-lights-stars','data' => ['user' => $user,'showGreenStars' => false,'size' => 'xs']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('divine-lights-stars'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'showGreenStars' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'size' => 'xs']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalda3b72888c1a99858652a21cb00b9c65)): ?>
<?php $attributes = $__attributesOriginalda3b72888c1a99858652a21cb00b9c65; ?>
<?php unset($__attributesOriginalda3b72888c1a99858652a21cb00b9c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalda3b72888c1a99858652a21cb00b9c65)): ?>
<?php $component = $__componentOriginalda3b72888c1a99858652a21cb00b9c65; ?>
<?php unset($__componentOriginalda3b72888c1a99858652a21cb00b9c65); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php if($user->bio): ?>
                    <div class="mb-6">
                        <h5 class="text-sm font-medium text-gray-900 mb-2">Bio</h5>
                        <p class="text-gray-700"><?php echo e($user->bio); ?></p>
                    </div>
                    <?php endif; ?>

                    <?php
                        $hasApprovedRequest = false; // Check if current user has approved request for this profile
                        $showPrivateInfo = (Auth::id() === $user->id) || $hasApprovedRequest;
                    ?>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php if($user->email && $showPrivateInfo): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Email</h5>
                            <p class="text-gray-700"><?php echo e($user->email); ?></p>
                        </div>
                        <?php elseif($user->email && !$showPrivateInfo): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Email</h5>
                            <p class="text-gray-500 italic">Hidden - Request access to view</p>
                        </div>
                        <?php endif; ?>

                        <?php if($user->phone && $showPrivateInfo): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Phone</h5>
                            <p class="text-gray-700"><?php echo e($user->phone); ?></p>
                        </div>
                        <?php elseif($user->phone && !$showPrivateInfo): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Phone</h5>
                            <p class="text-gray-500 italic">Hidden - Request access to view</p>
                        </div>
                        <?php endif; ?>

                        <?php if($user->date_of_birth && $showPrivateInfo): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Date of Birth</h5>
                            <p class="text-gray-700"><?php echo e($user->date_of_birth->format('F j, Y')); ?></p>
                        </div>
                        <?php elseif($user->date_of_birth && !$showPrivateInfo): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Date of Birth</h5>
                            <p class="text-gray-500 italic">Hidden - Request access to view</p>
                        </div>
                        <?php endif; ?>

                        <?php if($user->address && $showPrivateInfo): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Address</h5>
                            <p class="text-gray-700"><?php echo e($user->address); ?></p>
                        </div>
                        <?php elseif($user->address && !$showPrivateInfo): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Address</h5>
                            <p class="text-gray-500 italic">Hidden - Request access to view</p>
                        </div>
                        <?php endif; ?>

                        <?php if($user->website): ?>
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Website</h5>
                            <a href="<?php echo e($user->website); ?>" target="_blank" class="text-indigo-600 hover:text-indigo-500">
                                <?php echo e($user->website); ?>

                            </a>
                        </div>
                        <?php endif; ?>

                        <div>
                            <h5 class="text-sm font-medium text-gray-900">Member Since</h5>
                            <p class="text-gray-700"><?php echo e($user->created_at->format('F Y')); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Divine Lights Rank System -->
            <div class="bg-white shadow-lg rounded-lg border border-gray-200 p-8 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-gray-900">Divine Lights Rank System</h3>
                    <div class="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full"><?php echo e($user->name); ?>'s Progress</div>
                </div>

                <!-- All Stages Overview -->
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-8">
                    <?php for($stage = 1; $stage <= 6; $stage++): ?>
                        <div class="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-4 text-center">
                            <div class="text-sm font-bold text-gray-900 mb-3">Stage <?php echo e($stage); ?></div>
                            <?php if (isset($component)) { $__componentOriginalda3b72888c1a99858652a21cb00b9c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalda3b72888c1a99858652a21cb00b9c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.divine-lights-stars','data' => ['user' => $user,'stage' => $stage,'showWhiteStars' => false,'size' => 'xs']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('divine-lights-stars'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'stage' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($stage),'showWhiteStars' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'size' => 'xs']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalda3b72888c1a99858652a21cb00b9c65)): ?>
<?php $attributes = $__attributesOriginalda3b72888c1a99858652a21cb00b9c65; ?>
<?php unset($__attributesOriginalda3b72888c1a99858652a21cb00b9c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalda3b72888c1a99858652a21cb00b9c65)): ?>
<?php $component = $__componentOriginalda3b72888c1a99858652a21cb00b9c65; ?>
<?php unset($__componentOriginalda3b72888c1a99858652a21cb00b9c65); ?>
<?php endif; ?>
                            <div class="text-xs text-gray-600 mt-2">
                                <?php if($stage == 1): ?> Personal
                                <?php elseif($stage == 2): ?> Family
                                <?php elseif($stage == 3): ?> Community
                                <?php elseif($stage == 4): ?> Social
                                <?php elseif($stage == 5): ?> Global
                                <?php elseif($stage == 6): ?> Universal
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>

                <!-- Fulfillment Stars -->
                <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-lg font-bold text-gray-900 mb-2">Fulfillment Achievement</h4>
                            <p class="text-sm text-gray-700">Complete all stages to unlock white stars and achieve full enlightenment</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <?php if (isset($component)) { $__componentOriginalda3b72888c1a99858652a21cb00b9c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalda3b72888c1a99858652a21cb00b9c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.divine-lights-stars','data' => ['user' => $user,'showGreenStars' => false,'size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('divine-lights-stars'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'showGreenStars' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'size' => 'sm']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalda3b72888c1a99858652a21cb00b9c65)): ?>
<?php $attributes = $__attributesOriginalda3b72888c1a99858652a21cb00b9c65; ?>
<?php unset($__attributesOriginalda3b72888c1a99858652a21cb00b9c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalda3b72888c1a99858652a21cb00b9c65)): ?>
<?php $component = $__componentOriginalda3b72888c1a99858652a21cb00b9c65; ?>
<?php unset($__componentOriginalda3b72888c1a99858652a21cb00b9c65); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Progress Summary -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-700">3</div>
                        <div class="text-sm text-green-600 font-medium">Stages Activated</div>
                    </div>
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-700">18</div>
                        <div class="text-sm text-blue-600 font-medium">Green Stars Earned</div>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-purple-700">2</div>
                        <div class="text-sm text-purple-600 font-medium">White Stars Achieved</div>
                    </div>
                </div>
            </div>

            <!-- Activated Stages -->
            <?php if($user->isLightMember() && $activatedStages->count() > 0): ?>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Activated Stages</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = $activatedStages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-green-900"><?php echo e($activation->membershipStage->name); ?></h4>
                                    <p class="text-xs text-green-700">Activated <?php echo e($activation->activated_at->format('M j, Y')); ?></p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-green-900">$<?php echo e($activation->membershipStage->commission_rate); ?></p>
                                    <p class="text-xs text-green-700">per referral</p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Achievements -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Achievements</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Member Achievement -->
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900">Community Member</h4>
                            <p class="text-xs text-gray-500">Joined the sustainable living community</p>
                        </div>

                        <?php if($user->isLightMember()): ?>
                        <!-- Light Member Achievement -->
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900">Light Member</h4>
                            <p class="text-xs text-gray-500">Activated first stage</p>
                        </div>
                        <?php endif; ?>

                        <?php if($stats['total_referrals'] > 0): ?>
                        <!-- Referrer Achievement -->
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                </svg>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900">Community Builder</h4>
                            <p class="text-xs text-gray-500">Referred <?php echo e($stats['total_referrals']); ?> member<?php echo e($stats['total_referrals'] !== 1 ? 's' : ''); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Stats</h3>
                </div>
                <div class="p-6 space-y-6">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-green-600">$<?php echo e(number_format($stats['total_earnings'], 2)); ?></p>
                        <p class="text-sm text-gray-500">Total Earnings</p>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <p class="text-lg font-semibold text-gray-900"><?php echo e($stats['total_referrals']); ?></p>
                            <p class="text-xs text-gray-500">Total Referrals</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-semibold text-gray-900"><?php echo e($stats['member_since']->format('M Y')); ?></p>
                            <p class="text-xs text-gray-500">Member Since</p>
                        </div>
                    </div>

                    <?php if($user->isLightMember()): ?>
                    <div class="text-center pt-4 border-t border-gray-200">
                        <p class="text-lg font-semibold text-indigo-600"><?php echo e($stats['activated_stages_count']); ?></p>
                        <p class="text-xs text-gray-500">Activated Stages</p>
                    </div>
                    <?php endif; ?>

                    <?php if($user->light_member_activated_at): ?>
                    <div class="text-center pt-4 border-t border-gray-200">
                        <p class="text-sm text-gray-900">Light Member Since</p>
                        <p class="text-xs text-gray-500"><?php echo e($user->light_member_activated_at->format('M j, Y')); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if(Auth::id() !== $user->id): ?>
            <!-- Contact Actions -->
            <div class="bg-white shadow rounded-lg mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Connect</h3>
                </div>
                <div class="p-6 space-y-4">
                    <button onclick="sendMessage(<?php echo e($user->id); ?>)" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        Send Message
                    </button>

                    <?php
                        $hasApprovedRequest = false; // Check if current user has approved request
                        $hasPendingRequest = false; // Check if current user has pending request
                    ?>

                    <?php if(!$hasApprovedRequest && !$hasPendingRequest): ?>
                        <button onclick="requestProfileAccess(<?php echo e($user->id); ?>)" class="w-full inline-flex justify-center items-center px-4 py-2 border border-indigo-300 text-sm font-medium rounded-md text-indigo-700 bg-indigo-50 hover:bg-indigo-100">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                            </svg>
                            Request Profile Information
                        </button>
                    <?php elseif($hasPendingRequest): ?>
                        <div class="w-full inline-flex justify-center items-center px-4 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-yellow-50">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            Request Pending Approval
                        </div>
                    <?php else: ?>
                        <div class="w-full inline-flex justify-center items-center px-4 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Profile Access Granted
                        </div>
                    <?php endif; ?>

                    <?php if($user->website): ?>
                    <a href="<?php echo e($user->website); ?>" target="_blank"
                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clip-rule="evenodd"></path>
                        </svg>
                        Visit Website
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function sendMessage(userId) {
    // For now, redirect to a messaging page or show a modal
    // This would be implemented with a proper messaging system
    alert('Messaging functionality will be implemented. User ID: ' + userId);

    // Example implementation:
    // window.location.href = '/messages/compose/' + userId;
    // or open a modal for composing message
}

function requestProfileAccess(userId) {
    if (confirm('Request access to view this user\'s private profile information?')) {
        fetch('/profile/request-access', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                user_id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Profile access request sent successfully!');
                location.reload();
            } else {
                alert('Error sending request: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while sending the request.');
        });
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/profile/show.blade.php ENDPATH**/ ?>