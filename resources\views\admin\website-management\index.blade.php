@extends('layouts.admin')

@section('title', 'Website Management')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Website Management</h1>
                <p class="text-gray-600 mt-1">Complete website management and configuration</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="publishChanges()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Publish Changes
                </button>
                <button onclick="saveWebsiteSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Website Status -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Website Status</h3>
                    <p class="text-sm text-green-600">Online</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Total Users</h3>
                    <p class="text-sm text-gray-600">24,847</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Page Views</h3>
                    <p class="text-sm text-gray-600">1.2M</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Performance</h3>
                    <p class="text-sm text-gray-600">Excellent</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'general' }">
                <button @click="activeTab = 'general'" :class="activeTab === 'general' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    General Settings
                </button>
                <button @click="activeTab = 'seo'" :class="activeTab === 'seo' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    SEO & Meta
                </button>
                <button @click="activeTab = 'security'" :class="activeTab === 'security' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Security
                </button>
                <button @click="activeTab = 'performance'" :class="activeTab === 'performance' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Performance
                </button>
                <button @click="activeTab = 'integrations'" :class="activeTab === 'integrations' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Integrations
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'general' }">
        <!-- General Settings Tab -->
        <div x-show="activeTab === 'general'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">General Website Settings</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Basic Information -->
                        <div class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900">Basic Information</h4>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Website Title</label>
                                <input type="text" value="Divine Lights Platform" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Website Description</label>
                                <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2">A comprehensive platform for awareness and spiritual growth through six stages of activation.</textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Website URL</label>
                                <input type="url" value="https://divinelights.com" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                                <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="UTC">UTC</option>
                                    <option value="America/New_York">Eastern Time</option>
                                    <option value="America/Chicago">Central Time</option>
                                    <option value="America/Denver">Mountain Time</option>
                                    <option value="America/Los_Angeles" selected>Pacific Time</option>
                                </select>
                            </div>
                        </div>

                        <!-- Website Features -->
                        <div class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900">Website Features</h4>
                            
                            <div class="space-y-4">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">User Registration</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Email Verification</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Referral System</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Stage Activations</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Community Features</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">VIP Club System</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Virtual Tools</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Multilingual Support</span>
                                </label>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Default Language</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="en" selected>English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                    <option value="it">Italian</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Default Currency</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="USD" selected>USD - US Dollar</option>
                                    <option value="EUR">EUR - Euro</option>
                                    <option value="GBP">GBP - British Pound</option>
                                    <option value="CAD">CAD - Canadian Dollar</option>
                                    <option value="AUD">AUD - Australian Dollar</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SEO & Meta Tab -->
        <div x-show="activeTab === 'seo'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">SEO & Meta Settings</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                            <input type="text" value="Divine Lights Platform - Spiritual Awareness & Growth" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <p class="text-xs text-gray-500 mt-1">Recommended: 50-60 characters</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                            <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2">Join Divine Lights Platform for a transformative journey through six stages of awareness. Build community, earn rewards, and unlock your spiritual potential.</textarea>
                            <p class="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                            <input type="text" value="spiritual growth, awareness, community, referral system, divine lights" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Open Graph Image</label>
                            <div class="flex items-center space-x-3">
                                <img src="/images/og-image.jpg" alt="OG Image" class="h-20 w-32 object-cover rounded">
                                <input type="file" accept="image/*" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Recommended: 1200x630 pixels</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Google Analytics ID</label>
                            <input type="text" value="GA-XXXXXXXXX-X" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Google Search Console</label>
                            <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Paste your Google Search Console verification code here"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Tab -->
        <div x-show="activeTab === 'security'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Security Settings</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Authentication</h4>
                                <div class="space-y-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Two-Factor Authentication</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Strong Password Requirements</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300">
                                        <span class="ml-2 text-sm text-gray-700">Login Rate Limiting</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Session Timeout</span>
                                    </label>
                                </div>
                            </div>

                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Data Protection</h4>
                                <div class="space-y-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">SSL/HTTPS Enforcement</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Data Encryption</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">GDPR Compliance</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300">
                                        <span class="ml-2 text-sm text-gray-700">Automatic Backups</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                            <input type="number" value="30" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Login Attempts</label>
                            <input type="number" value="5" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function publishChanges() {
    if (confirm('Publish all changes to the live website?')) {
        alert('Changes published successfully!');
    }
}

function saveWebsiteSettings() {
    alert('Website settings saved successfully!');
}
</script>
@endsection
