<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create user stage activations table for tracking multiple stage activations
        Schema::create('user_stage_activations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('membership_stage_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->decimal('activation_bonus_paid', 10, 2)->default(0);
            $table->timestamp('activated_at');
            $table->timestamp('deactivated_at')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'membership_stage_id']);
            $table->index(['user_id', 'is_active']);
        });

        // Update commissions table to track which stage earned the commission
        Schema::table('commissions', function (Blueprint $table) {
            $table->foreignId('stage_activation_id')->nullable()->after('user_id')->constrained('user_stage_activations')->onDelete('set null');
            $table->string('stage_slug')->nullable()->after('stage_activation_id');
        });

        // Remove single membership_stage from users table since we now support multiple
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('membership_stage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('commissions', function (Blueprint $table) {
            $table->dropForeign(['stage_activation_id']);
            $table->dropColumn(['stage_activation_id', 'stage_slug']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->enum('membership_stage', ['bronze', 'silver', 'gold', 'platinum', 'diamond', 'elite'])->nullable()->after('membership_tier');
        });

        Schema::dropIfExists('user_stage_activations');
    }
};
