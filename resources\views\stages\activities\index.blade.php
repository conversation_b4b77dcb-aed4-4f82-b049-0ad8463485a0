@extends('layouts.app')

@section('title', 'Activities - ' . $stage->name)

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                        <svg class="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                        </svg>
                        Dashboard
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-3 h-3 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 6 10">
                            <path fill-rule="evenodd" d="m1 9 4-4-4-4" clip-rule="evenodd"/>
                        </svg>
                        <a href="{{ route('stages.area', $stage) }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600">{{ $stage->name }}</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-3 h-3 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 6 10">
                            <path fill-rule="evenodd" d="m1 9 4-4-4-4" clip-rule="evenodd"/>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-gray-500">Activities</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="mt-4">
            <h1 class="text-3xl font-bold text-gray-900">{{ $stage->name }} Activities</h1>
            <p class="mt-2 text-gray-600">Engage in meaningful activities to grow your sustainable living journey</p>
        </div>
    </div>

    <!-- Activity Type Filter -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <a href="{{ route('stages.activities.index', [$stage, 'type' => 'all']) }}" 
                   class="py-2 px-1 border-b-2 font-medium text-sm {{ $type === 'all' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    All Activities
                </a>
                @foreach($activityTypes as $typeKey => $typeDisplay)
                <a href="{{ route('stages.activities.index', [$stage, 'type' => $typeKey]) }}" 
                   class="py-2 px-1 border-b-2 font-medium text-sm {{ $type === $typeKey ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    {{ $typeDisplay }}
                </a>
                @endforeach
            </nav>
        </div>
    </div>

    <!-- Activities Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($activities as $activity)
        @php
            $completion = $completions->get($activity->id);
            $isCompleted = $completion && $completion->isCompleted();
            $isInProgress = $completion && $completion->status === 'in_progress';
        @endphp
        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
            <!-- Activity Header -->
            <div class="p-6">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $activity->type_color }}">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="{{ $activity->type_icon }}"></path>
                                </svg>
                                {{ $activity->type_display }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $activity->difficulty_color }}">
                                {{ $activity->difficulty_display }}
                            </span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $activity->title }}</h3>
                        <p class="text-gray-600 text-sm mb-4">{{ Str::limit($activity->description, 120) }}</p>
                    </div>
                </div>

                <!-- Activity Stats -->
                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        @if($activity->duration_minutes)
                            {{ $activity->duration_minutes >= 60 ? round($activity->duration_minutes / 60) . 'h' : $activity->duration_minutes . 'm' }}
                        @else
                            Flexible
                        @endif
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        {{ $activity->points }} points
                    </div>
                </div>

                <!-- Progress Bar -->
                @if($completion)
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm mb-1">
                        <span class="text-gray-600">Progress</span>
                        <span class="font-medium {{ $completion->status_color }}">{{ $completion->status_display }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full transition-all duration-300" style="width: {{ $completion->getCompletionPercentage() }}%"></div>
                    </div>
                </div>
                @endif

                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <a href="{{ route('stages.activities.show', [$stage, $activity]) }}" 
                       class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        View Details
                    </a>
                    
                    @if($isCompleted)
                    <span class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-green-300 shadow-sm text-sm font-medium rounded-md text-green-700 bg-green-50">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Completed
                    </span>
                    @elseif($isInProgress)
                    <a href="{{ route('stages.activities.show', [$stage, $activity]) }}" 
                       class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Continue
                    </a>
                    @else
                    <form method="POST" action="{{ route('stages.activities.start', [$stage, $activity]) }}" class="flex-1">
                        @csrf
                        <button type="submit" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Start Activity
                        </button>
                    </form>
                    @endif
                </div>
            </div>
        </div>
        @empty
        <div class="col-span-full">
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 17H9.154a3.374 3.374 0 00-1.849-.553l-.548-.547z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No activities available</h3>
                <p class="mt-1 text-sm text-gray-500">
                    @if($type !== 'all')
                        No {{ strtolower($activityTypes[$type]) }} found for this stage.
                    @else
                        No activities have been created for this stage yet.
                    @endif
                </p>
                @if($type !== 'all')
                <div class="mt-6">
                    <a href="{{ route('stages.activities.index', $stage) }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        View All Activities
                    </a>
                </div>
                @endif
            </div>
        </div>
        @endforelse
    </div>

    <!-- Quick Stats -->
    @if($activities->count() > 0)
    <div class="mt-12 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Your Progress</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            @php
                $totalActivities = $activities->count();
                $completedActivities = $completions->where('status', 'completed')->count() + $completions->where('status', 'verified')->count();
                $inProgressActivities = $completions->where('status', 'in_progress')->count();
                $totalPoints = $completions->sum('points_earned');
            @endphp
            
            <div class="text-center">
                <div class="text-2xl font-bold text-indigo-600">{{ $completedActivities }}</div>
                <div class="text-sm text-gray-500">Completed</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ $inProgressActivities }}</div>
                <div class="text-sm text-gray-500">In Progress</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-600">{{ $totalActivities }}</div>
                <div class="text-sm text-gray-500">Total Available</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600">{{ $totalPoints }}</div>
                <div class="text-sm text-gray-500">Points Earned</div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
