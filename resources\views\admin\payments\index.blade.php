@extends('layouts.admin')

@section('title', 'Payment Gateways')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Payment Gateway Management</h1>
        <p class="mt-2 text-gray-600">Configure and manage payment processing systems</p>
    </div>

    <!-- Payment Gateways -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Available Payment Gateways</h3>
        </div>
        <div class="divide-y divide-gray-200">
            @forelse($gateways as $gateway)
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                @if($gateway->name === 'stripe')
                                <svg class="h-8 w-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z"/>
                                </svg>
                                @elseif($gateway->name === 'paypal')
                                <svg class="h-8 w-8 text-blue-800" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.028-.026.056-.052.08-.744 6.137-4.644 8.51-9.83 8.51h-2.65c-.524 0-.968.382-1.05.9l-1.12 7.106h4.184a.641.641 0 0 0 .633-.74l.033-.205.629-3.98.04-.26a.641.641 0 0 1 .633-.74h.398c3.66 0 6.526-1.487 7.36-5.781.348-1.797.167-3.297-.784-4.349z"/>
                                </svg>
                                @elseif($gateway->name === 'paystack')
                                <svg class="h-8 w-8 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M0 16h8v8H0zM8 8h8v8H8zM16 0h8v8h-8z"/>
                                </svg>
                                @else
                                <svg class="h-8 w-8 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                                @endif
                            </div>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-medium text-gray-900">{{ $gateway->display_name }}</h4>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $gateway->status_color }}">
                                    {{ $gateway->status_text }}
                                </span>
                                @if($gateway->supported_currencies)
                                <span class="text-sm text-gray-500">
                                    Supports: {{ implode(', ', $gateway->supported_currencies) }}
                                </span>
                                @endif
                            </div>
                            <div class="text-sm text-gray-500 mt-1">
                                Fee: {{ $gateway->transaction_fee_percentage }}% + ${{ $gateway->transaction_fee_fixed }}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="configureGateway('{{ $gateway->id }}', '{{ $gateway->name }}', '{{ $gateway->display_name }}')" 
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                            </svg>
                            Configure
                        </button>
                        <form method="POST" action="{{ route('admin.payments.toggle', $gateway) }}" class="inline">
                            @csrf
                            @method('PUT')
                            <button type="submit" 
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white {{ $gateway->is_active ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' }} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                {{ $gateway->is_active ? 'Disable' : 'Enable' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            @empty
            <div class="p-6 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No payment gateways</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by setting up your first payment gateway.</p>
                <div class="mt-6">
                    <button type="button" onclick="initializeGateways()" 
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        Initialize Payment Gateways
                    </button>
                </div>
            </div>
            @endforelse
        </div>
    </div>

    <!-- Payment Statistics -->
    @if($gateways->count() > 0)
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Transactions</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['total_transactions'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Volume</dt>
                            <dd class="text-lg font-medium text-gray-900">${{ number_format($stats['total_volume'] ?? 0, 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Success Rate</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['success_rate'] ?? 0, 1) }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Gateway Configuration Modal -->
<div id="gatewayModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Configure Payment Gateway</h3>
            <form id="gatewayForm" method="POST">
                @csrf
                @method('PUT')
                <div class="mt-4 space-y-4" id="configFields">
                    <!-- Dynamic fields will be inserted here -->
                </div>
                <div class="flex items-center justify-between mt-6">
                    <button type="button" onclick="hideGatewayModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Save Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function configureGateway(gatewayId, gatewayName, displayName) {
    document.getElementById('modalTitle').textContent = `Configure ${displayName}`;
    document.getElementById('gatewayForm').action = `/admin/payments/${gatewayId}/configure`;
    
    const configFields = document.getElementById('configFields');
    configFields.innerHTML = '';
    
    // Generate fields based on gateway type
    const fields = getGatewayFields(gatewayName);
    fields.forEach(field => {
        const fieldHtml = `
            <div>
                <label for="${field.name}" class="block text-sm font-medium text-gray-700">${field.label}</label>
                <input type="${field.type}" id="${field.name}" name="${field.name}" 
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       ${field.required ? 'required' : ''} placeholder="${field.placeholder || ''}">
            </div>
        `;
        configFields.innerHTML += fieldHtml;
    });
    
    document.getElementById('gatewayModal').classList.remove('hidden');
}

function getGatewayFields(gatewayName) {
    const fieldSets = {
        stripe: [
            { name: 'public_key', label: 'Publishable Key', type: 'text', required: true, placeholder: 'pk_test_...' },
            { name: 'secret_key', label: 'Secret Key', type: 'password', required: true, placeholder: 'sk_test_...' },
            { name: 'webhook_secret', label: 'Webhook Secret', type: 'password', required: false, placeholder: 'whsec_...' }
        ],
        paypal: [
            { name: 'client_id', label: 'Client ID', type: 'text', required: true, placeholder: 'Your PayPal Client ID' },
            { name: 'client_secret', label: 'Client Secret', type: 'password', required: true, placeholder: 'Your PayPal Client Secret' },
            { name: 'webhook_id', label: 'Webhook ID', type: 'text', required: false, placeholder: 'Your PayPal Webhook ID' }
        ],
        paystack: [
            { name: 'public_key', label: 'Public Key', type: 'text', required: true, placeholder: 'pk_test_...' },
            { name: 'secret_key', label: 'Secret Key', type: 'password', required: true, placeholder: 'sk_test_...' }
        ]
    };
    
    return fieldSets[gatewayName] || [];
}

function hideGatewayModal() {
    document.getElementById('gatewayModal').classList.add('hidden');
}

function initializeGateways() {
    if (confirm('This will create default payment gateway configurations. Continue?')) {
        fetch('/admin/payments/initialize', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to initialize payment gateways');
            }
        });
    }
}
</script>
@endsection
