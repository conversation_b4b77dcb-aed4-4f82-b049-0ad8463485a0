<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SiteSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'sort_order',
        'is_public',
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Setting types
     */
    const TYPES = [
        'text' => 'Text',
        'textarea' => 'Textarea',
        'image' => 'Image',
        'boolean' => 'Boolean',
        'json' => 'JSON',
        'number' => 'Number',
        'email' => 'Email',
        'url' => 'URL',
    ];

    /**
     * Setting groups
     */
    const GROUPS = [
        'general' => 'General',
        'company' => 'Company Information',
        'pages' => 'Page Content',
        'pricing' => 'Pricing & Plans',
        'appearance' => 'Appearance',
        'email' => 'Email Settings',
        'social' => 'Social Media',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            Cache::forget('site_settings');
        });

        static::deleted(function () {
            Cache::forget('site_settings');
        });
    }

    /**
     * Get a setting value by key.
     */
    public static function get(string $key, $default = null)
    {
        $settings = Cache::remember('site_settings', 3600, function () {
            return self::pluck('value', 'key')->toArray();
        });

        return $settings[$key] ?? $default;
    }

    /**
     * Set a setting value.
     */
    public static function set(string $key, $value): void
    {
        self::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );
    }

    /**
     * Get all public settings (for frontend).
     */
    public static function getPublic(): array
    {
        return Cache::remember('public_site_settings', 3600, function () {
            return self::where('is_public', true)
                ->pluck('value', 'key')
                ->toArray();
        });
    }

    /**
     * Get settings by group.
     */
    public static function getByGroup(string $group): array
    {
        return self::where('group', $group)
            ->orderBy('sort_order')
            ->orderBy('label')
            ->get()
            ->toArray();
    }

    /**
     * Get the formatted value based on type.
     */
    public function getFormattedValueAttribute()
    {
        return match ($this->type) {
            'boolean' => (bool) $this->value,
            'number' => (float) $this->value,
            'json' => json_decode($this->value, true),
            default => $this->value,
        };
    }

    /**
     * Create default settings.
     */
    public static function createDefaults(): void
    {
        $defaults = [
            // Company Information
            [
                'key' => 'company_name',
                'value' => 'Sustainable Living Platform',
                'type' => 'text',
                'group' => 'company',
                'label' => 'Company Name',
                'description' => 'The name of your company/organization',
                'sort_order' => 1,
                'is_public' => true,
            ],
            [
                'key' => 'company_logo',
                'value' => null,
                'type' => 'image',
                'group' => 'company',
                'label' => 'Company Logo',
                'description' => 'Upload your company logo',
                'sort_order' => 2,
                'is_public' => true,
            ],
            [
                'key' => 'company_description',
                'value' => 'Join our sustainable living community and make a positive impact on the environment.',
                'type' => 'textarea',
                'group' => 'company',
                'label' => 'Company Description',
                'description' => 'Brief description of your company',
                'sort_order' => 3,
                'is_public' => true,
            ],

            // Page Content
            [
                'key' => 'welcome_title',
                'value' => 'Welcome to Your Sustainable Living Journey',
                'type' => 'text',
                'group' => 'pages',
                'label' => 'Welcome Page Title',
                'description' => 'Main title on the welcome page',
                'sort_order' => 1,
                'is_public' => true,
            ],
            [
                'key' => 'welcome_subtitle',
                'value' => 'Connect, Learn, and Grow with Like-minded Individuals',
                'type' => 'text',
                'group' => 'pages',
                'label' => 'Welcome Page Subtitle',
                'description' => 'Subtitle on the welcome page',
                'sort_order' => 2,
                'is_public' => true,
            ],
            [
                'key' => 'welcome_description',
                'value' => 'Discover a community dedicated to sustainable living, environmental consciousness, and positive change. Join thousands of members on their journey towards a more sustainable future.',
                'type' => 'textarea',
                'group' => 'pages',
                'label' => 'Welcome Page Description',
                'description' => 'Main description on the welcome page',
                'sort_order' => 3,
                'is_public' => true,
            ],
            [
                'key' => 'dashboard_welcome_message',
                'value' => 'Welcome back! Continue your sustainable living journey.',
                'type' => 'textarea',
                'group' => 'pages',
                'label' => 'Dashboard Welcome Message',
                'description' => 'Welcome message shown on user dashboard',
                'sort_order' => 4,
                'is_public' => false,
            ],

            // Pricing & Plans
            [
                'key' => 'enable_custom_pricing',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'pricing',
                'label' => 'Enable Custom Pricing',
                'description' => 'Allow admins to customize stage activation pricing',
                'sort_order' => 1,
                'is_public' => false,
            ],
            [
                'key' => 'default_commission_rate',
                'value' => '10.00',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Default Commission Rate',
                'description' => 'Default commission rate for new stages (in currency)',
                'sort_order' => 2,
                'is_public' => false,
            ],
            [
                'key' => 'default_activation_bonus',
                'value' => '5.00',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Default Activation Bonus',
                'description' => 'Default activation bonus for new stages (in currency)',
                'sort_order' => 3,
                'is_public' => false,
            ],

            // Appearance
            [
                'key' => 'favicon',
                'value' => null,
                'type' => 'image',
                'group' => 'appearance',
                'label' => 'Favicon',
                'description' => 'Upload your site favicon (16x16 or 32x32 pixels, .ico, .png, or .svg)',
                'sort_order' => 1,
                'is_public' => true,
            ],
            [
                'key' => 'primary_color',
                'value' => '#4F46E5',
                'type' => 'text',
                'group' => 'appearance',
                'label' => 'Primary Color',
                'description' => 'Primary brand color (hex code)',
                'sort_order' => 2,
                'is_public' => true,
            ],
            [
                'key' => 'secondary_color',
                'value' => '#10B981',
                'type' => 'text',
                'group' => 'appearance',
                'label' => 'Secondary Color',
                'description' => 'Secondary brand color (hex code)',
                'sort_order' => 3,
                'is_public' => true,
            ],

            // General Settings
            [
                'key' => 'site_maintenance',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'general',
                'label' => 'Maintenance Mode',
                'description' => 'Enable maintenance mode for the site',
                'sort_order' => 1,
                'is_public' => false,
            ],
            [
                'key' => 'user_registration_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'general',
                'label' => 'User Registration Enabled',
                'description' => 'Allow new user registrations',
                'sort_order' => 2,
                'is_public' => false,
            ],
        ];

        foreach ($defaults as $setting) {
            self::firstOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
