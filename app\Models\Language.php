<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Language extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'native_name',
        'flag_emoji',
        'is_active',
        'is_default',
        'is_rtl',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'is_rtl' => 'boolean',
    ];

    /**
     * Get the default language.
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Get active languages.
     */
    public static function getActive()
    {
        return static::where('is_active', true)->orderBy('name')->get();
    }

    /**
     * Get language by code.
     */
    public static function getByCode($code)
    {
        return static::where('code', $code)->where('is_active', true)->first();
    }

    /**
     * Get display name with flag.
     */
    public function getDisplayNameAttribute()
    {
        return $this->flag_emoji . ' ' . $this->native_name;
    }

    /**
     * Get users using this language.
     */
    public function users()
    {
        return $this->hasMany(User::class, 'language', 'code');
    }
}
