@extends('layouts.app')

@section('title', 'Life Journey Activations')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Earth-Friendly Life Journey Activations</h1>
        <p class="mt-2 text-gray-600">
            @if($user->isEarthFriendlyMember())
                Welcome, Earth-Friendly Member! Choose any stage below to become a Light Member and start your sustainable living journey.
            @else
                Welcome, Light Member! You can switch between stages anytime to optimize your commission earnings.
            @endif
        </p>
    </div>

    <!-- Current Status -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Current Status</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    @if($user->isEarthFriendlyMember())
                        <h4 class="text-xl font-semibold text-green-600">Earth-Friendly Member</h4>
                        <p class="text-gray-600">Free membership - Ready to activate your first stage!</p>
                    @else
                        <h4 class="text-xl font-semibold text-blue-600">Light Member</h4>
                        <p class="text-gray-600">
                            {{ $user->activeStageActivations->count() }} Active Stages • {{ $stats['active_referrals'] }} active referrals
                        </p>
                        @if($user->light_member_activated_at)
                        <p class="text-sm text-gray-500">Light member since {{ $user->light_member_activated_at->format('M j, Y') }}</p>
                        @endif
                    @endif
                </div>
                <div class="text-right">
                    <p class="text-2xl font-bold text-green-600">${{ number_format($user->total_earnings, 2) }}</p>
                    <p class="text-sm text-gray-500">Total Earned</p>
                    @if($user->isLightMember())
                    <p class="text-sm text-blue-600 mt-1">
                        ${{ number_format($user->total_bonus_earnings ?? 0, 2) }} from bonuses
                    </p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Available Stages -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        @foreach($membershipStages as $stage)
        @php
            $isActivated = $user->hasActivatedStage($stage->slug);
        @endphp
        <div class="bg-white shadow rounded-lg overflow-hidden {{ $isActivated ? 'ring-2 ring-green-500' : '' }}">
            <!-- Stage Header -->
            <div class="px-6 py-4 {{ $isActivated ? 'bg-green-50 border-b border-green-200' : 'bg-gray-50 border-b border-gray-200' }}">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold {{ $isActivated ? 'text-green-900' : 'text-gray-900' }}">
                        {{ $stage->name }}
                    </h3>
                    @if($isActivated)
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Activated
                    </span>
                    @else
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Available
                    </span>
                    @endif
                </div>
                <p class="text-sm {{ $isActivated ? 'text-green-600' : 'text-gray-600' }} mt-1">
                    @if($stage->min_years && $stage->max_years)
                        {{ $stage->min_years }}-{{ $stage->max_years }} yrs
                    @elseif($stage->min_years)
                        {{ $stage->min_years }}+ yrs
                    @else
                        Open to all levels
                    @endif
                </p>
            </div>

            <!-- Stage Content -->
            <div class="px-6 py-4">


                <!-- Activation Fee -->
                <div class="text-center mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p class="text-sm font-medium text-blue-800">Activation Fee</p>
                    <p class="text-lg font-bold text-blue-900">
                        @if($stage->activation_price > 0)
                            ${{ number_format($stage->activation_price, 2) }}
                        @else
                            FREE
                        @endif
                    </p>
                    <p class="text-xs text-blue-600">Pledge your commitment</p>
                </div>



                <!-- Stage Information -->
                <div class="pt-4 border-t border-gray-200 space-y-3">
                    @if($stage->description)
                    <div>
                        <h5 class="text-sm font-medium text-gray-900 mb-1">Description</h5>
                        <p class="text-sm text-gray-600">{{ $stage->description }}</p>
                    </div>
                    @endif

                    <!-- Stage Benefits -->
                    @if($stage->benefits && count($stage->benefits) > 0)
                    <div>
                        <h5 class="text-sm font-medium text-gray-900 mb-2">Benefits</h5>
                        <ul class="text-sm text-gray-600 space-y-1">
                            @foreach($stage->benefits as $benefit)
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $benefit }}
                                </li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Stage Footer -->
            <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
                @if($isActivated)
                <div class="text-center">
                    <p class="text-sm text-green-600 font-medium mb-2">✓ Stage Activated</p>
                    <p class="text-xs text-gray-500 mb-3">Access to {{ $stage->name }} member area</p>
                    <a href="{{ url('/stages/' . $stage->slug) }}"
                       class="inline-flex items-center px-4 py-2 border border-green-300 text-sm font-medium rounded text-green-700 bg-green-50 hover:bg-green-100">
                        Enter {{ $stage->name }} Area
                    </a>
                </div>
                @else
                <form method="POST" action="{{ route('activations.activate') }}" class="text-center">
                    @csrf
                    <input type="hidden" name="stage" value="{{ $stage->slug }}">
                    @if($user->isEarthFriendlyMember())
                    <button type="submit"
                            onclick="return confirm('Ready to become a Light Member? Activating {{ $stage->name }} will unlock your member area and referral earning potential!')"
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        @if($stage->activation_price > 0)
                            Activate for ${{ number_format($stage->activation_price, 2) }}
                        @else
                            Join Free
                        @endif
                    </button>
                    @else
                    <button type="submit"
                            onclick="return confirm('Activate {{ $stage->name }} stage? @if($stage->activation_price > 0)This will cost ${{ number_format($stage->activation_price, 2) }}.@endif')"
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        @if($stage->activation_price > 0)
                            Activate for ${{ number_format($stage->activation_price, 2) }}
                        @else
                            Activate {{ $stage->name }}
                        @endif
                    </button>
                    @endif
                </form>
                @endif
            </div>
        </div>
        @endforeach
    </div>

    <!-- Important Notice -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">
                    @if($user->isEarthFriendlyMember())
                    Welcome to Earth-Friendly Membership!
                    @else
                    Light Member Benefits
                    @endif
                </h3>
                <div class="mt-2 text-sm text-green-700">
                    @if($user->isEarthFriendlyMember())
                    <p>As an Earth-Friendly member, you have free access to our community. Ready to start earning and access our full sustainable living resources? Activate any or all stages to become a Light Member!</p>
                    <ul class="mt-2 list-disc list-inside space-y-1">
                        <li><strong>Activate multiple stages</strong> - Each stage works independently</li>
                        <li><strong>Instant Light Member status</strong> - Access to all resources</li>
                        <li><strong>Earn from each activation</strong> - $5-$30 per referral activation</li>
                        <li><strong>Bonus opportunities</strong> - Milestone, time-based, and leaderboard bonuses</li>
                        <li><strong>Sustainable living journey</strong> - Resources to help you live better</li>
                    </ul>
                    @else
                    <p>As a Light Member, you can activate multiple stages simultaneously! Each activated stage earns you commissions when your referrals activate that same stage.</p>
                    <ul class="mt-2 list-disc list-inside space-y-1">
                        <li><strong>Multi-stage earnings</strong> - Earn from each activated stage independently</li>
                        <li><strong>Referral activation commissions</strong> - Earn when referrals activate your stages</li>
                        <li><strong>Milestone bonuses</strong> - Extra rewards at 10, 25, 50+ referrals</li>
                        <li><strong>Time-based bonuses</strong> - First 30 days and weekly streak rewards</li>
                        <li><strong>Flexible activation</strong> - Activate/deactivate stages anytime</li>
                    </ul>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 text-center">
        <div class="flex justify-center space-x-4">
            <a href="{{ route('awareness.index') }}"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                Share Awareness Link
            </a>
            <a href="{{ route('awareness.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                View My Awareness
            </a>
            <a href="{{ route('wallet.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                View Wallet
            </a>
        </div>
    </div>
</div>
@endsection
