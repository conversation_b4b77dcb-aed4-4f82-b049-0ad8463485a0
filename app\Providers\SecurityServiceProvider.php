<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;

class SecurityServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();
        $this->configureSecurityPolicies();
    }

    /**
     * Configure rate limiting for security
     */
    protected function configureRateLimiting(): void
    {
        // API Rate Limiting
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(config('security.rate_limiting.api_requests_per_minute', 60))
                ->by($request->user()?->id ?: $request->ip());
        });

        // Login Rate Limiting
        RateLimiter::for('login', function (Request $request) {
            return Limit::perMinute(config('security.rate_limiting.login_attempts_per_minute', 5))
                ->by($request->ip());
        });

        // Registration Rate Limiting
        RateLimiter::for('registration', function (Request $request) {
            return Limit::perHour(config('security.rate_limiting.registration_attempts_per_hour', 3))
                ->by($request->ip());
        });

        // Password Reset Rate Limiting
        RateLimiter::for('password-reset', function (Request $request) {
            return Limit::perHour(config('security.rate_limiting.password_reset_attempts_per_hour', 3))
                ->by($request->ip());
        });

        // Admin Actions Rate Limiting
        RateLimiter::for('admin', function (Request $request) {
            return Limit::perMinute(120)
                ->by($request->user()?->id ?: $request->ip());
        });

        // Financial Actions Rate Limiting
        RateLimiter::for('financial', function (Request $request) {
            return Limit::perMinute(30)
                ->by($request->user()?->id ?: $request->ip());
        });
    }

    /**
     * Configure security policies
     */
    protected function configureSecurityPolicies(): void
    {
        // Session configuration for security
        config([
            'session.lifetime' => config('security.auth.session_timeout', 1800) / 60, // Convert to minutes
            'session.expire_on_close' => true,
            'session.encrypt' => true,
            'session.http_only' => true,
            'session.same_site' => 'strict',
        ]);

        // Password validation rules
        if (config('security.auth.require_strong_passwords', true)) {
            config([
                'auth.password_validation' => [
                    'min:' . config('security.auth.password_min_length', 8),
                    'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
                ],
            ]);
        }
    }
}
