// Platform Customization System - External JavaScript
// This file contains all the JavaScript functionality for the platform customization page

// Basic notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Global state management
const platformCustomization = {
    settings: {
        branding: {},
        appearance: {},
        layout: {},
        content: {},
        features: {}
    },

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.initializeColorPickers();
    },

    loadSettings() {
        console.log('Loading platform customization settings...');
    },

    setupEventListeners() {
        console.log('Setting up event listeners...');
    },

    initializeColorPickers() {
        console.log('Initializing color pickers...');
    }
};

// Branding Functions
function uploadLogo(type = 'primary') {
    showNotification(`Upload ${type} logo functionality will be implemented`, 'info');
}

function uploadFavicon() {
    showNotification('Upload favicon functionality will be implemented', 'info');
}

function updateLogoPreview(url, type) {
    console.log(`Update logo preview: ${url} for type: ${type}`);
}

function updateFaviconPreview(url) {
    console.log(`Update favicon preview: ${url}`);
}

function resetBranding() {
    if (confirm('Are you sure you want to reset all branding settings to default?')) {
        showNotification('Branding reset to default', 'success');
    }
}

// Color and Appearance Functions
function updateColorScheme() {
    showNotification('Color scheme updated!', 'success');
    generateCustomCSS();
}

function generateCustomCSS() {
    console.log('Generating custom CSS...');
}

// Preview and Save Functions
function previewChanges() {
    showNotification('Preview functionality will be implemented', 'info');
}

function saveAllSettings() {
    showNotification('All settings saved successfully!', 'success');
}

function exportSettings() {
    showNotification('Settings exported successfully!', 'success');
}

function importSettings() {
    showNotification('Import functionality will be implemented', 'info');
}

function updateColorPreview(input) {
    console.log(`Update color preview for: ${input.name}`);
}

// Sub-menu specific functions
function saveBrandingSettings() {
    showNotification('Branding settings saved successfully!', 'success');
}

function saveAppearanceSettings() {
    showNotification('Appearance settings saved successfully!', 'success');
    generateCustomCSS();
}

function saveLayoutSettings() {
    showNotification('Layout settings saved successfully!', 'success');
}

function saveContentSettings() {
    showNotification('Content settings saved successfully!', 'success');
}

function saveFeaturesSettings() {
    showNotification('Features settings saved successfully!', 'success');
}

function previewTheme(themeName) {
    showNotification(`Previewing ${themeName} theme`, 'info');
}

function resetToDefaults(section) {
    if (confirm(`Are you sure you want to reset ${section} settings to defaults?`)) {
        showNotification(`${section} settings reset to defaults!`, 'success');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    platformCustomization.init();
    generateCustomCSS();
});

// Make functions globally available
window.showNotification = showNotification;
window.uploadLogo = uploadLogo;
window.uploadFavicon = uploadFavicon;
window.updateLogoPreview = updateLogoPreview;
window.updateFaviconPreview = updateFaviconPreview;
window.resetBranding = resetBranding;
window.updateColorScheme = updateColorScheme;
window.generateCustomCSS = generateCustomCSS;
window.previewChanges = previewChanges;
window.saveAllSettings = saveAllSettings;
window.exportSettings = exportSettings;
window.importSettings = importSettings;
window.updateColorPreview = updateColorPreview;
window.saveBrandingSettings = saveBrandingSettings;
window.saveAppearanceSettings = saveAppearanceSettings;
window.saveLayoutSettings = saveLayoutSettings;
window.saveContentSettings = saveContentSettings;
window.saveFeaturesSettings = saveFeaturesSettings;
window.previewTheme = previewTheme;
window.resetToDefaults = resetToDefaults;
window.uploadLogo = uploadLogo;
window.uploadFavicon = uploadFavicon;

// Additional functions for the new separated pages

function uploadLogo(type) {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('logo', file);
            formData.append('type', type);

            fetch('/admin/platform-customization/upload-logo', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Logo uploaded successfully', 'success');
                    // Update preview
                    const preview = document.getElementById(`${type}-logo-preview`);
                    const placeholder = document.getElementById(`${type}-logo-placeholder`);
                    if (preview && data.logo_url) {
                        preview.src = data.logo_url;
                        preview.classList.remove('hidden');
                        if (placeholder) placeholder.classList.add('hidden');
                    }
                } else {
                    showNotification(data.message || 'Error uploading logo', 'error');
                }
            })
            .catch(error => {
                console.error('Error uploading logo:', error);
                showNotification('Error uploading logo', 'error');
            });
        }
    };
    input.click();
}

function uploadFavicon() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.ico,.png';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('favicon', file);

            fetch('/admin/platform-customization/upload-favicon', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Favicon uploaded successfully', 'success');
                    // Update preview
                    const preview = document.getElementById('favicon-preview');
                    if (preview && data.favicon_url) {
                        preview.style.backgroundImage = `url(${data.favicon_url})`;
                        preview.style.backgroundSize = 'cover';
                    }
                } else {
                    showNotification(data.message || 'Error uploading favicon', 'error');
                }
            })
            .catch(error => {
                console.error('Error uploading favicon:', error);
                showNotification('Error uploading favicon', 'error');
            });
        }
    };
    input.click();
}

function previewTheme(theme) {
    // Apply theme preview temporarily
    document.body.className = document.body.className.replace(/theme-\w+/g, '') + ` theme-${theme}`;
    showNotification(`Previewing ${theme} theme`, 'info');
}
