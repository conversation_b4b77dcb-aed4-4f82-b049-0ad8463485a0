<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MarketplaceAffiliateCommission extends Model
{
    use HasFactory;

    protected $fillable = [
        'affiliate_id',
        'order_id',
        'commission_amount',
        'commission_rate',
        'status',
        'earned_at',
        'paid_at',
    ];

    protected $casts = [
        'commission_amount' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'earned_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the affiliate.
     */
    public function affiliate()
    {
        return $this->belongsTo(MarketplaceAffiliate::class);
    }

    /**
     * Get the order.
     */
    public function order()
    {
        return $this->belongsTo(MarketplaceOrder::class);
    }

    /**
     * Scope for pending commissions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved commissions.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for paid commissions.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }
}
