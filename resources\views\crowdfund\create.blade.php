@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Create Crowdfunding Campaign</h1>
                <p class="mt-2 text-gray-600">Launch your project and get community support</p>
            </div>
            <a href="{{ route('crowdfund.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Campaigns
            </a>
        </div>
    </div>

    <!-- Campaign Creation Form -->
    <div class="bg-white shadow rounded-lg">
        <form action="{{ route('crowdfund.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6 p-6">
            @csrf

            <!-- Basic Information -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Campaign Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Title -->
                    <div class="md:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700">Campaign Title</label>
                        <input type="text" name="title" id="title" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Enter a compelling title for your campaign"
                               value="{{ old('title') }}">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                        <select name="category" id="category" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select a category</option>
                            <option value="technology" {{ old('category') === 'technology' ? 'selected' : '' }}>Technology</option>
                            <option value="business" {{ old('category') === 'business' ? 'selected' : '' }}>Business</option>
                            <option value="creative" {{ old('category') === 'creative' ? 'selected' : '' }}>Creative</option>
                            <option value="social" {{ old('category') === 'social' ? 'selected' : '' }}>Social Impact</option>
                            <option value="education" {{ old('category') === 'education' ? 'selected' : '' }}>Education</option>
                            <option value="health" {{ old('category') === 'health' ? 'selected' : '' }}>Health</option>
                            <option value="environment" {{ old('category') === 'environment' ? 'selected' : '' }}>Environment</option>
                            <option value="other" {{ old('category') === 'other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Campaign Type -->
                    <div>
                        <label for="campaign_type" class="block text-sm font-medium text-gray-700">Campaign Type</label>
                        <select name="campaign_type" id="campaign_type" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select campaign type</option>
                            <option value="donation" {{ old('campaign_type') === 'donation' ? 'selected' : '' }}>Donation (No payback required)</option>
                            <option value="investment" {{ old('campaign_type') === 'investment' ? 'selected' : '' }}>Investment (Profit sharing)</option>
                            <option value="loan" {{ old('campaign_type') === 'loan' ? 'selected' : '' }}>Loan (Fixed repayment)</option>
                        </select>
                        @error('campaign_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Description -->
                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-gray-700">Campaign Description</label>
                    <textarea name="description" id="description" rows="6" required
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Describe your project, goals, and how the funds will be used...">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Financial Details -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Financial Details</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Target Amount -->
                    <div>
                        <label for="target_amount" class="block text-sm font-medium text-gray-700">Target Amount ($)</label>
                        <input type="number" name="target_amount" id="target_amount" required min="1" step="0.01"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="0.00"
                               value="{{ old('target_amount') }}">
                        @error('target_amount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Duration -->
                    <div>
                        <label for="duration_days" class="block text-sm font-medium text-gray-700">Campaign Duration (Days)</label>
                        <select name="duration_days" id="duration_days" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select duration</option>
                            <option value="30" {{ old('duration_days') == '30' ? 'selected' : '' }}>30 days</option>
                            <option value="45" {{ old('duration_days') == '45' ? 'selected' : '' }}>45 days</option>
                            <option value="60" {{ old('duration_days') == '60' ? 'selected' : '' }}>60 days</option>
                            <option value="90" {{ old('duration_days') == '90' ? 'selected' : '' }}>90 days</option>
                            <option value="120" {{ old('duration_days') == '120' ? 'selected' : '' }}>120 days</option>
                        </select>
                        @error('duration_days')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Profit Percentage (for investments) -->
                    <div id="profit_percentage_field" style="display: none;">
                        <label for="profit_percentage" class="block text-sm font-medium text-gray-700">Profit Percentage (%)</label>
                        <input type="number" name="profit_percentage" id="profit_percentage" min="0" max="100" step="0.1"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="0.0"
                               value="{{ old('profit_percentage') }}">
                        <p class="mt-1 text-xs text-gray-500">Expected profit percentage for investors</p>
                        @error('profit_percentage')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Media Upload -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Campaign Media</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Images -->
                    <div>
                        <label for="images" class="block text-sm font-medium text-gray-700">Campaign Images</label>
                        <input type="file" name="images[]" id="images" multiple accept="image/*"
                               class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                        <p class="mt-1 text-xs text-gray-500">Upload up to 5 images (max 2MB each)</p>
                        @error('images.*')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Documents -->
                    <div>
                        <label for="documents" class="block text-sm font-medium text-gray-700">Supporting Documents</label>
                        <input type="file" name="documents[]" id="documents" multiple accept=".pdf,.doc,.docx"
                               class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                        <p class="mt-1 text-xs text-gray-500">Business plan, financial projections, etc. (max 5MB each)</p>
                        @error('documents.*')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Terms and Submit -->
            <div class="pt-6">
                <div class="flex items-start mb-6">
                    <div class="flex items-center h-5">
                        <input id="terms" name="terms" type="checkbox" required
                               class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="terms" class="font-medium text-gray-700">
                            I agree to the terms and conditions
                        </label>
                        <p class="text-gray-500">
                            By creating this campaign, you agree to our platform terms, escrow policies, and community guidelines.
                        </p>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <p>Your campaign will be reviewed by our team before going live.</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('crowdfund.index') }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit"
                                class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                            </svg>
                            Create Campaign
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const campaignTypeSelect = document.getElementById('campaign_type');
    const profitPercentageField = document.getElementById('profit_percentage_field');
    
    function toggleProfitPercentage() {
        if (campaignTypeSelect.value === 'investment') {
            profitPercentageField.style.display = 'block';
            document.getElementById('profit_percentage').required = true;
        } else {
            profitPercentageField.style.display = 'none';
            document.getElementById('profit_percentage').required = false;
        }
    }
    
    campaignTypeSelect.addEventListener('change', toggleProfitPercentage);
    toggleProfitPercentage(); // Initial check
});
</script>
@endsection
