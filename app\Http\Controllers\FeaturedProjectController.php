<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FeaturedProject;
use App\Models\ProjectParticipation;
use App\Models\EarningHistory;
use Illuminate\Support\Facades\Auth;

class FeaturedProjectController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display featured projects for action stage members
     */
    public function index()
    {
        $user = Auth::user();

        // Check if user has action stage activation
        $hasActionStage = $user->activeStageActivations()
            ->whereHas('membershipStage', function($query) {
                $query->where('name', 'like', '%action%');
            })->exists();

        // Get active featured projects
        $projects = FeaturedProject::where('status', 'active')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->withCount('participations')
            ->with(['participations' => function($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        // Get user's participations
        $userParticipations = $user->projectParticipations()
            ->with('featuredProject')
            ->latest()
            ->take(5)
            ->get();

        // Statistics
        $stats = [
            'total_projects' => FeaturedProject::where('status', 'active')->count(),
            'user_participations' => $user->projectParticipations()->count(),
            'completed_projects' => $user->projectParticipations()->where('status', 'verified')->count(),
            'total_points_earned' => $user->projectParticipations()->sum('points_earned'),
            'total_bonus_earned' => $user->projectParticipations()->sum('bonus_earned'),
        ];

        return view('featured-projects.index', compact('projects', 'userParticipations', 'stats', 'hasActionStage'));
    }

    /**
     * Show specific featured project
     */
    public function show(FeaturedProject $project)
    {
        $user = Auth::user();

        // Check if user has action stage activation
        $hasActionStage = $user->activeStageActivations()
            ->whereHas('membershipStage', function($query) {
                $query->where('name', 'like', '%action%');
            })->exists();

        // Get user's participation if exists
        $participation = $user->projectParticipations()
            ->where('featured_project_id', $project->id)
            ->first();

        // Get recent participants
        $recentParticipants = $project->participations()
            ->with('user')
            ->where('status', '!=', 'rejected')
            ->latest()
            ->take(10)
            ->get();

        return view('featured-projects.show', compact('project', 'participation', 'recentParticipants', 'hasActionStage'));
    }

    /**
     * Enroll in a featured project
     */
    public function enroll(Request $request, FeaturedProject $project)
    {
        $user = Auth::user();

        // Check if user has action stage activation
        if ($project->requires_action_stage) {
            $hasActionStage = $user->activeStageActivations()
                ->whereHas('membershipStage', function($query) {
                    $query->where('name', 'like', '%action%');
                })->exists();

            if (!$hasActionStage) {
                return back()->with('error', 'You need to activate the Action stage to participate in this project.');
            }
        }

        // Check if project is active and within date range
        if ($project->status !== 'active' || $project->start_date > now() || $project->end_date < now()) {
            return back()->with('error', 'This project is not currently available for enrollment.');
        }

        // Check if user is already enrolled
        if ($user->projectParticipations()->where('featured_project_id', $project->id)->exists()) {
            return back()->with('error', 'You are already enrolled in this project.');
        }

        // Check if project has reached max participants
        if ($project->max_participants && $project->current_participants >= $project->max_participants) {
            return back()->with('error', 'This project has reached its maximum number of participants.');
        }

        // Create participation
        ProjectParticipation::create([
            'user_id' => $user->id,
            'featured_project_id' => $project->id,
            'status' => 'enrolled',
            'enrolled_at' => now(),
        ]);

        // Update project participant count
        $project->increment('current_participants');

        return back()->with('success', 'Successfully enrolled in the project! Check the instructions to get started.');
    }

    /**
     * Submit project completion proof
     */
    public function submitProof(Request $request, FeaturedProject $project)
    {
        $user = Auth::user();

        $participation = $user->projectParticipations()
            ->where('featured_project_id', $project->id)
            ->first();

        if (!$participation) {
            return back()->with('error', 'You are not enrolled in this project.');
        }

        if ($participation->status !== 'enrolled' && $participation->status !== 'in_progress') {
            return back()->with('error', 'You cannot submit proof for this project at this time.');
        }

        $request->validate([
            'proof_description' => 'required|string|max:1000',
            'proof_files.*' => 'nullable|file|max:10240', // 10MB max per file
            'proof_links' => 'nullable|string|max:500',
        ]);

        $proofData = [
            'description' => $request->proof_description,
            'links' => $request->proof_links,
            'submitted_at' => now()->toISOString(),
        ];

        // Handle file uploads
        if ($request->hasFile('proof_files')) {
            $files = [];
            foreach ($request->file('proof_files') as $file) {
                $path = $file->store('project-proofs', 'public');
                $files[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                ];
            }
            $proofData['files'] = $files;
        }

        $participation->update([
            'status' => 'completed',
            'submitted_proof' => $proofData,
            'completed_at' => now(),
        ]);

        return back()->with('success', 'Proof submitted successfully! Your submission is under review.');
    }

    /**
     * Admin: Verify project completion
     */
    public function verify(Request $request, ProjectParticipation $participation)
    {
        $this->middleware('admin');

        $request->validate([
            'status' => 'required|in:verified,rejected',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        $participation->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
            'verified_at' => $request->status === 'verified' ? now() : null,
        ]);

        if ($request->status === 'verified') {
            // Award points and bonus
            $project = $participation->featuredProject;

            $participation->update([
                'points_earned' => $project->reward_points,
                'bonus_earned' => $project->bonus_amount,
            ]);

            // Add points to user
            $participation->user->increment('reward_points', $project->reward_points);

            // Create earning history for bonus if applicable
            if ($project->bonus_amount > 0) {
                EarningHistory::create([
                    'user_id' => $participation->user_id,
                    'type' => 'project_completion',
                    'amount' => $project->bonus_amount,
                    'points' => $project->reward_points,
                    'currency' => 'USD',
                    'description' => "Project completion bonus: {$project->title}",
                    'reference_type' => ProjectParticipation::class,
                    'reference_id' => $participation->id,
                    'status' => 'approved',
                    'auto_pay' => true,
                ]);

                // Add to user's available balance if auto_pay is enabled
                $participation->user->increment('available_balance', $project->bonus_amount);
            }
        }

        return back()->with('success', "Participation {$request->status} successfully.");
    }
}
