<?php

namespace App\Http\Controllers;

use App\Models\ProjectType;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ProjectTypeController extends Controller
{
    public function index()
    {
        $projectTypes = ProjectType::active()->ordered()->get();
        
        return view('admin.project-types.index', compact('projectTypes'));
    }

    public function create()
    {
        return view('admin.project-types.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'nullable|string',
            'color' => 'required|string',
            'features' => 'nullable|array',
            'min_votes_required' => 'required|integer|min:1',
            'requires_voting' => 'boolean',
            'allows_donations' => 'boolean',
            'allows_volunteers' => 'boolean',
            'allows_crowdfunding' => 'boolean',
            'has_stages' => 'boolean',
            'default_duration_days' => 'nullable|integer|min:1',
            'sort_order' => 'integer|min:0',
        ]);

        $validated['slug'] = Str::slug($validated['name']);
        
        ProjectType::create($validated);

        return redirect()->route('admin.project-types.index')
            ->with('success', 'Project type created successfully.');
    }

    public function show(ProjectType $projectType)
    {
        $projectType->load('projects');
        
        return view('admin.project-types.show', compact('projectType'));
    }

    public function edit(ProjectType $projectType)
    {
        return view('admin.project-types.edit', compact('projectType'));
    }

    public function update(Request $request, ProjectType $projectType)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'nullable|string',
            'color' => 'required|string',
            'features' => 'nullable|array',
            'min_votes_required' => 'required|integer|min:1',
            'requires_voting' => 'boolean',
            'allows_donations' => 'boolean',
            'allows_volunteers' => 'boolean',
            'allows_crowdfunding' => 'boolean',
            'has_stages' => 'boolean',
            'default_duration_days' => 'nullable|integer|min:1',
            'sort_order' => 'integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validated['name'] !== $projectType->name) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $projectType->update($validated);

        return redirect()->route('admin.project-types.index')
            ->with('success', 'Project type updated successfully.');
    }

    public function destroy(ProjectType $projectType)
    {
        if ($projectType->projects()->count() > 0) {
            return redirect()->route('admin.project-types.index')
                ->with('error', 'Cannot delete project type with existing projects.');
        }

        $projectType->delete();

        return redirect()->route('admin.project-types.index')
            ->with('success', 'Project type deleted successfully.');
    }
}
