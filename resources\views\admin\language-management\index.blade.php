@extends('layouts.admin')

@section('title', 'Language Management')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Clean Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Language Management</h1>
                <p class="text-gray-600 mt-1">Manage multilingual content and translations</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportLanguageData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export Translations
                </button>
                <button onclick="saveLanguageSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Clean Navigation -->
    <div class="mb-8" x-data="{ activeTab: 'languages' }">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <nav class="flex space-x-0">
                <button @click="activeTab = 'languages'" :class="activeTab === 'languages' ? 'bg-purple-50 text-purple-700 border-purple-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 first:rounded-l-lg transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clip-rule="evenodd"></path>
                    </svg>
                    Languages
                </button>
                <button @click="activeTab = 'translations'" :class="activeTab === 'translations' ? 'bg-purple-50 text-purple-700 border-purple-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                    Translations
                </button>
                <button @click="activeTab = 'import'" :class="activeTab === 'import' ? 'bg-purple-50 text-purple-700 border-purple-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Import/Export
                </button>
                <button @click="activeTab = 'settings'" :class="activeTab === 'settings' ? 'bg-purple-50 text-purple-700 border-purple-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                    Settings
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'bg-purple-50 text-purple-700 border-purple-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium last:rounded-r-lg transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                    </svg>
                    Analytics
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div>
            <!-- Languages Tab -->
            <div x-show="activeTab === 'languages'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🌍 Language Configuration</h3>

                <div class="space-y-6">
                    <!-- Language Statistics -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                            <div class="text-2xl font-bold">5</div>
                            <div class="text-purple-100">Active Languages</div>
                        </div>
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                            <div class="text-2xl font-bold">1,234</div>
                            <div class="text-blue-100">Translation Keys</div>
                        </div>
                        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                            <div class="text-2xl font-bold">98%</div>
                            <div class="text-green-100">Completion Rate</div>
                        </div>
                        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
                            <div class="text-2xl font-bold">23</div>
                            <div class="text-orange-100">Missing Translations</div>
                        </div>
                    </div>

                    <!-- Available Languages -->
                    <div>
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Available Languages</h4>
                            <button onclick="addNewLanguage()" class="bg-purple-600 text-white px-4 py-2 rounded-md text-sm hover:bg-purple-700">Add Language</button>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <span class="text-2xl mr-3">🇺🇸</span>
                                        <div>
                                            <h5 class="font-medium text-gray-900">English</h5>
                                            <p class="text-sm text-gray-500">en</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Default</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-500">100% Complete</span>
                                    <button class="text-purple-600 hover:text-purple-800 text-sm">Edit</button>
                                </div>
                            </div>

                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <span class="text-2xl mr-3">🇪🇸</span>
                                        <div>
                                            <h5 class="font-medium text-gray-900">Spanish</h5>
                                            <p class="text-sm text-gray-500">es</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Active</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-500">95% Complete</span>
                                    <button class="text-purple-600 hover:text-purple-800 text-sm">Edit</button>
                                </div>
                            </div>

                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <span class="text-2xl mr-3">🇫🇷</span>
                                        <div>
                                            <h5 class="font-medium text-gray-900">French</h5>
                                            <p class="text-sm text-gray-500">fr</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Incomplete</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-500">78% Complete</span>
                                    <button class="text-purple-600 hover:text-purple-800 text-sm">Edit</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Translations Tab -->
            <div x-show="activeTab === 'translations'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📝 Translation Management</h3>

                <div class="space-y-6">
                    <!-- Translation Filters -->
                    <div class="flex items-center space-x-4">
                        <select class="px-3 py-2 border border-gray-300 rounded-md">
                            <option>All Languages</option>
                            <option>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                        </select>
                        <select class="px-3 py-2 border border-gray-300 rounded-md">
                            <option>All Categories</option>
                            <option>Navigation</option>
                            <option>Forms</option>
                            <option>Messages</option>
                        </select>
                        <input type="text" placeholder="Search translations..." class="px-3 py-2 border border-gray-300 rounded-md flex-1">
                        <button class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">Search</button>
                    </div>

                    <!-- Translation Table -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Key</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">English</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spanish</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">French</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">nav.home</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Home</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Inicio</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Accueil</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button class="text-purple-600 hover:text-purple-900">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">nav.about</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">About</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Acerca de</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-red-500">Missing</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button class="text-purple-600 hover:text-purple-900">Edit</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Import/Export Tab -->
            <div x-show="activeTab === 'import'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📤 Import/Export Translations</h3>

                <div class="space-y-6">
                    <!-- Export Section -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Export Translations</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h5 class="text-sm font-medium text-gray-900 mb-2">Export Format</h5>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    <option>JSON</option>
                                    <option>CSV</option>
                                    <option>Excel</option>
                                    <option>PHP Array</option>
                                </select>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h5 class="text-sm font-medium text-gray-900 mb-2">Select Languages</h5>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="rounded border-gray-300">
                                        <span class="ml-2 text-sm text-gray-700">English</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="rounded border-gray-300">
                                        <span class="ml-2 text-sm text-gray-700">Spanish</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <button class="mt-4 bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700">Export Translations</button>
                    </div>

                    <!-- Import Section -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Import Translations</h4>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3-3m-3 3l3 3m-3-3H21m12 0v-8a4 4 0 00-4-4h-5m0 0V8a4 4 0 014-4h4a4 4 0 014 4v4h-8z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                <div class="mt-4">
                                    <label class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">Upload translation file</span>
                                        <input type="file" class="sr-only" accept=".json,.csv,.xlsx">
                                    </label>
                                    <p class="mt-2 text-xs text-gray-500">JSON, CSV, or Excel files up to 10MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div x-show="activeTab === 'settings'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">⚙️ Language Settings</h3>

                <div class="space-y-6">
                    <!-- General Settings -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-4">General Configuration</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Default Language</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    <option>English (en)</option>
                                    <option>Spanish (es)</option>
                                    <option>French (fr)</option>
                                </select>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Auto-detect Language</h5>
                                    <p class="text-sm text-gray-500">Automatically detect user's preferred language</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div x-show="activeTab === 'analytics'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📊 Language Analytics</h3>

                <div class="space-y-6">
                    <!-- Usage Statistics -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h5 class="text-sm font-medium text-gray-900 mb-2">Most Used Language</h5>
                            <div class="flex items-center">
                                <span class="text-2xl mr-2">🇺🇸</span>
                                <div>
                                    <p class="font-medium">English</p>
                                    <p class="text-sm text-gray-500">68% of users</p>
                                </div>
                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h5 class="text-sm font-medium text-gray-900 mb-2">Translation Progress</h5>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                            <p class="text-sm text-gray-500 mt-1">85% Complete</p>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h5 class="text-sm font-medium text-gray-900 mb-2">Missing Translations</h5>
                            <p class="text-2xl font-bold text-red-600">23</p>
                            <p class="text-sm text-gray-500">Across all languages</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportLanguageData() {
    alert('Exporting language data...');
}

function saveLanguageSettings() {
    alert('Language settings saved successfully!');
}

function addNewLanguage() {
    alert('Add new language functionality will be implemented');
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Language Management loaded');
});
</script>
@endsection
