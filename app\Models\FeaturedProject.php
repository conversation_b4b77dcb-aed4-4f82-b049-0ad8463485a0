<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FeaturedProject extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'instructions',
        'reward_points',
        'bonus_amount',
        'max_participants',
        'current_participants',
        'start_date',
        'end_date',
        'status',
        'requires_action_stage',
        'required_actions',
        'verification_method',
    ];

    protected $casts = [
        'reward_points' => 'decimal:2',
        'bonus_amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'requires_action_stage' => 'boolean',
        'required_actions' => 'array',
    ];

    /**
     * Get the participations for this project.
     */
    public function participations()
    {
        return $this->hasMany(ProjectParticipation::class);
    }

    /**
     * Get verified participations.
     */
    public function verifiedParticipations()
    {
        return $this->hasMany(ProjectParticipation::class)->where('status', 'verified');
    }

    /**
     * Check if project is active.
     */
    public function getIsActiveAttribute()
    {
        return $this->status === 'active' &&
               $this->start_date <= now() &&
               $this->end_date >= now();
    }

    /**
     * Check if project has available spots.
     */
    public function getHasAvailableSpotsAttribute()
    {
        if (!$this->max_participants) {
            return true;
        }

        return $this->current_participants < $this->max_participants;
    }

    /**
     * Get progress percentage.
     */
    public function getProgressPercentageAttribute()
    {
        if (!$this->max_participants) {
            return 0;
        }

        return round(($this->current_participants / $this->max_participants) * 100, 1);
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute()
    {
        return match($this->status) {
            'draft' => 'gray',
            'active' => 'green',
            'completed' => 'blue',
            'cancelled' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get days remaining.
     */
    public function getDaysRemainingAttribute()
    {
        if ($this->end_date < now()) {
            return 0;
        }

        return now()->diffInDays($this->end_date);
    }
}
