<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProjectVolunteer extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'user_id',
        'skills_offered',
        'availability',
        'message',
        'status',
        'approved_at',
        'approved_by',
        'hours_contributed',
        'contribution_notes'
    ];

    protected $casts = [
        'approved_at' => 'datetime'
    ];

    // Relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(CommunityProject::class, 'project_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Accessors
    public function getStatusDisplayAttribute(): string
    {
        return ucfirst($this->status);
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'approved' => 'bg-green-100 text-green-800',
            'pending' => 'bg-yellow-100 text-yellow-800',
            'declined' => 'bg-red-100 text-red-800',
            'completed' => 'bg-blue-100 text-blue-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    // Scopes
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Methods
    public function approve(User $approver): void
    {
        $this->status = 'approved';
        $this->approved_at = now();
        $this->approved_by = $approver->id;
        $this->save();
        
        // Update project volunteer count
        $this->project->updateVolunteerCount();
    }

    public function decline(): void
    {
        $this->status = 'declined';
        $this->save();
    }

    public function complete(int $hours = 0, string $notes = null): void
    {
        $this->status = 'completed';
        $this->hours_contributed = $hours;
        $this->contribution_notes = $notes;
        $this->save();
    }
}
