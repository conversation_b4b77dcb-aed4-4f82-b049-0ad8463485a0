@extends('layouts.admin')

@section('title', 'User Wallet Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">User Wallet Management System</h1>
                <p class="text-gray-600 mt-1">Manage user wallets, transactions, and financial history</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportWalletData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Data
                </button>
                <button onclick="bulkWalletUpdate()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Bulk Operations
                </button>
            </div>
        </div>
    </div>

    <!-- Wallet Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">$2,847,920</h3>
                    <p class="text-sm text-gray-600">Total Wallet Balance</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">$89,420</h3>
                    <p class="text-sm text-gray-600">Pending Withdrawals</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">47,892</h3>
                    <p class="text-sm text-gray-600">Total Points</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">23,456</h3>
                    <p class="text-sm text-gray-600">Total Transactions</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">12</h3>
                    <p class="text-sm text-gray-600">Flagged Transactions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'wallets' }">
                <button @click="activeTab = 'wallets'" :class="activeTab === 'wallets' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Wallet Overview
                </button>
                <button @click="activeTab = 'transactions'" :class="activeTab === 'transactions' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Transaction History
                </button>
                <button @click="activeTab = 'withdrawals'" :class="activeTab === 'withdrawals' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Withdrawal Management
                </button>
                <button @click="activeTab = 'points'" :class="activeTab === 'points' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Points System
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Financial Analytics
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'wallets' }">
        <!-- Wallet Overview Tab -->
        <div x-show="activeTab === 'wallets'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">User Wallet Overview</h3>
                        <div class="flex items-center space-x-3">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">All Wallets</option>
                                <option value="active">Active Wallets</option>
                                <option value="high_balance">High Balance</option>
                                <option value="low_balance">Low Balance</option>
                                <option value="suspended">Suspended</option>
                            </select>
                            <input type="text" placeholder="Search users..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Transaction</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-600">JS</span>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">John Smith</div>
                                            <div class="text-sm text-gray-500"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">$2,847.50</div>
                                    <div class="text-sm text-gray-500">USD</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">1,250</div>
                                    <div class="text-sm text-gray-500">Available</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-20</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="viewWallet(1)" class="text-indigo-600 hover:text-indigo-900">View</button>
                                        <button onclick="adjustBalance(1)" class="text-green-600 hover:text-green-900">Adjust</button>
                                        <button onclick="freezeWallet(1)" class="text-red-600 hover:text-red-900">Freeze</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Transaction History Tab -->
        <div x-show="activeTab === 'transactions'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Transaction History</h3>
                        <div class="flex items-center space-x-3">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">All Transactions</option>
                                <option value="deposits">Deposits</option>
                                <option value="withdrawals">Withdrawals</option>
                                <option value="referral_earnings">Referral Earnings</option>
                                <option value="point_conversions">Point Conversions</option>
                                <option value="flagged">Flagged</option>
                            </select>
                            <input type="date" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">#TXN-001247</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">John Smith</div>
                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Referral Earning
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-green-600">+$125.00</div>
                                    <div class="text-sm text-gray-500">USD</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Completed
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-20 14:30</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="viewTransaction(1)" class="text-indigo-600 hover:text-indigo-900">View</button>
                                        <button onclick="flagTransaction(1)" class="text-red-600 hover:text-red-900">Flag</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Withdrawal Management Tab -->
        <div x-show="activeTab === 'withdrawals'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Withdrawal Management</h3>
                        <button onclick="bulkApproveWithdrawals()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            Bulk Approve
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Pending Withdrawal -->
                        <div class="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-sm font-medium text-gray-600">SJ</span>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Sarah Johnson</h4>
                                        <p class="text-sm text-gray-600">Bank Transfer - $500.00</p>
                                        <p class="text-xs text-gray-500">Requested 2 hours ago</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="approveWithdrawal(1)" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700">
                                        Approve
                                    </button>
                                    <button onclick="rejectWithdrawal(1)" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700">
                                        Reject
                                    </button>
                                    <button onclick="viewWithdrawalDetails(1)" class="text-indigo-600 hover:text-indigo-900 text-sm">Details</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Points System Tab -->
        <div x-show="activeTab === 'points'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Points System Management</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Points Configuration -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900">Points Configuration</h4>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Daily Visit Reward</label>
                                    <p class="text-xs text-gray-500">Points awarded for daily login</p>
                                </div>
                                <input type="number" value="10" class="border border-gray-300 rounded-md px-3 py-2 text-sm w-20">
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Conversion Rate</label>
                                    <p class="text-xs text-gray-500">Points per $1 USD</p>
                                </div>
                                <input type="number" value="100" class="border border-gray-300 rounded-md px-3 py-2 text-sm w-20">
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Minimum Conversion</label>
                                    <p class="text-xs text-gray-500">Minimum points to convert to cash</p>
                                </div>
                                <input type="number" value="1000" class="border border-gray-300 rounded-md px-3 py-2 text-sm w-24">
                            </div>
                        </div>

                        <!-- Points Analytics -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900">Points Analytics</h4>
                            
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm text-gray-600">Total Points Issued</span>
                                    <span class="text-sm font-medium text-gray-900">2,847,920</span>
                                </div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm text-gray-600">Points Converted</span>
                                    <span class="text-sm font-medium text-gray-900">1,234,567</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Conversion Rate</span>
                                    <span class="text-sm font-medium text-green-600">43.4%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button onclick="savePointsSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportWalletData() {
    alert('Export wallet data functionality will be implemented');
}

function bulkWalletUpdate() {
    alert('Bulk wallet update functionality will be implemented');
}

function viewWallet(id) {
    alert(`View wallet ${id} functionality will be implemented`);
}

function adjustBalance(id) {
    alert(`Adjust balance for wallet ${id} functionality will be implemented`);
}

function freezeWallet(id) {
    if (confirm('Are you sure you want to freeze this wallet?')) {
        alert(`Freeze wallet ${id} functionality will be implemented`);
    }
}

function viewTransaction(id) {
    alert(`View transaction ${id} functionality will be implemented`);
}

function flagTransaction(id) {
    if (confirm('Flag this transaction as suspicious?')) {
        alert(`Flag transaction ${id} functionality will be implemented`);
    }
}

function bulkApproveWithdrawals() {
    if (confirm('Approve all pending withdrawals?')) {
        alert('Bulk approve withdrawals functionality will be implemented');
    }
}

function approveWithdrawal(id) {
    if (confirm('Approve this withdrawal request?')) {
        alert(`Approve withdrawal ${id} functionality will be implemented`);
    }
}

function rejectWithdrawal(id) {
    if (confirm('Reject this withdrawal request?')) {
        alert(`Reject withdrawal ${id} functionality will be implemented`);
    }
}

function viewWithdrawalDetails(id) {
    alert(`View withdrawal details ${id} functionality will be implemented`);
}

function savePointsSettings() {
    alert('Save points settings functionality will be implemented');
}
</script>
@endsection
