<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Marketplace Categories
        Schema::create('marketplace_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('icon')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Marketplace Products/Services
        Schema::create('marketplace_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained('marketplace_categories');
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('features')->nullable();
            $table->enum('type', ['product', 'service', 'information']);
            $table->decimal('price', 10, 2);
            $table->decimal('escrow_fee_percentage', 5, 2)->default(5.00);
            $table->integer('quantity')->nullable(); // For products
            $table->integer('delivery_days')->default(7);
            $table->json('images')->nullable();
            $table->json('files')->nullable(); // For information products
            $table->enum('status', ['draft', 'pending', 'active', 'suspended', 'sold_out'])->default('draft');
            $table->boolean('is_featured')->default(false);
            $table->integer('views')->default(0);
            $table->decimal('rating', 3, 2)->default(0);
            $table->integer('total_sales')->default(0);
            $table->timestamps();
        });

        // Marketplace Orders
        Schema::create('marketplace_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('buyer_id')->constrained('users');
            $table->foreignId('seller_id')->constrained('users');
            $table->foreignId('product_id')->constrained('marketplace_products');
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('escrow_amount', 10, 2);
            $table->decimal('platform_fee', 10, 2);
            $table->enum('status', ['pending', 'paid', 'in_progress', 'delivered', 'completed', 'disputed', 'cancelled', 'refunded']);
            $table->enum('payment_status', ['pending', 'paid', 'released', 'refunded']);
            $table->text('buyer_notes')->nullable();
            $table->text('seller_notes')->nullable();
            $table->timestamp('delivery_deadline')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });

        // Escrow Transactions
        Schema::create('escrow_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('marketplace_orders');
            $table->decimal('amount', 10, 2);
            $table->enum('type', ['deposit', 'release', 'refund', 'dispute_hold']);
            $table->enum('status', ['pending', 'completed', 'failed']);
            $table->text('description');
            $table->json('metadata')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
        });

        // Earning History
        Schema::create('earning_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type'); // referral, commission, bonus, project_completion, marketplace_sale
            $table->decimal('amount', 10, 2);
            $table->decimal('points', 10, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->text('description');
            $table->string('reference_type')->nullable(); // Model type
            $table->unsignedBigInteger('reference_id')->nullable(); // Model ID
            $table->enum('status', ['pending', 'approved', 'paid', 'cancelled']);
            $table->boolean('auto_pay')->default(false);
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
        });

        // Featured Projects for Action Stage
        Schema::create('featured_projects', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('instructions');
            $table->decimal('reward_points', 10, 2);
            $table->decimal('bonus_amount', 10, 2)->default(0);
            $table->integer('max_participants')->nullable();
            $table->integer('current_participants')->default(0);
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['draft', 'active', 'completed', 'cancelled']);
            $table->boolean('requires_action_stage')->default(true);
            $table->json('required_actions')->nullable(); // List of actions to complete
            $table->string('verification_method')->default('manual'); // manual, automatic
            $table->timestamps();
        });

        // Project Participations
        Schema::create('project_participations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('featured_project_id')->constrained()->onDelete('cascade');
            $table->enum('status', ['enrolled', 'in_progress', 'completed', 'verified', 'rejected']);
            $table->json('submitted_proof')->nullable(); // Evidence of completion
            $table->text('admin_notes')->nullable();
            $table->decimal('points_earned', 10, 2)->default(0);
            $table->decimal('bonus_earned', 10, 2)->default(0);
            $table->timestamp('enrolled_at');
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_participations');
        Schema::dropIfExists('featured_projects');
        Schema::dropIfExists('earning_history');
        Schema::dropIfExists('escrow_transactions');
        Schema::dropIfExists('marketplace_orders');
        Schema::dropIfExists('marketplace_products');
        Schema::dropIfExists('marketplace_categories');
    }
};
