<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProjectStage extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'title',
        'description',
        'stage_order',
        'target_amount',
        'target_days',
        'status',
        'completion_images',
        'completion_notes',
        'completed_at',
        'approved_at',
        'approved_by',
        'admin_feedback'
    ];

    protected $casts = [
        'completion_images' => 'array',
        'target_amount' => 'decimal:2',
        'completed_at' => 'datetime',
        'approved_at' => 'datetime'
    ];

    // Relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(CommunityProject::class, 'project_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Accessors
    public function getStatusDisplayAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->status));
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'completed' => 'bg-blue-100 text-blue-800',
            'approved' => 'bg-green-100 text-green-800',
            'in_progress' => 'bg-yellow-100 text-yellow-800',
            'rejected' => 'bg-red-100 text-red-800',
            'pending' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    // Methods
    public function markInProgress(): void
    {
        $this->status = 'in_progress';
        $this->save();
    }

    public function markCompleted(array $images = [], string $notes = null): void
    {
        $this->status = 'completed';
        $this->completion_images = $images;
        $this->completion_notes = $notes;
        $this->completed_at = now();
        $this->save();
    }

    public function approve(User $approver, string $feedback = null): void
    {
        $this->status = 'approved';
        $this->approved_at = now();
        $this->approved_by = $approver->id;
        $this->admin_feedback = $feedback;
        $this->save();
    }

    public function reject(User $approver, string $feedback = null): void
    {
        $this->status = 'rejected';
        $this->approved_by = $approver->id;
        $this->admin_feedback = $feedback;
        $this->save();
    }
}
