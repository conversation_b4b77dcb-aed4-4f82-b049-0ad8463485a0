<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class HelpersManagementController extends Controller
{
    public function index()
    {
        $helpers = $this->getAllHelpers();
        $tickets = $this->getRecentTickets();
        $helpDeskStats = $this->getHelpDeskStatistics();
        $knowledgeBase = $this->getKnowledgeBaseCategories();
        
        return view('admin.helpers-management.index', compact(
            'helpers', 
            'tickets', 
            'helpDeskStats', 
            'knowledgeBase'
        ));
    }

    public function createHelper(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'role' => 'required|string|in:support_agent,senior_agent,team_lead,manager',
            'password' => 'required|string|min:8',
            'departments' => 'nullable|array',
            'permissions' => 'nullable|array',
            'max_tickets_per_day' => 'nullable|integer|min:1|max:100',
        ]);

        $helperData = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'password' => Hash::make($request->password),
            'departments' => $request->departments ?? [],
            'permissions' => $request->permissions ?? [],
            'max_tickets_per_day' => $request->max_tickets_per_day ?? 20,
            'is_active' => true,
            'created_at' => now(),
            'created_by' => Auth::id()
        ];

        $helperId = $this->addNewHelper($helperData);

        return response()->json([
            'success' => true,
            'message' => 'Helper created successfully!',
            'helper_id' => $helperId
        ]);
    }

    public function updateHelper(Request $request, $helperId)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'role' => 'required|string|in:support_agent,senior_agent,team_lead,manager',
            'departments' => 'nullable|array',
            'permissions' => 'nullable|array',
            'max_tickets_per_day' => 'nullable|integer|min:1|max:100',
            'is_active' => 'boolean',
        ]);

        $updateData = [
            'name' => $request->name,
            'role' => $request->role,
            'departments' => $request->departments ?? [],
            'permissions' => $request->permissions ?? [],
            'max_tickets_per_day' => $request->max_tickets_per_day ?? 20,
            'is_active' => $request->boolean('is_active'),
            'updated_at' => now(),
            'updated_by' => Auth::id()
        ];

        $this->updateHelperData($helperId, $updateData);

        return response()->json([
            'success' => true,
            'message' => 'Helper updated successfully!'
        ]);
    }

    public function getTickets(Request $request)
    {
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);
        $priority = $request->get('priority');
        $status = $request->get('status');
        $assignedTo = $request->get('assigned_to');
        $search = $request->get('search');

        $query = $this->buildTicketsQuery($priority, $status, $assignedTo, $search);
        
        $tickets = $query->orderBy('created_at', 'desc')
                         ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $tickets->items(),
            'pagination' => [
                'current_page' => $tickets->currentPage(),
                'last_page' => $tickets->lastPage(),
                'per_page' => $tickets->perPage(),
                'total' => $tickets->total(),
            ]
        ]);
    }

    public function createTicket(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|string|in:low,medium,high,urgent',
            'category' => 'required|string',
            'user_email' => 'nullable|email',
            'assigned_to' => 'nullable|integer',
        ]);

        $ticketData = [
            'id' => $this->generateTicketId(),
            'title' => $request->title,
            'description' => $request->description,
            'priority' => $request->priority,
            'category' => $request->category,
            'status' => 'open',
            'user_email' => $request->user_email,
            'assigned_to' => $request->assigned_to,
            'created_by' => Auth::id(),
            'created_at' => now()
        ];

        $this->addNewTicket($ticketData);

        return response()->json([
            'success' => true,
            'message' => 'Ticket created successfully!',
            'ticket_id' => $ticketData['id']
        ]);
    }

    public function updateTicket(Request $request, $ticketId)
    {
        $request->validate([
            'status' => 'nullable|string|in:open,in_progress,resolved,closed',
            'priority' => 'nullable|string|in:low,medium,high,urgent',
            'assigned_to' => 'nullable|integer',
            'resolution_notes' => 'nullable|string',
        ]);

        $updateData = array_filter([
            'status' => $request->status,
            'priority' => $request->priority,
            'assigned_to' => $request->assigned_to,
            'resolution_notes' => $request->resolution_notes,
            'updated_at' => now(),
            'updated_by' => Auth::id()
        ]);

        $this->updateTicketData($ticketId, $updateData);

        return response()->json([
            'success' => true,
            'message' => 'Ticket updated successfully!'
        ]);
    }

    public function assignTicket(Request $request)
    {
        $request->validate([
            'ticket_id' => 'required|string',
            'helper_id' => 'required|integer',
            'notes' => 'nullable|string|max:500'
        ]);

        $result = $this->assignTicketToHelper(
            $request->ticket_id,
            $request->helper_id,
            $request->notes
        );

        return response()->json($result);
    }

    public function closeTicket(Request $request, $ticketId)
    {
        $request->validate([
            'resolution_notes' => 'required|string|max:1000',
            'satisfaction_rating' => 'nullable|integer|min:1|max:5',
            'send_notification' => 'boolean'
        ]);

        $result = $this->closeTicketWithResolution(
            $ticketId,
            $request->resolution_notes,
            $request->satisfaction_rating,
            $request->boolean('send_notification')
        );

        return response()->json($result);
    }

    public function getKnowledgeBase(Request $request)
    {
        $category = $request->get('category');
        $search = $request->get('search');
        
        $articles = $this->getKnowledgeBaseArticles($category, $search);
        
        return response()->json([
            'success' => true,
            'data' => $articles
        ]);
    }

    public function createKnowledgeBaseArticle(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category' => 'required|string',
            'tags' => 'nullable|array',
            'is_public' => 'boolean',
            'featured' => 'boolean',
        ]);

        $articleData = [
            'title' => $request->title,
            'content' => $request->content,
            'category' => $request->category,
            'tags' => $request->tags ?? [],
            'is_public' => $request->boolean('is_public'),
            'featured' => $request->boolean('featured'),
            'author_id' => Auth::id(),
            'created_at' => now()
        ];

        $articleId = $this->addKnowledgeBaseArticle($articleData);

        return response()->json([
            'success' => true,
            'message' => 'Article created successfully!',
            'article_id' => $articleId
        ]);
    }

    public function updateHelpDeskSettings(Request $request)
    {
        $request->validate([
            'default_response_time' => 'required|integer|min:1|max:168',
            'auto_assignment' => 'required|string|in:round_robin,least_busy,manual',
            'enable_email_notifications' => 'boolean',
            'enable_satisfaction_surveys' => 'boolean',
            'working_hours_start' => 'nullable|string',
            'working_hours_end' => 'nullable|string',
            'timezone' => 'nullable|string',
            'escalation_rules' => 'nullable|array',
        ]);

        $settings = [
            'default_response_time' => $request->default_response_time,
            'auto_assignment' => $request->auto_assignment,
            'enable_email_notifications' => $request->boolean('enable_email_notifications'),
            'enable_satisfaction_surveys' => $request->boolean('enable_satisfaction_surveys'),
            'working_hours_start' => $request->working_hours_start,
            'working_hours_end' => $request->working_hours_end,
            'timezone' => $request->timezone,
            'escalation_rules' => $request->escalation_rules ?? [],
            'updated_at' => now(),
            'updated_by' => Auth::id()
        ];

        $this->updateHelpDeskConfiguration($settings);

        return response()->json([
            'success' => true,
            'message' => 'Help desk settings updated successfully!'
        ]);
    }

    public function getHelperPerformance(Request $request)
    {
        $period = $request->get('period', '30d');
        $helperId = $request->get('helper_id');
        
        $performance = [
            'tickets_handled' => $this->getTicketsHandled($helperId, $period),
            'average_response_time' => $this->getAverageResponseTime($helperId, $period),
            'resolution_rate' => $this->getResolutionRate($helperId, $period),
            'satisfaction_rating' => $this->getSatisfactionRating($helperId, $period),
            'productivity_metrics' => $this->getProductivityMetrics($helperId, $period)
        ];

        return response()->json([
            'success' => true,
            'data' => $performance
        ]);
    }

    public function exportHelperData(Request $request)
    {
        $type = $request->get('type', 'helpers'); // helpers, tickets, performance
        $format = $request->get('format', 'csv');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        $data = $this->prepareHelperExportData($type, $dateFrom, $dateTo);
        $filename = "helpers_{$type}_" . date('Y-m-d_H-i-s') . ".{$format}";

        if ($format === 'csv') {
            return $this->exportAsCSV($data, $filename);
        } else {
            return $this->exportAsExcel($data, $filename);
        }
    }

    private function getAllHelpers()
    {
        return Cache::remember('all_helpers', 1800, function () {
            return [
                [
                    'id' => 1,
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'role' => 'Support Agent',
                    'status' => 'Active',
                    'tickets_handled' => 45,
                    'last_active' => '2 hours ago',
                    'departments' => ['General Support', 'Technical'],
                    'performance_rating' => 4.5
                ],
                [
                    'id' => 2,
                    'name' => 'Jane Smith',
                    'email' => '<EMAIL>',
                    'role' => 'Team Lead',
                    'status' => 'Active',
                    'tickets_handled' => 89,
                    'last_active' => '30 minutes ago',
                    'departments' => ['Management', 'Training'],
                    'performance_rating' => 4.8
                ]
            ];
        });
    }

    private function getRecentTickets($limit = 10)
    {
        return collect([
            [
                'id' => 'TICKET-001',
                'title' => 'Login Issues with Two-Factor Authentication',
                'priority' => 'High',
                'status' => 'In Progress',
                'assigned_to' => 'John Doe',
                'created_at' => Carbon::now()->subHours(2),
                'user_email' => '<EMAIL>'
            ],
            [
                'id' => 'TICKET-002',
                'title' => 'Payment Processing Error',
                'priority' => 'Medium',
                'status' => 'Open',
                'assigned_to' => null,
                'created_at' => Carbon::now()->subHours(4),
                'user_email' => '<EMAIL>'
            ]
        ]);
    }

    private function getHelpDeskStatistics()
    {
        return [
            'active_helpers' => 24,
            'open_tickets' => 156,
            'avg_response_time' => '2.3 hours',
            'satisfaction_rate' => 94.5,
            'tickets_resolved_today' => 34,
            'overdue_tickets' => 12
        ];
    }

    private function getKnowledgeBaseCategories()
    {
        return [
            ['name' => 'Getting Started', 'articles' => 12, 'views' => 1234],
            ['name' => 'Account Management', 'articles' => 8, 'views' => 892],
            ['name' => 'Payment & Billing', 'articles' => 15, 'views' => 2156],
            ['name' => 'Technical Support', 'articles' => 23, 'views' => 3421]
        ];
    }

    private function addNewHelper($helperData)
    {
        // Implementation for adding new helper
        Cache::forget('all_helpers');
        return uniqid('helper_');
    }

    private function updateHelperData($helperId, $data)
    {
        // Implementation for updating helper data
        Cache::forget('all_helpers');
    }

    private function buildTicketsQuery($priority, $status, $assignedTo, $search)
    {
        // Mock query builder - replace with actual implementation
        return collect([]);
    }

    private function generateTicketId()
    {
        return 'TICKET-' . strtoupper(uniqid());
    }

    private function addNewTicket($ticketData)
    {
        // Implementation for adding new ticket
    }

    private function updateTicketData($ticketId, $data)
    {
        // Implementation for updating ticket data
    }

    private function assignTicketToHelper($ticketId, $helperId, $notes)
    {
        // Implementation for ticket assignment
        return [
            'success' => true,
            'message' => 'Ticket assigned successfully'
        ];
    }

    private function closeTicketWithResolution($ticketId, $notes, $rating, $sendNotification)
    {
        // Implementation for closing ticket
        return [
            'success' => true,
            'message' => 'Ticket closed successfully'
        ];
    }

    private function getKnowledgeBaseArticles($category, $search)
    {
        // Implementation for getting knowledge base articles
        return [];
    }

    private function addKnowledgeBaseArticle($articleData)
    {
        // Implementation for adding knowledge base article
        return uniqid('article_');
    }

    private function updateHelpDeskConfiguration($settings)
    {
        Cache::put('helpdesk_settings', $settings, 3600);
    }

    private function getTicketsHandled($helperId, $period)
    {
        return rand(20, 100);
    }

    private function getAverageResponseTime($helperId, $period)
    {
        return rand(1, 8) . '.' . rand(0, 9) . ' hours';
    }

    private function getResolutionRate($helperId, $period)
    {
        return rand(80, 98) . '.' . rand(0, 9) . '%';
    }

    private function getSatisfactionRating($helperId, $period)
    {
        return rand(40, 50) / 10;
    }

    private function getProductivityMetrics($helperId, $period)
    {
        return [
            'tickets_per_day' => rand(5, 15),
            'first_response_time' => rand(30, 180) . ' minutes',
            'escalation_rate' => rand(2, 8) . '%'
        ];
    }

    private function prepareHelperExportData($type, $dateFrom, $dateTo)
    {
        // Implementation for preparing export data
        return [];
    }

    private function exportAsCSV($data, $filename)
    {
        // CSV export implementation
        return response()->json(['message' => 'CSV export not implemented yet']);
    }

    private function exportAsExcel($data, $filename)
    {
        // Excel export implementation
        return response()->json(['message' => 'Excel export not implemented yet']);
    }
}
