@extends('layouts.app')

@section('title', $activity->title . ' - ' . $stage->name)

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                        <svg class="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                        </svg>
                        Dashboard
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-3 h-3 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 6 10">
                            <path fill-rule="evenodd" d="m1 9 4-4-4-4" clip-rule="evenodd"/>
                        </svg>
                        <a href="{{ route('stages.area', $stage) }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600">{{ $stage->name }}</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-3 h-3 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 6 10">
                            <path fill-rule="evenodd" d="m1 9 4-4-4-4" clip-rule="evenodd"/>
                        </svg>
                        <a href="{{ route('stages.activities.index', $stage) }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600">Activities</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-3 h-3 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 6 10">
                            <path fill-rule="evenodd" d="m1 9 4-4-4-4" clip-rule="evenodd"/>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-gray-500">{{ $activity->title }}</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Activity Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-8">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $activity->type_color }}">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="{{ $activity->type_icon }}"></path>
                            </svg>
                            {{ $activity->type_display }}
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $activity->difficulty_color }}">
                            {{ $activity->difficulty_display }}
                        </span>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $activity->title }}</h1>
                    <p class="text-lg text-gray-600 mb-6">{{ $activity->description }}</p>
                    
                    <!-- Activity Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600">{{ $activity->points }}</div>
                            <div class="text-sm text-gray-500">Points</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">
                                @if($activity->duration_minutes)
                                    {{ $activity->duration_minutes >= 60 ? round($activity->duration_minutes / 60) . 'h' : $activity->duration_minutes . 'm' }}
                                @else
                                    Flexible
                                @endif
                            </div>
                            <div class="text-sm text-gray-500">Duration</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600">{{ $activity->difficulty_display }}</div>
                            <div class="text-sm text-gray-500">Difficulty</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">
                                @if($completion)
                                    {{ $completion->getCompletionPercentage() }}%
                                @else
                                    0%
                                @endif
                            </div>
                            <div class="text-sm text-gray-500">Progress</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            @if($completion)
            <div class="mb-6">
                <div class="flex items-center justify-between text-sm mb-2">
                    <span class="font-medium text-gray-700">Activity Progress</span>
                    <span class="font-medium {{ $completion->status_color }}">{{ $completion->status_display }}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="bg-green-600 h-3 rounded-full transition-all duration-300" style="width: {{ $completion->getCompletionPercentage() }}%"></div>
                </div>
                @if($completion->started_at)
                <div class="text-sm text-gray-500 mt-2">
                    Started: {{ $completion->started_at->format('M j, Y \a\t g:i A') }}
                    @if($completion->completed_at)
                        | Completed: {{ $completion->completed_at->format('M j, Y \a\t g:i A') }}
                    @endif
                </div>
                @endif
            </div>
            @endif

            <!-- Action Buttons -->
            <div class="flex space-x-4">
                @if($completion && $completion->isCompleted())
                <div class="flex items-center px-6 py-3 bg-green-50 border border-green-200 rounded-md">
                    <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-green-700 font-medium">Activity Completed!</span>
                </div>
                @elseif($completion && $completion->status === 'in_progress')
                <form method="POST" action="{{ route('stages.activities.complete', [$stage, $activity]) }}">
                    @csrf
                    <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Mark as Complete
                    </button>
                </form>
                @else
                <form method="POST" action="{{ route('stages.activities.start', [$stage, $activity]) }}">
                    @csrf
                    <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                        Start Activity
                    </button>
                </form>
                @endif
                
                <a href="{{ route('stages.activities.index', $stage) }}" class="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Back to Activities
                </a>
            </div>
        </div>
    </div>

    <!-- Activity Content -->
    @if($activity->content)
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Activity Instructions</h3>
        </div>
        <div class="px-6 py-6">
            <div class="prose max-w-none">
                {!! nl2br(e($activity->content)) !!}
            </div>
        </div>
    </div>
    @endif

    <!-- Requirements -->
    @if($activity->requirements && count($activity->requirements) > 0)
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Requirements</h3>
        </div>
        <div class="px-6 py-6">
            <ul class="list-disc list-inside space-y-2">
                @foreach($activity->requirements as $requirement)
                <li class="text-gray-700">{{ $requirement }}</li>
                @endforeach
            </ul>
        </div>
    </div>
    @endif

    <!-- Resources -->
    @if($resources->count() > 0)
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Resources</h3>
        </div>
        <div class="px-6 py-6">
            <div class="space-y-4">
                @foreach($resources as $resource)
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div class="flex items-center">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $resource->type_color }}">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="{{ $resource->type_icon }}"></path>
                            </svg>
                            {{ $resource->type_display }}
                        </span>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-900">{{ $resource->title }}</h4>
                            @if($resource->description)
                            <p class="text-sm text-gray-500">{{ $resource->description }}</p>
                            @endif
                            @if($resource->formatted_file_size)
                            <p class="text-xs text-gray-400">{{ $resource->formatted_file_size }}</p>
                            @endif
                        </div>
                    </div>
                    @if($resource->isDownloadable())
                    <a href="{{ $resource->download_url }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                       {{ $resource->type === 'link' ? 'target="_blank"' : '' }}>
                        @if($resource->type === 'link')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Visit
                        @else
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Download
                        @endif
                    </a>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Completion Notes -->
    @if($completion && $completion->notes)
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Your Notes</h3>
        </div>
        <div class="px-6 py-6">
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-gray-700">{{ $completion->notes }}</p>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
