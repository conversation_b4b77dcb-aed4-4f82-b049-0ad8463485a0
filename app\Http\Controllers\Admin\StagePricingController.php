<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MembershipStage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class StagePricingController extends Controller
{
    /**
     * Display the stage pricing management page
     */
    public function index()
    {
        $stages = MembershipStage::orderBy('sort_order')
            ->select('id', 'name', 'slug', 'activation_price', 'commission_rate', 'activation_bonus', 'description', 'benefits', 'sort_order', 'min_years', 'max_years')
            ->get();

        return view('admin.platform-customization.stage-pricing', compact('stages'));
    }

    /**
     * Update stage pricing and configuration
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'stages' => 'required|array',
            'stages.*.name' => 'required|string|max:255',
            'stages.*.pricing' => 'required|numeric|min:0',
            'stages.*.instant_commission' => 'required|numeric|min:0',
            'stages.*.bonus_commission' => 'required|numeric|min:0',
            'stages.*.min_years' => 'nullable|integer|min:0',
            'stages.*.max_years' => 'nullable|integer|min:0',
            'stages.*.description' => 'required|string',
            'stages.*.benefits' => 'required|array',
            'stages.*.benefits.*' => 'string|max:255',
        ]);

        DB::transaction(function () use ($validated) {
            foreach ($validated['stages'] as $stageNumber => $stageData) {
                $stage = MembershipStage::where('sort_order', $stageNumber)->first();
                
                if ($stage) {
                    $stage->update([
                        'name' => $stageData['name'],
                        'slug' => Str::slug($stageData['name']),
                        'activation_price' => $stageData['pricing'],
                        'commission_rate' => $stageData['instant_commission'],
                        'activation_bonus' => $stageData['bonus_commission'],
                        'min_years' => $stageData['min_years'] ?? null,
                        'max_years' => $stageData['max_years'] ?? null,
                        'description' => $stageData['description'],
                        'benefits' => $stageData['benefits'],
                    ]);
                }
            }
        });

        return response()->json([
            'success' => true,
            'message' => 'Stage pricing settings updated successfully!'
        ]);
    }

    /**
     * Get current stage data for AJAX requests
     */
    public function getStageData()
    {
        $stages = MembershipStage::orderBy('sort_order')
            ->select('id', 'name', 'slug', 'activation_price', 'commission_rate', 'activation_bonus', 'description', 'benefits', 'sort_order', 'min_years', 'max_years')
            ->get();

        return response()->json($stages);
    }

    /**
     * Reset stages to default values
     */
    public function resetToDefaults()
    {
        $defaultStages = [
            1 => [
                'name' => 'Starter',
                'slug' => 'starter',
                'activation_price' => 50.00,
                'commission_rate' => 10.00,
                'activation_bonus' => 5.00,
                'min_years' => 1,
                'max_years' => 10,
                'description' => 'Beginning your sustainable life journey with basic foundations and eco-friendly practices.',
                'benefits' => [
                    'Access to starter resources',
                    'Basic sustainability guides',
                    'Community forum access',
                    'Monthly eco-tips newsletter'
                ]
            ],
            2 => [
                'name' => 'Development',
                'slug' => 'development',
                'activation_price' => 100.00,
                'commission_rate' => 20.00,
                'activation_bonus' => 10.00,
                'min_years' => 10,
                'max_years' => 20,
                'description' => 'Developing sustainable habits and expanding your knowledge of eco-friendly living practices.',
                'benefits' => [
                    'Advanced sustainability courses',
                    'Personal eco-coach access',
                    'Green product discounts',
                    'Exclusive webinar series'
                ]
            ],
            3 => [
                'name' => 'Action',
                'slug' => 'action',
                'activation_price' => 200.00,
                'commission_rate' => 40.00,
                'activation_bonus' => 20.00,
                'min_years' => 20,
                'max_years' => 30,
                'description' => 'Taking active steps towards a fully sustainable lifestyle with practical implementation.',
                'benefits' => [
                    'Action plan templates',
                    'Implementation guides',
                    'Monthly progress reviews',
                    'Priority support access'
                ]
            ],
            4 => [
                'name' => 'Management',
                'slug' => 'management',
                'activation_price' => 350.00,
                'commission_rate' => 70.00,
                'activation_bonus' => 35.00,
                'min_years' => 30,
                'max_years' => 40,
                'description' => 'Managing and optimizing your sustainable living systems for maximum efficiency and impact.',
                'benefits' => [
                    'Advanced management tools',
                    'System optimization guides',
                    'Expert consultation calls',
                    'Leadership training access'
                ]
            ],
            5 => [
                'name' => 'Abundance',
                'slug' => 'abundance',
                'activation_price' => 500.00,
                'commission_rate' => 100.00,
                'activation_bonus' => 50.00,
                'min_years' => 40,
                'max_years' => 50,
                'description' => 'Living in abundance while maintaining sustainable practices and sharing your knowledge with others.',
                'benefits' => [
                    'Abundance mindset training',
                    'Wealth building strategies',
                    'Mentorship opportunities',
                    'Exclusive abundance community'
                ]
            ],
            6 => [
                'name' => 'Retirement',
                'slug' => 'retirement',
                'activation_price' => 1000.00,
                'commission_rate' => 200.00,
                'activation_bonus' => 100.00,
                'min_years' => 50,
                'max_years' => null,
                'description' => 'Enjoying the fruits of a lifetime of sustainable living while mentoring the next generation.',
                'benefits' => [
                    'Legacy planning tools',
                    'Retirement sustainability guides',
                    'Elder wisdom sharing platform',
                    'Lifetime achievement recognition'
                ]
            ]
        ];

        DB::transaction(function () use ($defaultStages) {
            foreach ($defaultStages as $stageNumber => $stageData) {
                $stage = MembershipStage::where('sort_order', $stageNumber)->first();
                
                if ($stage) {
                    $stage->update($stageData);
                }
            }
        });

        return response()->json([
            'success' => true,
            'message' => 'Stage pricing settings reset to defaults successfully!'
        ]);
    }
}
