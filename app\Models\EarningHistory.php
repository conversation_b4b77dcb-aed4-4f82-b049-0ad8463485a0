<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EarningHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'amount',
        'points',
        'currency',
        'description',
        'reference_type',
        'reference_id',
        'status',
        'auto_pay',
        'paid_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'points' => 'decimal:2',
        'auto_pay' => 'boolean',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the user that owns the earning.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the reference model.
     */
    public function reference()
    {
        return $this->morphTo();
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute()
    {
        return match($this->status) {
            'pending' => 'yellow',
            'approved' => 'blue',
            'paid' => 'green',
            'cancelled' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get type display text.
     */
    public function getTypeDisplayAttribute()
    {
        return match($this->type) {
            'referral' => 'Referral Commission',
            'commission' => 'Commission',
            'bonus' => 'Bonus',
            'project_completion' => 'Project Completion',
            'marketplace_sale' => 'Marketplace Sale',
            'milestone_bonus' => 'Milestone Bonus',
            'leadership_bonus' => 'Leadership Bonus',
            default => ucfirst(str_replace('_', ' ', $this->type)),
        };
    }

    /**
     * Scope for pending earnings.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved earnings.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for paid earnings.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }
}
