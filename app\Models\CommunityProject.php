<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class CommunityProject extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'user_id',
        'title',
        'description',
        'requirements',
        'amount_needed',
        'project_type',
        'project_type_id',
        'status',
        'votes_count',
        'votes_required',
        'voting_deadline',
        'project_deadline',
        'project_duration_days',
        'amount_raised',
        'volunteer_count',
        'volunteer_slots',
        'funding_goal',
        'duration_days',
        'start_date',
        'end_date',
        'team_credits',
        'completion_status',
        'admin_review_notes',
        'completed_at',
        'auto_moved_to_closed',
        'images',
        'videos',
        'documents',
        'admin_notes',
        'approved_at',
        'approved_by',
        'featured_at',
        'closed_at',
        'is_featured',
        'credibility_score'
    ];

    protected $casts = [
        'images' => 'array',
        'videos' => 'array',
        'documents' => 'array',
        'voting_deadline' => 'datetime',
        'project_deadline' => 'datetime',
        'approved_at' => 'datetime',
        'featured_at' => 'datetime',
        'closed_at' => 'datetime',
        'amount_needed' => 'decimal:2',
        'amount_raised' => 'decimal:2',
        'is_featured' => 'boolean'
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($project) {
            if (empty($project->project_id)) {
                $project->project_id = 'CP' . str_pad(static::count() + 1, 6, '0', STR_PAD_LEFT);
            }
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function votes(): HasMany
    {
        return $this->hasMany(ProjectVote::class, 'project_id');
    }

    public function supportVotes(): HasMany
    {
        return $this->hasMany(ProjectVote::class, 'project_id')->where('vote_type', 'support');
    }

    public function againstVotes(): HasMany
    {
        return $this->hasMany(ProjectVote::class, 'project_id')->where('vote_type', 'against');
    }

    public function donations(): HasMany
    {
        return $this->hasMany(ProjectDonation::class, 'project_id');
    }

    public function volunteers(): HasMany
    {
        return $this->hasMany(ProjectVolunteer::class, 'project_id');
    }

    public function stages(): HasMany
    {
        return $this->hasMany(ProjectStage::class, 'project_id')->orderBy('stage_order');
    }

    public function teamCredits(): HasMany
    {
        return $this->hasMany(ProjectTeamCredit::class, 'project_id');
    }

    public function projectType(): BelongsTo
    {
        return $this->belongsTo(ProjectType::class);
    }

    public function projectVotes(): HasMany
    {
        return $this->hasMany(ProjectVote::class);
    }

    public function projectStages(): HasMany
    {
        return $this->hasMany(ProjectStage::class)->orderBy('stage_order');
    }

    // Accessors & Mutators
    public function getProjectTypeDisplayAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->project_type));
    }

    public function getStatusDisplayAttribute(): string
    {
        return ucfirst($this->status);
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'petition' => 'bg-yellow-100 text-yellow-800',
            'featured' => 'bg-green-100 text-green-800',
            'closed' => 'bg-blue-100 text-blue-800',
            'denied' => 'bg-red-100 text-red-800',
            'cancelled' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    public function getFundingProgressAttribute(): float
    {
        if ($this->amount_needed <= 0) {
            return 0;
        }
        return min(($this->amount_raised / $this->amount_needed) * 100, 100);
    }

    public function getVotingProgressAttribute(): float
    {
        if ($this->votes_required <= 0) {
            return 0;
        }
        return min(($this->votes_count / $this->votes_required) * 100, 100);
    }

    public function getDaysRemainingAttribute(): int
    {
        if ($this->status === 'petition' && $this->voting_deadline) {
            return max(0, Carbon::now()->diffInDays($this->voting_deadline, false));
        }
        
        if ($this->status === 'featured' && $this->project_deadline) {
            return max(0, Carbon::now()->diffInDays($this->project_deadline, false));
        }
        
        return 0;
    }

    public function getIsVotingExpiredAttribute(): bool
    {
        return $this->status === 'petition' && 
               $this->voting_deadline && 
               Carbon::now()->isAfter($this->voting_deadline);
    }

    public function getIsProjectExpiredAttribute(): bool
    {
        return $this->status === 'featured' && 
               $this->project_deadline && 
               Carbon::now()->isAfter($this->project_deadline);
    }

    // Scopes
    public function scopePetition($query)
    {
        return $query->where('status', 'petition');
    }

    public function scopeFeatured($query)
    {
        return $query->where('status', 'featured');
    }

    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('project_type', $type);
    }

    public function scopePopular($query)
    {
        return $query->orderBy('votes_count', 'desc');
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Methods
    public function canUserVote(User $user): bool
    {
        return $this->status === 'petition' &&
               !$this->is_voting_expired &&
               $this->user_id !== $user->id && // Prevent creators from voting on their own projects
               !$this->votes()->where('user_id', $user->id)->exists();
    }

    public function canUserDonate(User $user): bool
    {
        return $this->status === 'featured' && 
               !$this->is_project_expired &&
               $this->amount_raised < $this->amount_needed;
    }

    public function canUserVolunteer(User $user): bool
    {
        return $this->status === 'featured' && 
               !$this->is_project_expired &&
               !$this->volunteers()->where('user_id', $user->id)->exists();
    }

    public function updateVotesCount(): void
    {
        $this->votes_count = $this->supportVotes()->count();
        $this->save();
    }

    public function updateAmountRaised(): void
    {
        $this->amount_raised = $this->donations()->where('status', 'completed')->sum('amount');
        $this->save();
    }

    public function updateVolunteerCount(): void
    {
        $this->volunteer_count = $this->volunteers()->where('status', 'approved')->count();
        $this->save();
    }

    public function checkVotingDeadline(): void
    {
        if ($this->status === 'petition' && $this->is_voting_expired) {
            if ($this->votes_count < $this->votes_required) {
                $this->status = 'denied';
                $this->save();
            }
        }
    }

    public function checkProjectDeadline(): void
    {
        if ($this->status === 'featured' && $this->is_project_expired) {
            $this->status = 'closed';
            $this->closed_at = Carbon::now();
            $this->save();
        }
    }

    public function approve(): void
    {
        $this->status = 'featured';
        $this->approved_at = Carbon::now();
        $this->featured_at = Carbon::now();

        if ($this->project_duration_days) {
            $this->project_deadline = Carbon::now()->addDays($this->project_duration_days);
        }

        $this->save();
    }

    public function deny(): void
    {
        $this->status = 'denied';
        $this->save();
    }

    public function close(): void
    {
        $this->status = 'closed';
        $this->closed_at = Carbon::now();
        $this->save();
    }
}
