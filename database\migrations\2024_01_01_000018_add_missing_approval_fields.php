<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add only missing fields to users table
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'approved_at')) {
                $table->timestamp('approved_at')->nullable()->after('admin_since');
            }
            if (!Schema::hasColumn('users', 'approved_by')) {
                $table->unsignedBigInteger('approved_by')->nullable()->after('approved_at');
                $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            }
            if (!Schema::hasColumn('users', 'approval_notes')) {
                $table->text('approval_notes')->nullable()->after('approved_by');
            }
        });

        // Add approval fields to user_stage_activations table
        Schema::table('user_stage_activations', function (Blueprint $table) {
            if (!Schema::hasColumn('user_stage_activations', 'approval_status')) {
                $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('approved')->after('is_active');
            }
            if (!Schema::hasColumn('user_stage_activations', 'approved_at')) {
                $table->timestamp('approved_at')->nullable()->after('approval_status');
            }
            if (!Schema::hasColumn('user_stage_activations', 'approved_by')) {
                $table->unsignedBigInteger('approved_by')->nullable()->after('approved_at');
                $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            }
            if (!Schema::hasColumn('user_stage_activations', 'approval_notes')) {
                $table->text('approval_notes')->nullable()->after('approved_by');
            }
        });

        // Add approval fields to commissions table
        Schema::table('commissions', function (Blueprint $table) {
            if (!Schema::hasColumn('commissions', 'approval_status')) {
                $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('approved')->after('status');
            }
            if (!Schema::hasColumn('commissions', 'approved_at')) {
                $table->timestamp('approved_at')->nullable()->after('approval_status');
            }
            if (!Schema::hasColumn('commissions', 'approved_by')) {
                $table->unsignedBigInteger('approved_by')->nullable()->after('approved_at');
                $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            }
            if (!Schema::hasColumn('commissions', 'approval_notes')) {
                $table->text('approval_notes')->nullable()->after('approved_by');
            }
        });

        // Add pricing fields to membership_stages table
        Schema::table('membership_stages', function (Blueprint $table) {
            if (!Schema::hasColumn('membership_stages', 'activation_price')) {
                $table->decimal('activation_price', 10, 2)->default(0)->after('activation_bonus');
            }
            if (!Schema::hasColumn('membership_stages', 'monthly_fee')) {
                $table->decimal('monthly_fee', 10, 2)->default(0)->after('activation_price');
            }
            if (!Schema::hasColumn('membership_stages', 'requires_approval')) {
                $table->boolean('requires_approval')->default(false)->after('monthly_fee');
            }
            if (!Schema::hasColumn('membership_stages', 'max_activations_per_user')) {
                $table->integer('max_activations_per_user')->nullable()->after('requires_approval');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'approved_by')) {
                $table->dropForeign(['approved_by']);
            }
            $table->dropColumn(['approved_at', 'approved_by', 'approval_notes']);
        });

        Schema::table('user_stage_activations', function (Blueprint $table) {
            if (Schema::hasColumn('user_stage_activations', 'approved_by')) {
                $table->dropForeign(['approved_by']);
            }
            $table->dropColumn(['approval_status', 'approved_at', 'approved_by', 'approval_notes']);
        });

        Schema::table('commissions', function (Blueprint $table) {
            if (Schema::hasColumn('commissions', 'approved_by')) {
                $table->dropForeign(['approved_by']);
            }
            $table->dropColumn(['approval_status', 'approved_at', 'approved_by', 'approval_notes']);
        });

        Schema::table('membership_stages', function (Blueprint $table) {
            $table->dropColumn(['activation_price', 'monthly_fee', 'requires_approval', 'max_activations_per_user']);
        });
    }
};
