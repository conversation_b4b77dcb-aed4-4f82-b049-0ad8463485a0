@extends('layouts.admin')

@section('title', 'Financial Overview & Management')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Financial Overview & Management</h1>
                <p class="text-gray-600 mt-1">Monitor revenue, transactions, and financial performance</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportFinancialReport()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Report
                </button>
                <button onclick="generateReport()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    Generate Report
                </button>
            </div>
        </div>
    </div>

    <!-- Financial Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">$2,456,789</h3>
                    <p class="text-sm text-gray-600">Total Revenue</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">$189,234</h3>
                    <p class="text-sm text-gray-600">Monthly Revenue</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">1,247</h3>
                    <p class="text-sm text-gray-600">Total Transactions</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">+15.3%</h3>
                    <p class="text-sm text-gray-600">Growth Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'overview' }">
                <button @click="activeTab = 'overview'" :class="activeTab === 'overview' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Revenue Overview
                </button>
                <button @click="activeTab = 'transactions'" :class="activeTab === 'transactions' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Transaction Management
                </button>
                <button @click="activeTab = 'payments'" :class="activeTab === 'payments' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Payment Settings
                </button>
                <button @click="activeTab = 'reports'" :class="activeTab === 'reports' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Financial Reports
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Analytics & Insights
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'overview' }">
        <!-- Revenue Overview Tab -->
        <div x-show="activeTab === 'overview'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Revenue Breakdown</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Stage Activations</span>
                            <span class="text-sm font-medium text-gray-900">$1,234,567 (50.2%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 50.2%"></div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Referral Commissions</span>
                            <span class="text-sm font-medium text-gray-900">$789,123 (32.1%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 32.1%"></div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Premium Features</span>
                            <span class="text-sm font-medium text-gray-900">$433,099 (17.7%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: 17.7%"></div>
                        </div>
                    </div>
                </div>

                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Monthly Trends</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">January 2024</span>
                            <span class="text-sm font-medium text-gray-900">$156,789</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">February 2024</span>
                            <span class="text-sm font-medium text-gray-900">$178,234</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">March 2024</span>
                            <span class="text-sm font-medium text-gray-900">$189,567</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">April 2024</span>
                            <span class="text-sm font-medium text-gray-900">$201,345</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">May 2024</span>
                            <span class="text-sm font-medium text-gray-900">$223,678</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">June 2024</span>
                            <span class="text-sm font-medium text-green-600">$245,890</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Management Tab -->
        <div x-show="activeTab === 'transactions'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Recent Transactions</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#TXN-001234</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Stage Activation</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$299.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-29</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="viewTransaction('TXN-001234')" class="text-indigo-600 hover:text-indigo-900 mr-3">View</button>
                                    <button onclick="refundTransaction('TXN-001234')" class="text-red-600 hover:text-red-900">Refund</button>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#TXN-001235</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Referral Commission</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$45.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-29</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="viewTransaction('TXN-001235')" class="text-indigo-600 hover:text-indigo-900 mr-3">View</button>
                                    <button onclick="approveTransaction('TXN-001235')" class="text-green-600 hover:text-green-900">Approve</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Payment Settings Tab -->
        <div x-show="activeTab === 'payments'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Payment Gateway Settings</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Primary Payment Gateway</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>Stripe</option>
                            <option>PayPal</option>
                            <option>Square</option>
                            <option>Razorpay</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>USD - US Dollar</option>
                            <option>EUR - Euro</option>
                            <option>GBP - British Pound</option>
                            <option>CAD - Canadian Dollar</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Enable Automatic Payouts</h4>
                            <p class="text-sm text-gray-500">Automatically process commission payouts</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Transaction Fees</h4>
                            <p class="text-sm text-gray-500">Charge processing fees to users</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Reports Tab -->
        <div x-show="activeTab === 'reports'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Generate Financial Reports</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option>Revenue Summary</option>
                                <option>Transaction Details</option>
                                <option>Commission Report</option>
                                <option>Tax Report</option>
                                <option>Profit & Loss</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option>Last 7 days</option>
                                <option>Last 30 days</option>
                                <option>Last 3 months</option>
                                <option>Last 6 months</option>
                                <option>Last year</option>
                                <option>Custom range</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Format</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option>PDF</option>
                                <option>Excel (XLSX)</option>
                                <option>CSV</option>
                            </select>
                        </div>
                        <button onclick="generateCustomReport()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            Generate Report
                        </button>
                    </div>
                    <div class="space-y-4">
                        <h4 class="text-sm font-medium text-gray-900">Quick Reports</h4>
                        <div class="space-y-2">
                            <button onclick="generateQuickReport('daily')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded border">Daily Revenue Report</button>
                            <button onclick="generateQuickReport('weekly')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded border">Weekly Summary</button>
                            <button onclick="generateQuickReport('monthly')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded border">Monthly Financial Report</button>
                            <button onclick="generateQuickReport('quarterly')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded border">Quarterly Analysis</button>
                            <button onclick="generateQuickReport('annual')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded border">Annual Financial Statement</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics & Insights Tab -->
        <div x-show="activeTab === 'analytics'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Performance Metrics</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Average Transaction Value</span>
                            <span class="text-sm font-medium text-gray-900">$197.45</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Conversion Rate</span>
                            <span class="text-sm font-medium text-green-600">18.7%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Customer Lifetime Value</span>
                            <span class="text-sm font-medium text-gray-900">$1,247.89</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Churn Rate</span>
                            <span class="text-sm font-medium text-red-600">3.2%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Revenue per User</span>
                            <span class="text-sm font-medium text-gray-900">$89.34</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Revenue Forecasting</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Next Month Projection</span>
                            <span class="text-sm font-medium text-green-600">$267,890</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Next Quarter Projection</span>
                            <span class="text-sm font-medium text-green-600">$789,234</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Annual Projection</span>
                            <span class="text-sm font-medium text-green-600">$3,156,789</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Growth Trend</span>
                            <span class="text-sm font-medium text-green-600">↗ Positive</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Confidence Level</span>
                            <span class="text-sm font-medium text-gray-900">87%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportFinancialReport() {
    alert('Export financial report functionality will be implemented');
}

function generateReport() {
    alert('Generate report functionality will be implemented');
}

function viewTransaction(id) {
    alert(`View transaction ${id} functionality will be implemented`);
}

function refundTransaction(id) {
    if (confirm(`Are you sure you want to refund transaction ${id}?`)) {
        alert(`Refund transaction ${id} functionality will be implemented`);
    }
}

function approveTransaction(id) {
    if (confirm(`Are you sure you want to approve transaction ${id}?`)) {
        alert(`Approve transaction ${id} functionality will be implemented`);
    }
}

function generateCustomReport() {
    alert('Generate custom report functionality will be implemented');
}

function generateQuickReport(type) {
    showNotification('Generating report...', 'info');

    const reportTypes = {
        'daily': 'revenue_summary',
        'weekly': 'revenue_summary',
        'monthly': 'revenue_summary',
        'quarterly': 'profit_loss',
        'annual': 'profit_loss'
    };

    const dateRanges = {
        'daily': '7d',
        'weekly': '30d',
        'monthly': '90d',
        'quarterly': '90d',
        'annual': '1y'
    };

    fetch('/admin/financial-overview/generate-report', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            report_type: reportTypes[type] || 'revenue_summary',
            date_range: dateRanges[type] || '30d',
            format: 'pdf'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Report generated successfully!', 'success');
            window.open(data.download_url, '_blank');
        } else {
            showNotification(data.message || 'Report generation failed', 'error');
        }
    })
    .catch(error => {
        console.error('Report generation error:', error);
        showNotification('Report generation failed', 'error');
    });
}

// Financial Management System
const financialManagement = {
    data: {
        revenue: [],
        transactions: [],
        analytics: {},
        settings: {}
    },

    charts: {
        revenue: null,
        breakdown: null,
        trends: null
    },

    init() {
        this.loadFinancialData();
        this.setupEventListeners();
        this.initializeCharts();
        this.startRealTimeUpdates();
    },

    loadFinancialData() {
        Promise.all([
            this.loadRevenueData(),
            this.loadTransactions(),
            this.loadAnalytics(),
            this.loadSettings()
        ]).then(() => {
            this.updateDashboard();
        });
    },

    loadRevenueData(period = '30d') {
        return fetch(`/admin/financial-overview/revenue-data?period=${period}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.revenue = data.data;
                    this.updateRevenueChart();
                }
            });
    },

    loadTransactions(page = 1, filters = {}) {
        const params = new URLSearchParams({ page, ...filters });

        return fetch(`/admin/financial-overview/transactions?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.transactions = data.data;
                    this.updateTransactionsTable();
                    this.updatePagination(data.pagination);
                }
            });
    },

    loadAnalytics(period = '30d') {
        return fetch(`/admin/financial-overview/analytics?period=${period}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.analytics = data.data;
                    this.updateAnalyticsDisplay();
                }
            });
    },

    loadSettings() {
        return fetch('/admin/financial-overview/settings')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.settings = data.data;
                    this.populateSettingsForm();
                }
            });
    },

    setupEventListeners() {
        // Period selector changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('#period-selector')) {
                this.loadRevenueData(e.target.value);
                this.loadAnalytics(e.target.value);
            }
        });

        // Transaction filters
        document.addEventListener('change', (e) => {
            if (e.target.matches('.transaction-filter')) {
                this.applyTransactionFilters();
            }
        });

        // Real-time search
        document.addEventListener('input', (e) => {
            if (e.target.matches('#transaction-search')) {
                this.debounce(() => this.searchTransactions(e.target.value), 300);
            }
        });
    },

    initializeCharts() {
        // Initialize Chart.js charts
        this.initRevenueChart();
        this.initBreakdownChart();
        this.initTrendsChart();
    },

    startRealTimeUpdates() {
        // Update financial data every 5 minutes
        setInterval(() => {
            this.loadFinancialData();
        }, 300000);

        // Update key metrics every minute
        setInterval(() => {
            this.updateKeyMetrics();
        }, 60000);
    },

    updateDashboard() {
        this.updateRevenueChart();
        this.updateBreakdownChart();
        this.updateTransactionsTable();
        this.updateKeyMetrics();
    },

    debounce(func, wait) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(func, wait);
    }
};

// Transaction Management Functions
function viewTransaction(transactionId) {
    fetch(`/admin/financial-overview/transactions/${transactionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showTransactionModal(data.data);
            }
        });
}

function refundTransaction(transactionId) {
    const amount = prompt('Enter refund amount (leave empty for full refund):');
    const reason = prompt('Reason for refund:');

    if (reason) {
        fetch('/admin/financial-overview/refund', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                transaction_id: transactionId,
                amount: amount || null,
                reason: reason,
                notify_user: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Refund processed successfully!', 'success');
                financialManagement.loadTransactions();
            } else {
                showNotification(data.message || 'Refund failed', 'error');
            }
        });
    }
}

function approveTransaction(transactionId) {
    if (confirm('Are you sure you want to approve this transaction?')) {
        fetch(`/admin/financial-overview/transactions/${transactionId}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Transaction approved!', 'success');
                financialManagement.loadTransactions();
            }
        });
    }
}

// Payment Settings Functions
function updatePaymentSettings() {
    const formData = new FormData(document.querySelector('#paymentSettingsForm'));

    fetch('/admin/financial-overview/payment-settings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Payment settings updated!', 'success');
        } else {
            showNotification(data.message || 'Update failed', 'error');
        }
    });
}

function testPaymentGateway(gateway) {
    showNotification('Testing payment gateway...', 'info');

    fetch(`/admin/financial-overview/test-gateway/${gateway}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`${gateway} connection successful!`, 'success');
        } else {
            showNotification(`${gateway} connection failed: ${data.message}`, 'error');
        }
    });
}

// Report Generation Functions
function generateCustomReport() {
    const reportType = document.querySelector('#report_type').value;
    const dateRange = document.querySelector('#date_range').value;
    const format = document.querySelector('#report_format').value;

    let dateFrom = null;
    let dateTo = null;

    if (dateRange === 'custom') {
        dateFrom = document.querySelector('#date_from').value;
        dateTo = document.querySelector('#date_to').value;

        if (!dateFrom || !dateTo) {
            showNotification('Please select custom date range', 'warning');
            return;
        }
    }

    showNotification('Generating report...', 'info');

    fetch('/admin/financial-overview/generate-report', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            report_type: reportType,
            date_range: dateRange,
            date_from: dateFrom,
            date_to: dateTo,
            format: format
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Report generated successfully!', 'success');
            window.open(data.download_url, '_blank');
        } else {
            showNotification(data.message || 'Report generation failed', 'error');
        }
    });
}

// Export Functions
function exportTransactions() {
    const filters = {
        status: document.querySelector('#transaction_status_filter')?.value,
        type: document.querySelector('#transaction_type_filter')?.value,
        date_from: document.querySelector('#export_date_from')?.value,
        date_to: document.querySelector('#export_date_to')?.value,
        format: document.querySelector('#export_format')?.value || 'csv'
    };

    const params = new URLSearchParams(filters);
    window.open(`/admin/financial-overview/export-transactions?${params}`, '_blank');
    showNotification('Export started. Download will begin shortly.', 'info');
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatDate(date) {
    return new Date(date).toLocaleDateString();
}

function formatDateTime(date) {
    return new Date(date).toLocaleString();
}

function showTransactionModal(transaction) {
    // Implementation for showing transaction details modal
    alert(`Transaction Details for ${transaction.id} - Modal implementation needed`);
}

// Financial Tab Management System
const financialTabManager = {
    currentTab: 'revenue',

    init() {
        this.setupTabListeners();
        this.loadTabContent(this.currentTab);
    },

    setupTabListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-financial-tab]')) {
                e.preventDefault();
                const tabId = e.target.dataset.financialTab;
                this.switchTab(tabId);
            }
        });
    },

    switchTab(tabId) {
        // Update active tab
        document.querySelectorAll('[data-financial-tab]').forEach(tab => {
            tab.classList.remove('border-blue-500', 'text-blue-600');
            tab.classList.add('border-transparent', 'text-gray-500');
        });

        const activeTab = document.querySelector(`[data-financial-tab="${tabId}"]`);
        if (activeTab) {
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-blue-500', 'text-blue-600');
        }

        // Hide all tab content
        document.querySelectorAll('[data-financial-content]').forEach(content => {
            content.classList.add('hidden');
        });

        // Show selected tab content
        const activeContent = document.querySelector(`[data-financial-content="${tabId}"]`);
        if (activeContent) {
            activeContent.classList.remove('hidden');
        }

        this.currentTab = tabId;
        this.loadTabContent(tabId);
    },

    loadTabContent(tabId) {
        switch(tabId) {
            case 'revenue':
                this.loadRevenueContent();
                break;
            case 'transactions':
                this.loadTransactionsContent();
                break;
            case 'reports':
                this.loadReportsContent();
                break;
            case 'analytics':
                this.loadAnalyticsContent();
                break;
            case 'settings':
                this.loadSettingsContent();
                break;
        }
    },

    loadRevenueContent() {
        fetch('/admin/financial-overview/revenue-analytics')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateRevenueDisplay(data.data);
                }
            });
    },

    loadTransactionsContent() {
        fetch('/admin/financial-overview/transactions-summary')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateTransactionsDisplay(data.data);
                }
            });
    },

    loadReportsContent() {
        fetch('/admin/financial-overview/reports-list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateReportsDisplay(data.data);
                }
            });
    },

    loadAnalyticsContent() {
        fetch('/admin/financial-overview/analytics-dashboard')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateAnalyticsDisplay(data.data);
                }
            });
    },

    loadSettingsContent() {
        fetch('/admin/financial-overview/payment-settings')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateSettingsDisplay(data.data);
                }
            });
    },

    updateRevenueDisplay(data) {
        // Update revenue charts and metrics
        if (window.revenueChart) {
            window.revenueChart.data.datasets[0].data = data.revenue_data || [];
            window.revenueChart.update();
        }
    },

    updateTransactionsDisplay(data) {
        // Update transactions table
        const container = document.querySelector('#transactions-container');
        if (container && data.transactions) {
            container.innerHTML = this.generateTransactionsHTML(data.transactions);
        }
    },

    updateReportsDisplay(data) {
        // Update reports list
        const container = document.querySelector('#reports-container');
        if (container && data.reports) {
            container.innerHTML = this.generateReportsHTML(data.reports);
        }
    },

    updateAnalyticsDisplay(data) {
        // Update analytics dashboard
        if (data.analytics) {
            this.updateAnalyticsCharts(data.analytics);
        }
    },

    updateSettingsDisplay(data) {
        // Update settings form
        Object.keys(data).forEach(key => {
            const element = document.querySelector(`[name="financial_${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = data[key];
                } else {
                    element.value = data[key];
                }
            }
        });
    },

    generateTransactionsHTML(transactions) {
        return transactions.map(transaction => `
            <div class="border border-gray-200 rounded-lg p-4 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium">${transaction.id}</span>
                    <span class="text-sm px-2 py-1 rounded ${this.getStatusClass(transaction.status)}">
                        ${transaction.status}
                    </span>
                </div>
                <div class="text-sm text-gray-600">
                    Amount: ${formatCurrency(transaction.amount)} |
                    User: ${transaction.user_email} |
                    Date: ${formatDate(transaction.created_at)}
                </div>
                <div class="mt-2 flex space-x-2">
                    <button onclick="viewTransaction('${transaction.id}')" class="text-xs text-blue-600 hover:text-blue-900">View</button>
                    <button onclick="refundTransaction('${transaction.id}')" class="text-xs text-red-600 hover:text-red-900">Refund</button>
                </div>
            </div>
        `).join('');
    },

    generateReportsHTML(reports) {
        return reports.map(report => `
            <div class="border border-gray-200 rounded-lg p-4 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium">${report.name}</span>
                    <span class="text-sm text-gray-500">${report.type}</span>
                </div>
                <div class="text-sm text-gray-600 mb-2">${report.description}</div>
                <div class="flex space-x-2">
                    <button onclick="generateReport('${report.id}')" class="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">Generate</button>
                    <button onclick="scheduleReport('${report.id}')" class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">Schedule</button>
                </div>
            </div>
        `).join('');
    },

    getStatusClass(status) {
        switch(status) {
            case 'completed': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'failed': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    },

    updateAnalyticsCharts(analytics) {
        // Update various analytics charts
        if (window.profitChart && analytics.profit_data) {
            window.profitChart.data.datasets[0].data = analytics.profit_data;
            window.profitChart.update();
        }

        if (window.conversionChart && analytics.conversion_data) {
            window.conversionChart.data.datasets[0].data = analytics.conversion_data;
            window.conversionChart.update();
        }
    }
};

// Financial sub-menu specific functions
function saveRevenueSettings() {
    const formData = new FormData(document.querySelector('#revenue-settings-form'));

    fetch('/admin/financial-overview/revenue-settings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Revenue settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function savePaymentSettings() {
    const formData = new FormData(document.querySelector('#payment-settings-form'));

    fetch('/admin/financial-overview/payment-settings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Payment settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function generateFinancialReport(reportType) {
    const dateFrom = document.querySelector('#report-date-from')?.value;
    const dateTo = document.querySelector('#report-date-to')?.value;
    const format = document.querySelector('#report-format')?.value || 'pdf';

    showNotification('Generating financial report...', 'info');

    fetch('/admin/financial-overview/generate-report', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            report_type: reportType,
            date_from: dateFrom,
            date_to: dateTo,
            format: format
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Report generated successfully!', 'success');
            window.open(data.download_url, '_blank');
        } else {
            showNotification(data.message || 'Report generation failed', 'error');
        }
    });
}

function scheduleReport(reportId) {
    const frequency = prompt('Enter schedule frequency (daily, weekly, monthly):');
    const email = prompt('Enter email address for delivery:');

    if (frequency && email) {
        fetch('/admin/financial-overview/schedule-report', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                report_id: reportId,
                frequency: frequency,
                email: email
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Report scheduled successfully!', 'success');
            } else {
                showNotification(data.message || 'Scheduling failed', 'error');
            }
        });
    }
}

function exportFinancialData() {
    const type = document.querySelector('#export-type')?.value || 'transactions';
    const format = document.querySelector('#export-format')?.value || 'csv';
    const dateFrom = document.querySelector('#export-date-from')?.value;
    const dateTo = document.querySelector('#export-date-to')?.value;

    const params = new URLSearchParams({ type, format });
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    window.open(`/admin/financial-overview/export?${params}`, '_blank');
    showNotification('Export started. Download will begin shortly.', 'info');
}

function runFinancialAnalysis() {
    showNotification('Running financial analysis...', 'info');

    fetch('/admin/financial-overview/run-analysis', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Financial analysis completed!', 'success');
            displayAnalysisResults(data.results);
        } else {
            showNotification(data.message || 'Analysis failed', 'error');
        }
    });
}

function displayAnalysisResults(results) {
    const resultsContainer = document.querySelector('#analysis-results');
    if (!resultsContainer) return;

    resultsContainer.innerHTML = `
        <div class="bg-white rounded-lg p-6 mt-4">
            <h3 class="text-lg font-medium mb-4">Financial Analysis Results</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">${formatCurrency(results.total_revenue || 0)}</div>
                    <div class="text-sm text-green-700">Total Revenue</div>
                </div>
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">${results.growth_rate || 0}%</div>
                    <div class="text-sm text-blue-700">Growth Rate</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">${formatCurrency(results.profit_margin || 0)}</div>
                    <div class="text-sm text-purple-700">Profit Margin</div>
                </div>
            </div>
            <div class="space-y-3">
                ${(results.insights || []).map(insight => `
                    <div class="flex items-center p-3 rounded-lg bg-gray-50">
                        <div class="flex-1">
                            <div class="font-medium">${insight.title}</div>
                            <div class="text-sm text-gray-600">${insight.description}</div>
                        </div>
                        <div class="ml-4 text-2xl">
                            ${insight.type === 'positive' ? '📈' : insight.type === 'negative' ? '📉' : 'ℹ️'}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    financialManagement.init();
    financialTabManager.init();
});
</script>
@endsection
