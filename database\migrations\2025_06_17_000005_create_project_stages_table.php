<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_stages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('community_projects')->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->integer('stage_order');
            $table->decimal('target_amount', 15, 2)->nullable();
            $table->integer('target_days')->nullable();
            $table->enum('status', ['pending', 'in_progress', 'completed', 'approved', 'rejected'])->default('pending');
            $table->json('completion_images')->nullable();
            $table->text('completion_notes')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->datetime('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->text('admin_feedback')->nullable();
            $table->timestamps();
            
            $table->index(['project_id', 'stage_order']);
            $table->index(['project_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_stages');
    }
};
