<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StageResource extends Model
{
    use HasFactory;

    protected $fillable = [
        'stage_id',
        'title',
        'description',
        'type',
        'url',
        'file_path',
        'is_downloadable',
        'order',
        'uploaded_by',
    ];

    protected $casts = [
        'is_downloadable' => 'boolean',
    ];

    /**
     * Get the stage this resource belongs to
     */
    public function stage()
    {
        return $this->belongsTo(MembershipStage::class, 'stage_id');
    }

    /**
     * Get the admin that uploaded the resource
     */
    public function uploadedBy()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scope for ordered resources
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order', 'asc');
    }

    /**
     * Get the file size if it's a file
     */
    public function getFileSizeAttribute()
    {
        if ($this->file_path && file_exists(storage_path('app/' . $this->file_path))) {
            return filesize(storage_path('app/' . $this->file_path));
        }
        return null;
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute()
    {
        $size = $this->file_size;
        if (!$size) return null;

        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }
}
