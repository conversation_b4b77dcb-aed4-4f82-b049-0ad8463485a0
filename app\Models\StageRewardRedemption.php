<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StageRewardRedemption extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'stage_reward_id',
        'progress_id',
        'amount_awarded',
        'points_awarded',
        'status',
        'redeemed_at',
        'approved_at',
        'paid_at',
    ];

    protected $casts = [
        'amount_awarded' => 'decimal:2',
        'redeemed_at' => 'datetime',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the stage reward.
     */
    public function stageReward()
    {
        return $this->belongsTo(StageReward::class);
    }

    /**
     * Get the progress record.
     */
    public function progress()
    {
        return $this->belongsTo(UserStageRewardProgress::class, 'progress_id');
    }

    /**
     * Scope for pending redemptions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved redemptions.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for paid redemptions.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Approve the redemption.
     */
    public function approve()
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
        ]);
    }

    /**
     * Mark as paid.
     */
    public function markAsPaid()
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);
    }
}
