<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Services\NotificationService;
use Illuminate\Support\Str;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'email',
        'password',
        'referral_code',
        'referred_by',
        'membership_tier',
        'total_earnings',
        'available_balance',
        'total_referrals',
        'phone',
        'bio',
        'location',
        'website',
        'address',
        'city',
        'state',
        'country',
        'reward_points',
        'is_active',
        'is_admin',
        'is_top_admin',
        'admin_status',
        'admin_since',
        'account_status',
        'approved_at',
        'approved_by',
        'approval_notes',
        'activated_at',
        'light_member_activated_at',
        'preferred_language',
        'admin_permissions',
        'bank_name',
        'account_holder_name',
        'account_number',
        'routing_number',
        'swift_code',
        'iban',
        'bank_address',
        'bank_details_verified',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'activated_at' => 'datetime',
            'light_member_activated_at' => 'datetime',
            'admin_since' => 'datetime',
            'approved_at' => 'datetime',
            'total_earnings' => 'decimal:2',
            'available_balance' => 'decimal:2',
            'reward_points' => 'decimal:2',
            'is_active' => 'boolean',
            'is_admin' => 'boolean',
            'is_top_admin' => 'boolean',
            'admin_permissions' => 'array',
            'bank_details_verified' => 'boolean',
        ];
    }

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        if ($this->first_name && $this->last_name) {
            return $this->first_name . ' ' . $this->last_name;
        }
        return $this->name ?: '';
    }

    /**
     * Check if user has bank details.
     */
    public function hasBankDetails(): bool
    {
        return !empty($this->bank_name) && !empty($this->account_number);
    }

    /**
     * Get masked account number for display.
     */
    public function getMaskedAccountNumberAttribute(): string
    {
        if (empty($this->account_number)) {
            return '';
        }

        $length = strlen($this->account_number);
        if ($length <= 4) {
            return str_repeat('*', $length);
        }

        return str_repeat('*', $length - 4) . substr($this->account_number, -4);
    }

    /**
     * Get community projects created by this user.
     */
    public function communityProjects()
    {
        return $this->hasMany(CommunityProject::class);
    }

    /**
     * Get project donations made by this user.
     */
    public function projectDonations()
    {
        return $this->hasMany(ProjectDonation::class);
    }

    /**
     * Get project volunteer applications by this user.
     */
    public function projectVolunteers()
    {
        return $this->hasMany(ProjectVolunteer::class);
    }

    /**
     * Get user's credibility score.
     */
    public function credibilityScore()
    {
        return $this->hasOne(UserCredibilityScore::class);
    }

    public function points()
    {
        return $this->hasMany(UserPoint::class);
    }

    public function dailyVisits()
    {
        return $this->hasMany(DailyVisit::class);
    }

    public function getTotalPointsAttribute()
    {
        return $this->points()->where('is_redeemed', false)->sum('points');
    }

    public function getRedeemedPointsAttribute()
    {
        return $this->points()->where('is_redeemed', true)->sum('points');
    }

    /**
     * Get user's project participations.
     */
    public function projectParticipations()
    {
        return $this->hasMany(ProjectParticipation::class);
    }

    /**
     * Get user's earning history.
     */
    public function earningHistory()
    {
        return $this->hasMany(EarningHistory::class);
    }

    /**
     * Get user's marketplace products.
     */
    public function marketplaceProducts()
    {
        return $this->hasMany(MarketplaceProduct::class);
    }

    /**
     * Get user's marketplace orders as buyer.
     */
    public function buyerOrders()
    {
        return $this->hasMany(MarketplaceOrder::class, 'buyer_id');
    }

    /**
     * Get user's marketplace orders as seller.
     */
    public function sellerOrders()
    {
        return $this->hasMany(MarketplaceOrder::class, 'seller_id');
    }

    /**
     * Get user's notifications.
     */
    public function notifications()
    {
        return $this->hasMany(UserNotification::class);
    }

    /**
     * Get user's unread notifications.
     */
    public function unreadNotifications()
    {
        return $this->notifications()->unread();
    }

    /**
     * Get user's messages as sender.
     */
    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * Get user's messages as recipient.
     */
    public function receivedMessages()
    {
        return $this->hasMany(Message::class, 'recipient_id');
    }

    /**
     * Get unread message count.
     */
    public function getUnreadMessageCountAttribute()
    {
        return $this->receivedMessages()->unread()->count();
    }

    /**
     * Get unread notification count.
     */
    public function getUnreadNotificationCountAttribute()
    {
        return $this->unreadNotifications()->count();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->referral_code)) {
                $user->referral_code = $user->generateUniqueReferralCode();
            }

            // Make the first registered user the Top Admin
            if (self::count() === 0) {
                $user->is_top_admin = true;
                $user->is_admin = true;
                $user->admin_status = 'approved';
                $user->admin_since = now();
            } else {
                // Auto-assign to top admin if no referrer specified and not the first user
                if (empty($user->referred_by)) {
                    $topAdmin = self::where('is_top_admin', true)->first();
                    if ($topAdmin) {
                        $user->referred_by = $topAdmin->id;
                    }
                }
            }
        });
    }

    /**
     * Generate a unique referral code.
     */
    private function generateUniqueReferralCode(): string
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (self::where('referral_code', $code)->exists());

        return $code;
    }

    /**
     * Get the user who referred this user.
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referred_by');
    }

    /**
     * Get all users referred by this user.
     */
    public function referrals()
    {
        return $this->hasMany(User::class, 'referred_by');
    }

    /**
     * Get active referrals.
     */
    public function activeReferrals()
    {
        return $this->referrals()->where('is_active', true);
    }

    /**
     * Get referral relationships where this user is the referrer.
     */
    public function referralRelationships()
    {
        return $this->hasMany(\App\Models\Referral::class, 'referrer_id');
    }

    /**
     * Get commissions earned by this user.
     */
    public function commissions()
    {
        return $this->hasMany(\App\Models\Commission::class);
    }

    /**
     * Get withdrawal requests by this user.
     */
    public function withdrawals()
    {
        return $this->hasMany(\App\Models\Withdrawal::class);
    }

    /**
     * Get bonus payments for this user.
     */
    public function bonusPayments()
    {
        return $this->hasMany(\App\Models\BonusPayment::class);
    }

    /**
     * Get milestone tracking for this user.
     */
    public function milestoneTracking()
    {
        return $this->hasMany(\App\Models\MilestoneTracking::class);
    }

    /**
     * Get all stage activations for this user.
     */
    public function stageActivations()
    {
        return $this->hasMany(\App\Models\UserStageActivation::class);
    }

    /**
     * Get active stage activations for this user.
     */
    public function activeStageActivations()
    {
        return $this->stageActivations()->active()->with('membershipStage');
    }

    /**
     * Get all activated membership stages.
     */
    public function getActivatedStagesAttribute()
    {
        return $this->activeStageActivations()->get()->pluck('membershipStage');
    }

    /**
     * Check if user has activated a specific stage.
     */
    public function hasActivatedStage(string $stageSlug): bool
    {
        return $this->activeStageActivations()
            ->whereHas('membershipStage', function ($query) use ($stageSlug) {
                $query->where('slug', $stageSlug);
            })
            ->exists();
    }

    /**
     * Get stage activation for a specific stage.
     */
    public function getStageActivation(string $stageSlug)
    {
        return $this->stageActivations()
            ->whereHas('membershipStage', function ($query) use ($stageSlug) {
                $query->where('slug', $stageSlug);
            })
            ->first();
    }

    /**
     * Calculate total active referrals.
     */
    public function getTotalActiveReferralsAttribute(): int
    {
        return $this->activeReferrals()->count();
    }

    /**
     * Get the next available stage to activate (multi-stage system).
     */
    public function getNextAvailableStageAttribute(): ?\App\Models\MembershipStage
    {
        $stages = \App\Models\MembershipStage::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Find the first stage that's not activated
        foreach ($stages as $stage) {
            if (!$this->hasActivatedStage($stage->slug)) {
                return $stage;
            }
        }

        return null; // All stages activated
    }

    /**
     * Check if user can upgrade to next stage.
     */
    public function canUpgradeStage(): bool
    {
        $nextStage = $this->next_stage;
        return $nextStage && $this->total_active_referrals >= $nextStage->min_referrals;
    }

    /**
     * Upgrade user to next membership stage.
     */
    public function upgradeStage(): bool
    {
        if (!$this->canUpgradeStage()) {
            return false;
        }

        $nextStage = $this->next_stage;
        $this->membership_stage = $nextStage->slug;
        $this->save();

        // Award activation bonus if applicable
        if ($nextStage->activation_bonus > 0) {
            $this->available_balance += $nextStage->activation_bonus;
            $this->total_earnings += $nextStage->activation_bonus;
            $this->save();
        }

        return true;
    }

    /**
     * Activate user to a specific stage (can activate multiple stages).
     */
    public function activateStage(string $stageSlug): bool
    {
        $stage = \App\Models\MembershipStage::where('slug', $stageSlug)
            ->where('is_active', true)
            ->first();

        if (!$stage) {
            return false;
        }

        // Check if already activated
        if ($this->hasActivatedStage($stageSlug)) {
            return false; // Already activated
        }

        // If Earth-friendly member, upgrade to Light member on first activation
        if ($this->isEarthFriendlyMember()) {
            $this->membership_tier = 'light';
            $this->light_member_activated_at = now();
            $this->save();

            // Initialize milestone tracking
            $this->initializeMilestoneTracking();
        }

        // Create stage activation record
        $activation = \App\Models\UserStageActivation::create([
            'user_id' => $this->id,
            'membership_stage_id' => $stage->id,
            'is_active' => true,
            'activation_bonus_paid' => $stage->activation_bonus,
            'activated_at' => now(),
        ]);

        // Award activation bonus
        if ($stage->activation_bonus > 0) {
            $this->available_balance += $stage->activation_bonus;
            $this->total_earnings += $stage->activation_bonus;
            $this->save();
        }

        // Award referrer commission for this activation
        $this->awardReferrerCommissionForActivation($activation);

        return true;
    }

    /**
     * Deactivate a specific stage.
     */
    public function deactivateStage(string $stageSlug): bool
    {
        $activation = $this->getStageActivation($stageSlug);

        if (!$activation || !$activation->is_active) {
            return false;
        }

        $activation->deactivate();
        return true;
    }

    /**
     * Check if user can activate a specific stage.
     */
    public function canActivateStage(string $stageSlug): bool
    {
        $stage = \App\Models\MembershipStage::where('slug', $stageSlug)
            ->where('is_active', true)
            ->first();

        return $stage !== null && !$this->hasActivatedStage($stageSlug);
    }

    /**
     * Award referrer commission for stage activation.
     */
    private function awardReferrerCommissionForActivation(\App\Models\UserStageActivation $activation): void
    {
        if (!$this->referred_by) {
            return;
        }

        $referrer = User::find($this->referred_by);
        if (!$referrer || !$referrer->isLightMember()) {
            return;
        }

        $stage = $activation->membershipStage;

        // Check if referrer has this stage activated to earn commission
        if ($referrer->hasActivatedStage($stage->slug)) {
            $referrerActivation = $referrer->getStageActivation($stage->slug);

            \App\Models\Commission::create([
                'user_id' => $referrer->id,
                'stage_activation_id' => $referrerActivation->id,
                'stage_slug' => $stage->slug,
                'amount' => $stage->commission_rate,
                'description' => "Referral activation commission for {$this->name} activating {$stage->name} stage",
                'status' => 'paid',
            ]);

            // Update referrer balance
            $referrer->increment('available_balance', $stage->commission_rate);
            $referrer->increment('total_earnings', $stage->commission_rate);
        }
    }

    /**
     * Check if user is an Earth-friendly member.
     */
    public function isEarthFriendlyMember(): bool
    {
        return $this->membership_tier === 'earthfriendly';
    }

    /**
     * Check if user is a Light member.
     */
    public function isLightMember(): bool
    {
        return $this->membership_tier === 'light';
    }

    /**
     * Check if user is top admin.
     */
    public function isTopAdmin(): bool
    {
        return $this->is_top_admin === true;
    }

    /**
     * Check if user is approved admin.
     */
    public function isApprovedAdmin(): bool
    {
        return $this->is_admin && $this->admin_status === 'approved';
    }

    /**
     * Check if user is pending admin approval.
     */
    public function isPendingAdmin(): bool
    {
        return $this->admin_status === 'pending';
    }

    /**
     * User who approved this user.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Users approved by this user.
     */
    public function approvedUsers()
    {
        return $this->hasMany(User::class, 'approved_by');
    }

    /**
     * Check if user account is approved.
     */
    public function isAccountApproved(): bool
    {
        return $this->account_status === 'approved';
    }

    /**
     * Check if user account is pending approval.
     */
    public function isAccountPending(): bool
    {
        return $this->account_status === 'pending';
    }

    /**
     * Check if user can be deleted/deactivated by another admin.
     */
    public function canBeModifiedBy(User $admin): bool
    {
        // Top admin cannot be modified by anyone
        if ($this->is_top_admin) {
            return false;
        }

        // Only top admin can modify other admins
        if ($this->is_admin && !$admin->is_top_admin) {
            return false;
        }

        return true;
    }

    /**
     * Activate user to Light member status.
     */
    public function activateToLightMember(string $stageSlug): bool
    {
        $stage = \App\Models\MembershipStage::where('slug', $stageSlug)
            ->where('is_active', true)
            ->first();

        if (!$stage) {
            return false;
        }

        // Update to Light member and set stage
        $this->membership_tier = 'light';
        $this->membership_stage = $stage->slug;
        $this->light_member_activated_at = now();
        $this->save();

        // Award activation bonus
        if ($stage->activation_bonus > 0) {
            $this->available_balance += $stage->activation_bonus;
            $this->total_earnings += $stage->activation_bonus;
            $this->save();
        }

        // Initialize milestone tracking
        $this->initializeMilestoneTracking();

        return true;
    }

    /**
     * Initialize milestone tracking for new Light members.
     */
    public function initializeMilestoneTracking(): void
    {
        $milestones = \App\Models\MilestoneTracking::getDefaultMilestones();

        foreach ($milestones as $count => $bonus) {
            \App\Models\MilestoneTracking::firstOrCreate([
                'user_id' => $this->id,
                'milestone_count' => $count,
            ], [
                'bonus_amount' => $bonus,
                'achieved' => false,
            ]);
        }
    }

    /**
     * Check and award milestone bonuses.
     */
    public function checkMilestoneAchievements(): void
    {
        $currentReferrals = $this->total_active_referrals;

        $pendingMilestones = $this->milestoneTracking()
            ->where('achieved', false)
            ->where('milestone_count', '<=', $currentReferrals)
            ->get();

        foreach ($pendingMilestones as $milestone) {
            $milestone->markAsAchieved();
        }
    }

    /**
     * Get total bonus earnings.
     */
    public function getTotalBonusEarningsAttribute(): float
    {
        return $this->bonusPayments()->paid()->sum('amount');
    }

    /**
     * Get pending bonus earnings.
     */
    public function getPendingBonusEarningsAttribute(): float
    {
        return $this->bonusPayments()->pending()->sum('amount');
    }

    /**
     * Get user's wallets.
     */
    public function wallets()
    {
        return $this->hasMany(\App\Models\Wallet::class);
    }

    /**
     * Get user's active wallets.
     */
    public function activeWallets()
    {
        return $this->wallets()->where('is_active', true)->with('currency');
    }

    /**
     * Get wallet for specific currency.
     */
    public function getWallet($currencyCode)
    {
        return $this->wallets()
            ->whereHas('currency', function($q) use ($currencyCode) {
                $q->where('code', $currencyCode);
            })
            ->first();
    }

    /**
     * Get or create wallet for currency.
     */
    public function getOrCreateWallet($currencyCode)
    {
        $wallet = $this->getWallet($currencyCode);

        if (!$wallet) {
            $currency = \App\Models\Currency::where('code', $currencyCode)->first();
            if ($currency) {
                $wallet = $this->wallets()->create([
                    'currency_id' => $currency->id,
                    'balance' => 0,
                    'pending_balance' => 0,
                    'frozen_balance' => 0,
                    'is_active' => true,
                ]);
            }
        }

        return $wallet;
    }

    /**
     * Get primary wallet (default currency).
     */
    public function getPrimaryWallet()
    {
        $defaultCurrency = \App\Models\Currency::getDefault();
        return $this->getOrCreateWallet($defaultCurrency->code);
    }

    /**
     * Get total balance in USD.
     */
    public function getTotalBalanceUsd()
    {
        $total = 0;
        foreach ($this->activeWallets as $wallet) {
            $total += $wallet->currency->convertToUsd($wallet->balance);
        }
        return $total;
    }

    /**
     * Get the user's activity completions.
     */
    public function activityCompletions(): HasMany
    {
        return $this->hasMany(UserActivityCompletion::class);
    }

    /**
     * Check if user has access to a stage.
     */
    public function hasAccessToStage(MembershipStage $stage): bool
    {
        // Check if user has an active activation for this stage
        return $this->activeStageActivations()
            ->where('membership_stage_id', $stage->id)
            ->exists();
    }

    /**
     * Get default admin permissions.
     */
    public static function getDefaultAdminPermissions(): array
    {
        return [
            'user_management' => true,
            'currency_management' => true,
            'language_management' => true,
            'financial_overview' => true,
            'approval_management' => true,
            'stage_pricing' => false, // Top Admin only
            'admin_approval' => false, // Top Admin only
            'system_settings' => false, // Top Admin only
            'bulk_operations' => true,
            'export_data' => true,
        ];
    }

    /**
     * Get admin permissions.
     */
    public function getAdminPermissions(): array
    {
        if ($this->is_top_admin) {
            // Top Admin has all permissions
            return [
                'user_management' => true,
                'currency_management' => true,
                'language_management' => true,
                'financial_overview' => true,
                'approval_management' => true,
                'stage_pricing' => true,
                'admin_approval' => true,
                'system_settings' => true,
                'bulk_operations' => true,
                'export_data' => true,
            ];
        }

        return $this->admin_permissions ?? self::getDefaultAdminPermissions();
    }

    /**
     * Check if user has specific admin permission.
     */
    public function hasAdminPermission(string $permission): bool
    {
        if (!$this->is_admin) {
            return false;
        }

        if ($this->is_top_admin) {
            return true; // Top Admin has all permissions
        }

        $permissions = $this->getAdminPermissions();
        return $permissions[$permission] ?? false;
    }

    /**
     * Set admin permissions.
     */
    public function setAdminPermissions(array $permissions): void
    {
        $this->admin_permissions = $permissions;
        $this->save();
    }
}
