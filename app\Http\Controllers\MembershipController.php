<?php

namespace App\Http\Controllers;

use App\Models\MembershipStage;
use App\Services\ReferralService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MembershipController extends Controller
{
    protected $referralService;

    public function __construct(ReferralService $referralService)
    {
        $this->referralService = $referralService;
    }

    /**
     * Show membership stages and allow activation.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get all membership stages
        $membershipStages = MembershipStage::active()->ordered()->get();
        
        // Get referral statistics
        $stats = $this->referralService->getReferralStats($user);

        return view('activations.index', compact(
            'user',
            'membershipStages',
            'stats'
        ));
    }

    /**
     * Activate user to a specific membership stage.
     */
    public function activate(Request $request)
    {
        $request->validate([
            'stage' => 'required|string|exists:membership_stages,slug',
        ]);

        $user = Auth::user();
        $stageSlug = $request->stage;
        
        // Check if user can activate this stage
        if (!$user->canActivateStage($stageSlug)) {
            return redirect()->back()
                ->with('error', 'This stage is already activated or invalid.');
        }

        // Get the stage details
        $stage = MembershipStage::where('slug', $stageSlug)->first();

        // Activate the stage
        if ($user->activateStage($stageSlug)) {
            if ($user->isEarthFriendlyMember()) {
                $message = "Welcome to Light Membership! You've successfully activated the {$stage->name} stage.";
            } else {
                $message = "Successfully activated the {$stage->name} stage!";
            }

            // Add bonus message if applicable
            if ($stage->activation_bonus > 0) {
                $message .= " You've received a $" . number_format($stage->activation_bonus, 2) . " activation bonus.";
            }

            return redirect()->back()->with('success', $message);
        }

        return redirect()->back()
            ->with('error', 'Failed to activate membership stage. Please try again.');
    }

    /**
     * Deactivate a specific membership stage.
     */
    public function deactivate(Request $request)
    {
        $request->validate([
            'stage' => 'required|string|exists:membership_stages,slug',
        ]);

        $user = Auth::user();
        $stageSlug = $request->stage;

        // Get the stage details
        $stage = MembershipStage::where('slug', $stageSlug)->first();

        if ($user->deactivateStage($stageSlug)) {
            return redirect()->back()
                ->with('success', "Successfully deactivated the {$stage->name} stage.");
        }

        return redirect()->back()
            ->with('error', 'Failed to deactivate membership stage. Please try again.');
    }

    /**
     * Get stage details via AJAX.
     */
    public function getStageDetails(MembershipStage $stage)
    {
        return response()->json([
            'id' => $stage->id,
            'name' => $stage->name,
            'slug' => $stage->slug,
            'min_referrals' => $stage->min_referrals,
            'max_referrals' => $stage->max_referrals,
            'commission_rate' => $stage->commission_rate,
            'activation_bonus' => $stage->activation_bonus,
            'description' => $stage->description,
            'benefits' => $stage->benefits,
            'requirements_text' => $stage->requirements_text,
        ]);
    }

    /**
     * Show upgrade options for current user.
     */
    public function upgradeOptions()
    {
        $user = Auth::user();
        
        // Get all stages user can potentially activate (not yet activated)
        $availableStages = MembershipStage::active()
            ->ordered()
            ->get()
            ->filter(function ($stage) use ($user) {
                return !$user->hasActivatedStage($stage->slug);
            });
        
        $currentStage = null; // Multi-stage system - no single current stage
        $stats = $this->referralService->getReferralStats($user);

        return view('membership.upgrade', compact(
            'user',
            'availableStages',
            'currentStage',
            'stats'
        ));
    }
}
