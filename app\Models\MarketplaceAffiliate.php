<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class MarketplaceAffiliate extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'affiliate_code',
        'commission_rate',
        'total_earnings',
        'total_clicks',
        'total_sales',
        'is_active',
        'last_click_at',
    ];

    protected $casts = [
        'commission_rate' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'is_active' => 'boolean',
        'last_click_at' => 'datetime',
    ];

    /**
     * Get the user (affiliate).
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product.
     */
    public function product()
    {
        return $this->belongsTo(MarketplaceProduct::class, 'product_id');
    }

    /**
     * Get the affiliate clicks.
     */
    public function clicks()
    {
        return $this->hasMany(MarketplaceAffiliateClick::class, 'affiliate_id');
    }

    /**
     * Get the affiliate commissions.
     */
    public function commissions()
    {
        return $this->hasMany(MarketplaceAffiliateCommission::class, 'affiliate_id');
    }

    /**
     * Generate unique affiliate code.
     */
    public static function generateAffiliateCode($userId, $productId)
    {
        do {
            $code = 'AFF-' . strtoupper(Str::random(8));
        } while (self::where('affiliate_code', $code)->exists());

        return $code;
    }

    /**
     * Get affiliate link.
     */
    public function getAffiliateLinkAttribute()
    {
        return route('marketplace.show', [
            'product' => $this->product,
            'ref' => $this->affiliate_code
        ]);
    }

    /**
     * Calculate conversion rate.
     */
    public function getConversionRateAttribute()
    {
        if ($this->total_clicks == 0) {
            return 0;
        }

        return round(($this->total_sales / $this->total_clicks) * 100, 2);
    }

    /**
     * Scope for active affiliates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Boot method to generate affiliate code.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($affiliate) {
            if (empty($affiliate->affiliate_code)) {
                $affiliate->affiliate_code = self::generateAffiliateCode(
                    $affiliate->user_id,
                    $affiliate->product_id
                );
            }
        });
    }
}
