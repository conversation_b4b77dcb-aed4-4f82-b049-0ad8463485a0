<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StageReward extends Model
{
    use HasFactory;

    protected $fillable = [
        'membership_stage_id',
        'title',
        'description',
        'type',
        'requirement_type',
        'requirements',
        'reward_amount',
        'reward_points',
        'is_active',
        'is_repeatable',
        'max_completions',
        'sort_order',
    ];

    protected $casts = [
        'requirements' => 'array',
        'reward_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'is_repeatable' => 'boolean',
    ];

    /**
     * Get the membership stage.
     */
    public function membershipStage()
    {
        return $this->belongsTo(MembershipStage::class);
    }

    /**
     * Get the user progress records.
     */
    public function userProgress()
    {
        return $this->hasMany(UserStageRewardProgress::class);
    }

    /**
     * Get the redemptions.
     */
    public function redemptions()
    {
        return $this->hasMany(StageRewardRedemption::class);
    }

    /**
     * Scope for active rewards.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for instant payment rewards.
     */
    public function scopeInstantPayment($query)
    {
        return $query->where('type', 'instant_payment');
    }

    /**
     * Scope for bonus payment rewards.
     */
    public function scopeBonusPayment($query)
    {
        return $query->where('type', 'bonus_payment');
    }

    /**
     * Get progress for a specific user.
     */
    public function getProgressForUser($userId)
    {
        return $this->userProgress()->where('user_id', $userId)->first();
    }

    /**
     * Check if user can redeem this reward.
     */
    public function canUserRedeem($userId)
    {
        $progress = $this->getProgressForUser($userId);

        if (!$progress || !$progress->is_completed) {
            return false;
        }

        if (!$this->is_repeatable && $progress->completion_count > 0) {
            return false;
        }

        if ($this->max_completions && $progress->completion_count >= $this->max_completions) {
            return false;
        }

        return true;
    }

    /**
     * Get default rewards for a stage.
     */
    public static function getDefaultRewardsForStage($stageId)
    {
        return [
            // Instant Payment Rewards
            [
                'membership_stage_id' => $stageId,
                'title' => 'First Referral Bonus',
                'description' => 'Earn instant commission for your first successful referral',
                'type' => 'instant_payment',
                'requirement_type' => 'referrals',
                'requirements' => ['count' => 1],
                'reward_amount' => 25.00,
                'reward_points' => 500,
                'sort_order' => 1,
            ],
            [
                'membership_stage_id' => $stageId,
                'title' => 'Marketplace Purchase Bonus',
                'description' => 'Instant cashback on your first marketplace purchase',
                'type' => 'instant_payment',
                'requirement_type' => 'escrow_purchases',
                'requirements' => ['count' => 1, 'min_amount' => 50],
                'reward_amount' => 10.00,
                'reward_points' => 200,
                'sort_order' => 2,
            ],

            // Bonus Payment Rewards
            [
                'membership_stage_id' => $stageId,
                'title' => 'Project Completion Master',
                'description' => 'Complete 5 featured projects to unlock this bonus',
                'type' => 'bonus_payment',
                'requirement_type' => 'project_completion',
                'requirements' => ['count' => 5],
                'reward_amount' => 100.00,
                'reward_points' => 2000,
                'sort_order' => 3,
            ],
            [
                'membership_stage_id' => $stageId,
                'title' => 'Referral Champion',
                'description' => 'Refer 10 new members to earn this bonus',
                'type' => 'bonus_payment',
                'requirement_type' => 'referrals',
                'requirements' => ['count' => 10],
                'reward_amount' => 200.00,
                'reward_points' => 4000,
                'sort_order' => 4,
            ],
            [
                'membership_stage_id' => $stageId,
                'title' => 'Activity Enthusiast',
                'description' => 'Complete 20 stage activities to unlock this reward',
                'type' => 'bonus_payment',
                'requirement_type' => 'activities',
                'requirements' => ['count' => 20],
                'reward_amount' => 75.00,
                'reward_points' => 1500,
                'sort_order' => 5,
            ],
        ];
    }
}
