<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Spin settings table
        Schema::create('spin_settings', function (Blueprint $table) {
            $table->id();
            $table->integer('spins_required_to_win')->default(10);
            $table->integer('spin_cost_points')->default(100);
            $table->boolean('is_active')->default(true);
            $table->json('prize_delivery_methods')->nullable(); // ['events', 'cash', 'pickup_station', 'digital']
            $table->timestamps();
        });

        // Spin prizes table
        Schema::create('spin_prizes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('emoji')->nullable();
            $table->enum('delivery_method', ['events', 'cash', 'pickup_station', 'digital', 'instant']);
            $table->decimal('cash_value', 10, 2)->nullable();
            $table->integer('points_value')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('weight')->default(1); // For probability calculation
            $table->integer('max_per_day')->nullable(); // Max times this prize can be won per day
            $table->integer('max_per_user')->nullable(); // Max times a user can win this prize
            $table->timestamps();
        });

        // User spins table
        Schema::create('user_spins', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('spin_type', ['free', 'paid']);
            $table->integer('points_spent')->default(0);
            $table->boolean('is_winner')->default(false);
            $table->foreignId('prize_id')->nullable()->constrained('spin_prizes')->onDelete('set null');
            $table->string('prize_name')->nullable();
            $table->text('prize_description')->nullable();
            $table->enum('delivery_method', ['events', 'cash', 'pickup_station', 'digital', 'instant'])->nullable();
            $table->decimal('cash_value', 10, 2)->nullable();
            $table->integer('points_value')->nullable();
            $table->boolean('prize_claimed')->default(false);
            $table->timestamp('prize_claimed_at')->nullable();
            $table->text('admin_notes')->nullable();
            $table->timestamps();
        });

        // User spin progress table
        Schema::create('user_spin_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('total_spins')->default(0);
            $table->integer('current_spin_count')->default(0); // Spins since last win
            $table->timestamp('last_free_spin_date')->nullable();
            $table->integer('total_wins')->default(0);
            $table->decimal('total_cash_won', 10, 2)->default(0);
            $table->integer('total_points_won')->default(0);
            $table->timestamps();
            
            $table->unique('user_id');
        });

        // Spin statistics table for admin
        Schema::create('spin_statistics', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->integer('total_spins')->default(0);
            $table->integer('free_spins')->default(0);
            $table->integer('paid_spins')->default(0);
            $table->integer('total_winners')->default(0);
            $table->integer('points_spent')->default(0);
            $table->decimal('cash_prizes_given', 10, 2)->default(0);
            $table->integer('points_prizes_given')->default(0);
            $table->timestamps();
            
            $table->unique('date');
        });

        // Insert default settings
        DB::table('spin_settings')->insert([
            'spins_required_to_win' => 10,
            'spin_cost_points' => 100,
            'is_active' => true,
            'prize_delivery_methods' => json_encode(['events', 'cash', 'pickup_station', 'digital']),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Insert default prizes
        $defaultPrizes = [
            [
                'name' => 'Gift Card',
                'description' => '$10 Gift Card for local stores',
                'emoji' => '🎁',
                'delivery_method' => 'pickup_station',
                'cash_value' => 10.00,
                'weight' => 5,
                'max_per_day' => 5,
                'max_per_user' => 2,
            ],
            [
                'name' => 'Cash Prize',
                'description' => '$25 Cash Prize',
                'emoji' => '💰',
                'delivery_method' => 'cash',
                'cash_value' => 25.00,
                'weight' => 2,
                'max_per_day' => 3,
                'max_per_user' => 1,
            ],
            [
                'name' => 'Event Ticket',
                'description' => 'Free ticket to community events',
                'emoji' => '🎫',
                'delivery_method' => 'events',
                'cash_value' => 15.00,
                'weight' => 8,
                'max_per_day' => 10,
                'max_per_user' => 3,
            ],
            [
                'name' => 'Bonus Points',
                'description' => '500 Bonus Points',
                'emoji' => '💎',
                'delivery_method' => 'instant',
                'points_value' => 500,
                'weight' => 15,
                'max_per_day' => 20,
                'max_per_user' => 5,
            ],
            [
                'name' => 'Food Voucher',
                'description' => '$15 Food Voucher',
                'emoji' => '🍕',
                'delivery_method' => 'pickup_station',
                'cash_value' => 15.00,
                'weight' => 10,
                'max_per_day' => 8,
                'max_per_user' => 3,
            ],
            [
                'name' => 'Shopping Credit',
                'description' => '$20 Shopping Credit',
                'emoji' => '🛍️',
                'delivery_method' => 'digital',
                'cash_value' => 20.00,
                'weight' => 3,
                'max_per_day' => 4,
                'max_per_user' => 2,
            ],
        ];

        foreach ($defaultPrizes as $prize) {
            DB::table('spin_prizes')->insert(array_merge($prize, [
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spin_statistics');
        Schema::dropIfExists('user_spin_progress');
        Schema::dropIfExists('user_spins');
        Schema::dropIfExists('spin_prizes');
        Schema::dropIfExists('spin_settings');
    }
};
