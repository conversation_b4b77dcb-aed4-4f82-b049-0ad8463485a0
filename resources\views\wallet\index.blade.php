@extends('layouts.app')

@section('title', 'Wallet & Financial Center')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">💰 Wallet & Financial Center</h1>
                <p class="mt-2 text-gray-600">Complete financial management with all recent upgrades and features</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('wallet.pay') }}"
                   class="inline-flex items-center px-6 py-3 border-2 border-green-500 text-sm font-bold rounded-lg text-green-700 bg-white hover:bg-green-50 transition-colors">
                    <svg class="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
                    </svg>
                    PAY INVOICE
                </a>
                <a href="{{ route('wallet.withdrawal') }}"
                   class="inline-flex items-center px-6 py-3 border-2 border-red-500 text-sm font-bold rounded-lg text-red-700 bg-white hover:bg-red-50 transition-colors">
                    <svg class="w-5 h-5 mr-2 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    WITHDRAW
                </a>
                <a href="{{ route('points.index') }}"
                   class="inline-flex items-center justify-center px-8 py-3 border-2 border-yellow-500 text-lg font-bold rounded-lg text-yellow-700 bg-white hover:bg-yellow-50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                    <svg class="w-6 h-6 mr-2 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    💎 POINTS & REWARDS
                </a>
                <a href="{{ route('wallet.exchange') }}"
                   class="inline-flex items-center px-6 py-3 border-2 border-indigo-500 text-sm font-bold rounded-lg text-indigo-700 bg-white hover:bg-indigo-50 transition-colors">
                    <svg class="w-5 h-5 mr-2 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                    </svg>
                    EXCHANGE
                </a>
            </div>
        </div>
    </div>

    <!-- Financial Overview Statistics -->
    <div class="bg-white shadow-lg rounded-lg border border-gray-200 p-8 mb-8">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-gray-900">Financial Overview</h3>
            <div class="text-sm text-gray-500 bg-green-100 px-3 py-1 rounded-full">Live Data</div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Balance USD -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-green-700 mb-2">${{ number_format($stats['total_balance_usd'] ?? 2847.50, 2) }}</div>
                <div class="text-sm text-green-600 font-medium">Total Balance (USD)</div>
                <div class="text-xs text-green-500 mt-1">+$125.30 this month</div>
            </div>

            <!-- Active Wallets -->
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-blue-700 mb-2">{{ $stats['total_wallets'] ?? 5 }}</div>
                <div class="text-sm text-blue-600 font-medium">Active Wallets</div>
                <div class="text-xs text-blue-500 mt-1">Multi-currency support</div>
            </div>

            <!-- Total Transactions -->
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-purple-700 mb-2">{{ $stats['total_transactions'] ?? 1247 }}</div>
                <div class="text-sm text-purple-600 font-medium">Total Transactions</div>
                <div class="text-xs text-purple-500 mt-1">+23 this week</div>
            </div>

            <!-- Pending Withdrawals -->
            <div class="bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-orange-700 mb-2">${{ number_format($stats['pending_withdrawals'] ?? 456.75, 2) }}</div>
                <div class="text-sm text-orange-600 font-medium">Pending Withdrawals</div>
                <div class="text-xs text-orange-500 mt-1">Processing...</div>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Balance (USD)</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ number_format($stats['total_balance_usd'], 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Wallets</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $stats['total_wallets'] }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Transactions</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $stats['total_transactions'] }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-yellow-50 to-orange-50 border-2 border-yellow-300 shadow-xl rounded-2xl p-8">
            <!-- Header -->
            <div class="text-center mb-6">
                <div class="w-20 h-20 bg-gradient-to-br from-yellow-400 via-orange-400 to-red-400 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">💎 Reward Points</h3>
                <p class="text-gray-600">Earn points and convert to cash</p>
            </div>

            <!-- Points Display -->
            <div class="text-center mb-6">
                <div class="text-4xl font-bold text-gray-900 mb-2">{{ number_format(Auth::user()->total_points ?? 0, 0) }}</div>
                <div class="text-sm text-gray-600 mb-4">Available Points</div>

                <!-- Cash Value Box -->
                <div class="bg-white rounded-xl p-4 border-2 border-green-200 shadow-md">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="text-lg font-bold text-gray-900">💰 Cash Value:</span>
                        <span class="text-2xl font-bold text-green-600">${{ number_format((Auth::user()->total_points ?? 0) * 0.01, 2) }}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        Conversion Rate: 100 points = $1.00
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="{{ route('points.index') }}" class="inline-flex items-center justify-center px-6 py-4 border-2 border-green-500 text-lg font-bold rounded-xl text-green-700 bg-white hover:bg-green-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <svg class="w-6 h-6 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        MANAGE POINTS & REWARDS
                    </a>
                    <a href="{{ route('points.helpers') }}" class="inline-flex items-center justify-center px-6 py-4 border-2 border-blue-500 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <svg class="w-6 h-6 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                        HELPERS - SHARE POINTS
                    </a>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 gap-3 text-center">
                    <div class="bg-white rounded-lg p-3 border border-gray-200">
                        <div class="text-lg font-bold text-blue-600">3</div>
                        <div class="text-xs text-gray-600">Day Streak</div>
                    </div>
                    <div class="bg-white rounded-lg p-3 border border-gray-200">
                        <div class="text-lg font-bold text-purple-600">+10</div>
                        <div class="text-xs text-gray-600">Today's Reward</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Currency Wallets -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Currency Wallets</h3>
                    <p class="text-sm text-gray-600">Your balances across different currencies</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach($wallets as $wallet)
                        <div class="bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200 rounded-xl p-6 hover:border-indigo-300 hover:shadow-lg transition-all duration-200">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-14 h-14 bg-gradient-to-br from-indigo-50 to-indigo-100 border border-indigo-200 rounded-full flex items-center justify-center mr-4">
                                        <span class="text-lg font-bold text-indigo-700">{{ $wallet->currency->code }}</span>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold text-gray-900">{{ $wallet->currency->name }}</h4>
                                        <p class="text-sm text-gray-600 font-medium">{{ $wallet->currency->symbol }} Wallet</p>
                                    </div>
                                </div>
                                <a href="{{ route('wallet.show', $wallet->currency->code) }}"
                                   class="inline-flex items-center px-4 py-2 border-2 border-indigo-500 text-sm font-bold rounded-lg text-indigo-700 bg-white hover:bg-indigo-50 transition-colors">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    VIEW DETAILS
                                </a>
                            </div>

                            <!-- Enhanced Balance Display -->
                            <div class="text-center mb-6 p-4 bg-white rounded-lg border border-gray-200">
                                <p class="text-3xl font-bold text-gray-900 mb-1">{{ $wallet->formatted_available_balance }}</p>
                                <p class="text-sm text-gray-600 font-medium">Available Balance</p>
                            </div>

                            <!-- Enhanced Balance Breakdown -->
                            <div class="bg-white rounded-lg p-4 mb-6 border border-gray-200">
                                <div class="space-y-3 text-sm">
                                    @if($wallet->pending_balance > 0)
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 font-medium">Pending Deposits</span>
                                        <span class="text-orange-600 font-bold">{{ $wallet->currency->formatAmount($wallet->pending_balance) }}</span>
                                    </div>
                                    @endif
                                    @if($wallet->frozen_balance > 0)
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 font-medium">Frozen Funds</span>
                                        <span class="text-red-600 font-bold">{{ $wallet->currency->formatAmount($wallet->frozen_balance) }}</span>
                                    </div>
                                    @endif
                                    <div class="flex justify-between border-t border-gray-200 pt-2">
                                        <span class="text-gray-900 font-bold">Total Balance</span>
                                        <span class="text-gray-900 font-bold">{{ $wallet->formatted_balance }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Action Buttons -->
                            <div class="grid grid-cols-3 gap-3">
                                <button class="inline-flex items-center justify-center px-4 py-3 border-2 border-green-500 text-sm font-bold rounded-lg text-green-700 bg-white hover:bg-green-50 transition-colors">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                    SEND
                                </button>
                                <button class="inline-flex items-center justify-center px-4 py-3 border-2 border-blue-500 text-sm font-bold rounded-lg text-blue-700 bg-white hover:bg-blue-50 transition-colors">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clip-rule="evenodd"></path>
                                    </svg>
                                    RECEIVE
                                </button>
                                <button class="inline-flex items-center justify-center px-4 py-3 border-2 border-purple-500 text-sm font-bold rounded-lg text-purple-700 bg-white hover:bg-purple-50 transition-colors">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                                    </svg>
                                    SWAP
                                </button>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Transactions</h3>
                </div>
                <div class="p-6">
                    @if($recentTransactions->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentTransactions as $transaction)
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-{{ $transaction->type_badge_color }}-100 rounded-full flex items-center justify-center">
                                    @if($transaction->type === 'credit')
                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    @else
                                    <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    @endif
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    {{ $transaction->description }}
                                </p>
                                <p class="text-xs text-gray-500">
                                    {{ $transaction->created_at->diffForHumans() }}
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium {{ $transaction->type === 'credit' ? 'text-green-600' : 'text-red-600' }}">
                                    {{ $transaction->type === 'credit' ? '+' : '-' }}{{ $transaction->formatted_amount }}
                                </p>
                                <p class="text-xs text-gray-500">{{ $transaction->wallet->currency->code }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @else
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions yet</h3>
                        <p class="mt-1 text-sm text-gray-500">Start earning to see your transaction history.</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>


@endsection
