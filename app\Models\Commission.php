<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Commission extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'referral_id',
        'stage',
        'amount',
        'status',
        'description',
        'paid_at',
        'approval_status',
        'approved_at',
        'approved_by',
        'approval_notes',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'paid_at' => 'datetime',
            'approved_at' => 'datetime',
        ];
    }

    /**
     * Get the user who earned this commission.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the referral that generated this commission.
     */
    public function referral()
    {
        return $this->belongsTo(Referral::class);
    }

    /**
     * Scope to get paid commissions.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope to get pending commissions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Mark commission as paid.
     */
    public function markAsPaid(): bool
    {
        if ($this->status === 'paid') {
            return true;
        }

        $this->status = 'paid';
        $this->paid_at = now();
        $this->save();

        // Update user's earnings
        $user = $this->user;
        $user->total_earnings += $this->amount;
        $user->available_balance += $this->amount;
        $user->save();

        return true;
    }

    /**
     * User who approved this commission.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Check if commission is approved.
     */
    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    /**
     * Check if commission is pending approval.
     */
    public function isPendingApproval(): bool
    {
        return $this->approval_status === 'pending';
    }

    /**
     * Scope for approved commissions.
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope for pending approval commissions.
     */
    public function scopePendingApproval($query)
    {
        return $query->where('approval_status', 'pending');
    }
}
