<?php

use App\Http\Controllers\SecurityController;
use Illuminate\Support\Facades\Route;

// Security Management System API
Route::get('/security/settings', [SecurityController::class, 'getSettings']);
Route::post('/security/settings', [SecurityController::class, 'saveSettings']);
Route::get('/security/logs', [SecurityController::class, 'getLogs']);
Route::post('/system/clear-cache', [SecurityController::class, 'clearCache']);
Route::post('/system/optimize', [SecurityController::class, 'optimizeSystem']);
Route::get('/system/info', [SecurityController::class, 'getSystemInfo']);
Route::post('/system/restart-queue', [SecurityController::class, 'restartQueue']);
Route::get('/security/logs/export', [SecurityController::class, 'exportLogs']);
Route::get('/security/logs/analytics', [SecurityController::class, 'analytics']);