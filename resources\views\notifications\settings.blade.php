@extends('layouts.app')

@section('title', 'Notification Settings')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Notification Settings</h1>
                <p class="text-gray-600 mt-1">Manage how and when you receive notifications</p>
            </div>
            <a href="{{ route('notifications.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L4.414 9H17a1 1 0 110 2H4.414l5.293 5.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Notifications
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('notifications.settings.update') }}">
        @csrf
        
        <!-- Email Notifications -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Email Notifications</h3>
                <p class="text-sm text-gray-500 mt-1">Choose which notifications you'd like to receive via email</p>
            </div>
            <div class="px-6 py-4 space-y-6">
                <!-- Financial Notifications -->
                <div>
                    <h4 class="text-base font-medium text-gray-900 mb-4">Financial Activities</h4>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Payment Received</label>
                                <p class="text-sm text-gray-500">When you receive payments, bonuses, or referral earnings</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Withdrawal Updates</label>
                                <p class="text-sm text-gray-500">Status updates on your withdrawal requests</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Stage Activation</label>
                                <p class="text-sm text-gray-500">When your stage activations are approved or require action</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Community Notifications -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-base font-medium text-gray-900 mb-4">Community Activities</h4>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">New Messages</label>
                                <p class="text-sm text-gray-500">When you receive new private messages</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Project Updates</label>
                                <p class="text-sm text-gray-500">Updates on projects you're participating in or following</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">New Referrals</label>
                                <p class="text-sm text-gray-500">When someone joins using your referral link</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Notifications -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-base font-medium text-gray-900 mb-4">System & Security</h4>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Security Alerts</label>
                                <p class="text-sm text-gray-500">Login attempts, password changes, and security updates</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Platform Updates</label>
                                <p class="text-sm text-gray-500">New features, maintenance notices, and important announcements</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium text-gray-700">Marketing & Promotions</label>
                                <p class="text-sm text-gray-500">Special offers, events, and promotional content</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                                    <option value="immediate">Immediate</option>
                                    <option value="daily">Daily Summary</option>
                                    <option value="weekly">Weekly Summary</option>
                                    <option value="never">Never</option>
                                </select>
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Push Notifications -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Push Notifications</h3>
                <p class="text-sm text-gray-500 mt-1">Receive instant notifications in your browser</p>
            </div>
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-700">Enable Push Notifications</label>
                        <p class="text-sm text-gray-500">Get notified instantly for important updates</p>
                    </div>
                    <button type="button" id="enable-push" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">
                        Enable Notifications
                    </button>
                </div>
            </div>
        </div>

        <!-- Notification Preferences -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">General Preferences</h3>
                <p class="text-sm text-gray-500 mt-1">Customize your notification experience</p>
            </div>
            <div class="px-6 py-4 space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Quiet Hours</label>
                    <p class="text-sm text-gray-500 mb-4">Set hours when you don't want to receive notifications</p>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">From</label>
                            <input type="time" class="w-full border border-gray-300 rounded-md px-3 py-2" value="22:00">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">To</label>
                            <input type="time" class="w-full border border-gray-300 rounded-md px-3 py-2" value="08:00">
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                    <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="UTC">UTC (Coordinated Universal Time)</option>
                        <option value="EST">EST (Eastern Standard Time)</option>
                        <option value="PST">PST (Pacific Standard Time)</option>
                        <option value="GMT">GMT (Greenwich Mean Time)</option>
                        <option value="CET">CET (Central European Time)</option>
                    </select>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                    <label class="ml-2 block text-sm text-gray-700">
                        Group similar notifications together
                    </label>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label class="ml-2 block text-sm text-gray-700">
                        Show notification previews
                    </label>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
            <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                Save Preferences
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle push notification permission
    document.getElementById('enable-push').addEventListener('click', function() {
        if ('Notification' in window) {
            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    this.textContent = 'Notifications Enabled';
                    this.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
                    this.classList.add('bg-green-600', 'hover:bg-green-700');
                    
                    // Show test notification
                    new Notification('Notifications Enabled!', {
                        body: 'You will now receive push notifications for important updates.',
                        icon: '/favicon.ico'
                    });
                } else {
                    alert('Please enable notifications in your browser settings to receive push notifications.');
                }
            }.bind(this));
        } else {
            alert('Your browser does not support push notifications.');
        }
    });
});
</script>
@endsection
