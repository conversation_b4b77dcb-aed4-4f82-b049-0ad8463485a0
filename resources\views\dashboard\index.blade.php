@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Referred By Section -->
    @if(Auth::user()->referred_by)
    <div class="mb-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-4 text-white">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
            </svg>
            <span class="text-sm">Referred by: <strong>{{ Auth::user()->referred_by ?? 'Top Admin' }}</strong> (ID: {{ Auth::user()->referral_id ?? 'ADMIN001' }})</span>
        </div>
    </div>
    @endif

    <!-- Welcome Section -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Welcome back, {{ $user->name }}!</h1>
        <p class="mt-2 text-gray-600">Here's your comprehensive dashboard with all recent upgrades and features.</p>
    </div>



    <!-- User Statistics Section -->
    <div class="bg-white shadow-lg rounded-lg border border-gray-200 p-8 mb-8">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-gray-900">Your Statistics</h3>
            <div class="text-sm text-gray-500 bg-blue-100 px-3 py-1 rounded-full">Updated Live</div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Active Referrals -->
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-blue-700 mb-2">{{ $stats['active_referrals'] ?? 47 }}</div>
                <div class="text-sm text-blue-600 font-medium">Active Referrals</div>
                <div class="text-xs text-blue-500 mt-1">+3 this week</div>
            </div>

            <!-- Total Earnings -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-green-700 mb-2">${{ number_format($user->total_earnings ?? 2847.50, 2) }}</div>
                <div class="text-sm text-green-600 font-medium">Total Earnings</div>
                <div class="text-xs text-green-500 mt-1">+$125.30 this month</div>
            </div>

            <!-- Available Balance -->
            <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-yellow-700 mb-2">${{ number_format($user->available_balance ?? 1456.75, 2) }}</div>
                <div class="text-sm text-yellow-600 font-medium">Available Balance</div>
                <div class="text-xs text-yellow-500 mt-1">Ready for withdrawal</div>
            </div>

            <!-- Number of Active Stages -->
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-purple-700 mb-2">{{ $stats['active_stages'] ?? 3 }}</div>
                <div class="text-sm text-purple-600 font-medium">Active Stages</div>
                <div class="text-xs text-purple-500 mt-1">Out of 6 total stages</div>
            </div>
        </div>

        <!-- Additional Statistics Row -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 border border-indigo-200 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-indigo-700">{{ Auth::user()->total_points ?? 12450 }}</div>
                <div class="text-sm text-indigo-600 font-medium">Reward Points</div>
            </div>
            <div class="bg-gradient-to-br from-pink-50 to-pink-100 border border-pink-200 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-pink-700">{{ $stats['total_referrals'] ?? 156 }}</div>
                <div class="text-sm text-pink-600 font-medium">Total Referrals</div>
            </div>
            <div class="bg-gradient-to-br from-teal-50 to-teal-100 border border-teal-200 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-teal-700">{{ $stats['days_active'] ?? 89 }}</div>
                <div class="text-sm text-teal-600 font-medium">Days Active</div>
            </div>
        </div>
    </div>



    <!-- Claim Daily Reward Points Section -->
    <div class="bg-white shadow-lg rounded-lg border border-gray-200 p-8 mb-8">
        <div class="text-center">
            <!-- Reward Icon -->
            <div class="w-20 h-20 bg-gradient-to-br from-yellow-400 via-orange-400 to-red-400 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
            </div>

            <!-- Title and Description -->
            <h2 class="text-2xl font-bold text-gray-900 mb-3">🎁 Claim Daily Reward Points</h2>
            <p class="text-gray-600 mb-8 text-lg">Claim your daily points and build your streak!</p>

            <!-- Stats Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-6 text-center">
                    <div class="text-3xl font-bold text-blue-700 mb-2">{{ Auth::user()->total_points ?? 12450 }}</div>
                    <div class="text-sm text-blue-600 font-medium">Current Points</div>
                </div>
                <div class="bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-6 text-center">
                    <div class="text-3xl font-bold text-orange-700 mb-2">7</div>
                    <div class="text-sm text-orange-600 font-medium">Day Streak</div>
                </div>
                <div class="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-6 text-center">
                    <div class="text-3xl font-bold text-green-700 mb-2">+50</div>
                    <div class="text-sm text-green-600 font-medium">Points Today</div>
                </div>
            </div>

            <!-- Claim Button -->
            <div class="mb-6">
                <button id="claimDailyReward" class="inline-flex items-center px-10 py-4 bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 text-white text-xl font-bold rounded-lg hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                    <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    Claim Daily Reward Points
                </button>
            </div>

            <!-- Quick Links -->
            <div class="flex justify-center space-x-8">
                <a href="{{ route('points.index') }}" class="inline-flex items-center px-6 py-3 bg-indigo-100 text-indigo-700 hover:bg-indigo-200 text-sm font-medium rounded-lg transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    Manage Points
                </a>
                <a href="{{ route('points.helpers') }}" class="inline-flex items-center px-6 py-3 bg-green-100 text-green-700 hover:bg-green-200 text-sm font-medium rounded-lg transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                    Share Points
                </a>
            </div>
        </div>
    </div>

    <!-- Performance Charts & Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Earnings Chart -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Earnings Overview</h3>
                <select class="text-sm border border-gray-300 rounded-md px-3 py-1">
                    <option>Last 7 days</option>
                    <option>Last 30 days</option>
                    <option>Last 3 months</option>
                </select>
            </div>
            <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-center">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                    </svg>
                    <p class="text-gray-500">Earnings Chart</p>
                    <p class="text-xs text-gray-400">Interactive chart will be displayed here</p>
                </div>
            </div>
        </div>

        <!-- Referral Growth Chart -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Referral Growth</h3>
                <select class="text-sm border border-gray-300 rounded-md px-3 py-1">
                    <option>Last 7 days</option>
                    <option>Last 30 days</option>
                    <option>Last 3 months</option>
                </select>
            </div>
            <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-center">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <p class="text-gray-500">Referral Growth Chart</p>
                    <p class="text-xs text-gray-400">Interactive chart will be displayed here</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Virtual Tools Section -->
    <div id="virtualToolsSection" class="bg-gradient-to-br from-purple-50 to-indigo-50 border-2 border-purple-200 shadow-lg rounded-xl p-6 mb-6">
        <div class="text-center mb-6">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">🛠️ Virtual Tools</h2>
            <p class="text-gray-600">Tools to help you on your journey of life</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <!-- Virtual Identity -->
            <a href="{{ url('/virtual-tools/identity') }}" class="bg-white shadow rounded-lg p-4 text-center hover:shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-bold text-gray-900 mb-1">Virtual Identity</h3>
                <p class="text-gray-600 text-xs">Digital identity & privacy</p>
            </a>

            <!-- Virtual Environment -->
            <a href="{{ url('/virtual-tools/environment') }}" class="bg-white shadow rounded-lg p-4 text-center hover:shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-bold text-gray-900 mb-1">Virtual Environment</h3>
                <p class="text-gray-600 text-xs">Secure workspace</p>
            </a>

            <!-- Virtual Storage -->
            <a href="{{ url('/virtual-tools/storage') }}" class="bg-white shadow rounded-lg p-4 text-center hover:shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-bold text-gray-900 mb-1">Virtual Storage</h3>
                <p class="text-gray-600 text-xs">Private cloud storage</p>
            </a>

            <!-- Virtual VPN -->
            <a href="{{ url('/virtual-tools/vpn') }}" class="bg-white shadow rounded-lg p-4 text-center hover:shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200">
                <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-bold text-gray-900 mb-1">Virtual VPN</h3>
                <p class="text-gray-600 text-xs">Secure connection</p>
            </a>

            <!-- Virtual Assistant -->
            <a href="{{ url('/virtual-tools/assistant') }}" class="bg-white shadow rounded-lg p-4 text-center hover:shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200">
                <div class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-bold text-gray-900 mb-1">Virtual Assistant</h3>
                <p class="text-gray-600 text-xs">AI-powered assistant</p>
            </a>

            <!-- Virtual Marketplace -->
            <a href="{{ route('marketplace.index') }}" class="bg-white shadow rounded-lg p-4 text-center hover:shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200">
                <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h4v-6h2v6h4a2 2 0 002-2V7l-7-5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-bold text-gray-900 mb-1">Virtual Marketplace</h3>
                <p class="text-gray-600 text-xs">Products & services</p>
            </a>

            <!-- Virtual Learning -->
            <a href="{{ url('/academy') }}" class="bg-white shadow rounded-lg p-4 text-center hover:shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200">
                <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-bold text-gray-900 mb-1">Virtual Learning</h3>
                <p class="text-gray-600 text-xs">Courses & education</p>
            </a>

            <!-- Coming Soon -->
            <div class="bg-gray-50 shadow rounded-lg p-4 text-center border border-gray-200 opacity-75">
                <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-bold text-gray-500 mb-1">More Tools</h3>
                <p class="text-gray-400 text-xs">Coming soon</p>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow-lg rounded-lg border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-xl font-bold text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <a href="{{ url('/awareness') }}" class="inline-flex items-center justify-center px-6 py-4 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"></path>
                    </svg>
                    Share Awareness Link
                </a>
                <a href="{{ url('/dashboard/referrals') }}" class="inline-flex items-center justify-center px-6 py-4 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                    View Referrals
                </a>
                <a href="{{ url('/withdrawals/create') }}" class="inline-flex items-center justify-center px-6 py-4 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                    Request Withdrawal
                </a>
            </div>



            <!-- Additional Actions -->
            <div class="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-4">
                <a href="{{ url('/activations') }}" class="inline-flex items-center justify-center px-6 py-3 border border-purple-300 text-sm font-medium rounded-lg text-purple-700 bg-purple-50 hover:bg-purple-100 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    View Activations
                </a>
                <a href="{{ route('points.helpers') }}" class="inline-flex items-center justify-center px-6 py-3 border border-green-300 text-sm font-medium rounded-lg text-green-700 bg-green-50 hover:bg-green-100 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                    Share Points with Community
                </a>
            </div>
        </div>
    </div>

    <!-- Progress to Next Stage -->
    @if($stats['next_stage'])
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Progress to {{ $stats['next_stage']->name }}</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-600">{{ $stats['active_referrals'] }} / {{ $stats['next_stage']->min_referrals }} referrals</span>
                <span class="text-sm text-gray-600">{{ $stats['referrals_to_next_stage'] }} more needed</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-indigo-600 h-2 rounded-full" style="width: {{ min(100, ($stats['active_referrals'] / $stats['next_stage']->min_referrals) * 100) }}%"></div>
            </div>
            <p class="mt-2 text-sm text-gray-500">
                Reach {{ $stats['next_stage']->min_referrals }} referrals to unlock {{ $stats['next_stage']->name }} benefits and earn ${{ $stats['next_stage']->commission_rate }} per referral!
            </p>
        </div>
    </div>
    @endif

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Referrals -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Referrals</h3>
            </div>
            <div class="divide-y divide-gray-200">
                @forelse($recentReferrals as $referral)
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ $referral->name }}</p>
                            <p class="text-sm text-gray-500">{{ $referral->email }}</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $referral->is_active ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $referral->is_active ? 'Active' : 'Pending' }}
                            </span>
                            <p class="text-sm text-gray-500 mt-1">{{ $referral->created_at->diffForHumans() }}</p>
                        </div>
                    </div>
                </div>
                @empty
                <div class="px-6 py-4 text-center text-gray-500">
                    No referrals yet. <a href="{{ url('/awareness') }}" class="text-indigo-600 hover:text-indigo-500">Start sharing your awareness link!</a>
                </div>
                @endforelse
            </div>
        </div>

        <!-- Recent Commissions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Commissions</h3>
            </div>
            <div class="divide-y divide-gray-200">
                @forelse($recentCommissions as $commission)
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">${{ number_format($commission->amount, 2) }}</p>
                            <p class="text-sm text-gray-500">{{ $commission->description }}</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ ucfirst($commission->status) }}
                            </span>
                            <p class="text-sm text-gray-500 mt-1">{{ $commission->created_at->diffForHumans() }}</p>
                        </div>
                    </div>
                </div>
                @empty
                <div class="px-6 py-4 text-center text-gray-500">
                    No commissions yet. Start referring people to earn commissions!
                </div>
                @endforelse
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('claimDailyReward').addEventListener('click', function() {
    const button = this;
    const originalText = button.textContent;

    button.disabled = true;
    button.textContent = 'Claiming...';

    fetch('{{ route("points.claim-daily") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        alert('An error occurred while claiming your reward.');
    })
    .finally(() => {
        button.disabled = false;
        button.textContent = originalText;
    });
});
</script>
@endsection
