<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\Language;

class SetLanguage
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $language = $this->determineLanguage($request);
        
        // Set the application locale
        App::setLocale($language);
        
        // Store in session for consistency
        Session::put('language', $language);
        
        return $next($request);
    }

    /**
     * Determine the language to use.
     */
    private function determineLanguage(Request $request): string
    {
        // 1. Check URL parameter (for language switching)
        if ($request->has('lang')) {
            $requestedLang = $request->get('lang');
            if (Language::getByCode($requestedLang)) {
                // Update user preference if logged in
                if (Auth::check()) {
                    Auth::user()->update(['language' => $requestedLang]);
                }
                return $requestedLang;
            }
        }

        // 2. Check authenticated user's preference
        if (Auth::check() && Auth::user()->language) {
            $userLang = Auth::user()->language;
            if (Language::getByCode($userLang)) {
                return $userLang;
            }
        }

        // 3. Check session
        if (Session::has('language')) {
            $sessionLang = Session::get('language');
            if (Language::getByCode($sessionLang)) {
                return $sessionLang;
            }
        }

        // 4. Check browser language
        $browserLang = $this->getBrowserLanguage($request);
        if ($browserLang && Language::getByCode($browserLang)) {
            return $browserLang;
        }

        // 5. Fall back to default language
        $defaultLanguage = Language::getDefault();
        return $defaultLanguage ? $defaultLanguage->code : 'en';
    }

    /**
     * Get browser language preference.
     */
    private function getBrowserLanguage(Request $request): ?string
    {
        $acceptLanguage = $request->header('Accept-Language');
        
        if (!$acceptLanguage) {
            return null;
        }

        // Parse Accept-Language header
        $languages = [];
        foreach (explode(',', $acceptLanguage) as $lang) {
            $parts = explode(';', trim($lang));
            $code = trim($parts[0]);
            $quality = 1.0;
            
            if (isset($parts[1]) && strpos($parts[1], 'q=') === 0) {
                $quality = (float) substr($parts[1], 2);
            }
            
            // Extract primary language code (e.g., 'en' from 'en-US')
            $primaryCode = explode('-', $code)[0];
            $languages[$primaryCode] = $quality;
        }

        // Sort by quality
        arsort($languages);

        // Return the highest quality language that we support
        foreach (array_keys($languages) as $langCode) {
            if (Language::getByCode($langCode)) {
                return $langCode;
            }
        }

        return null;
    }
}
