<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\PointsService;
use App\Models\PointConfiguration;
use App\Models\User;

class PointsController extends Controller
{
    protected $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * Display the points page
     */
    public function index()
    {
        $user = Auth::user();
        $pointsSummary = $this->pointsService->getUserPointsSummary($user);
        $pointConfigurations = PointConfiguration::active()->get();

        return view('points.index', compact('pointsSummary', 'pointConfigurations'));
    }

    /**
     * Redeem points for cash
     */
    public function redeem(Request $request)
    {
        $request->validate([
            'points' => 'required|integer|min:1'
        ]);

        $user = Auth::user();

        try {
            $result = $this->pointsService->redeemPoints($user, $request->points);

            return back()->with('success',
                "Successfully redeemed {$result['points_redeemed']} points for $" .
                number_format($result['cash_value'], 2) .
                ". Your new wallet balance is $" .
                number_format($result['new_balance'], 2)
            );
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Claim daily visit reward
     */
    public function claimDailyReward()
    {
        $user = Auth::user();

        try {
            $visit = $this->pointsService->recordDailyVisit($user);

            if ($visit->points_awarded) {
                $config = PointConfiguration::where('source', 'daily_visit')->first();
                $points = $config ? $config->points_awarded : 10;

                $updatedUser = User::find($user->id);
                return response()->json([
                    'success' => true,
                    'message' => "You earned {$points} points for your daily visit!",
                    'points_earned' => $points,
                    'total_points' => $updatedUser->total_points
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already claimed your daily reward today.'
                ]);
            }
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while claiming your reward: ' . $exception->getMessage()
            ]);
        }
    }
}
