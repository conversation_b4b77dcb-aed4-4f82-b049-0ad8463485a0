@extends('layouts.app')

@section('title', 'Tools')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header with Navigation -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Awareness Center</h1>
                <p class="mt-2 text-gray-600">Comprehensive tools to share your sustainable living journey</p>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <a href="{{ route('awareness.index') }}"
                   class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm">
                    Dashboard
                </a>
                <a href="{{ route('awareness.referrals') }}"
                   class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm">
                    Referrals
                </a>
                <a href="{{ route('awareness.tools') }}"
                   class="py-2 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium text-sm">
                    Tools
                </a>
                <a href="{{ route('awareness.analytics') }}"
                   class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm">
                    Analytics
                </a>
            </nav>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Links & Tracking -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Links & ID Tracking</h3>
                <p class="text-sm text-gray-600">Various link formats and tracking options</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- Primary Link -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Primary Awareness Link</label>
                    <div class="flex">
                        <input type="text" 
                               value="{{ $tools['links']['primary'] }}" 
                               readonly 
                               class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border border-gray-300 focus:ring-green-500 focus:border-green-500 sm:text-sm"
                               id="primaryLink">
                        <button onclick="copyToClipboard('primaryLink')"
                                class="inline-flex items-center px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100">
                            Copy
                        </button>
                    </div>
                </div>

                <!-- Short Link -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Short Link</label>
                    <div class="flex">
                        <input type="text" 
                               value="{{ $tools['links']['short'] }}" 
                               readonly 
                               class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border border-gray-300 focus:ring-green-500 focus:border-green-500 sm:text-sm"
                               id="shortLink">
                        <button onclick="copyToClipboard('shortLink')"
                                class="inline-flex items-center px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100">
                            Copy
                        </button>
                    </div>
                </div>

                <!-- Tracked Links -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Source-Tracked Links</label>
                    <div class="space-y-3">
                        @foreach($tools['links']['tracked'] as $source => $url)
                        <div>
                            <label class="block text-xs font-medium text-gray-600 mb-1 capitalize">{{ $source }} Campaign</label>
                            <div class="flex">
                                <input type="text" 
                                       value="{{ $url }}" 
                                       readonly 
                                       class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border border-gray-300 text-xs"
                                       id="tracked{{ ucfirst($source) }}">
                                <button onclick="copyToClipboard('tracked{{ ucfirst($source) }}')"
                                        class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100 text-xs">
                                    Copy
                                </button>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- ID Tracking -->
                <div class="pt-4 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">ID Tracking Information</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">Referral Code:</span>
                            <span class="font-mono text-gray-900">{{ $tools['id_tracking']['referral_code'] }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">User ID:</span>
                            <span class="font-mono text-gray-900">{{ $tools['id_tracking']['user_id'] }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Banners -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Banner Creatives</h3>
                <p class="text-sm text-gray-600">Different banner sizes and styles</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    @foreach($banners as $type => $banner)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 capitalize mb-2">{{ $type }} Banner</h4>
                        <p class="text-xs text-gray-600 mb-3">Size: {{ $banner['size'] }}</p>
                        
                        <div class="grid grid-cols-3 gap-2 mb-3">
                            @foreach($banner['variants'] as $variant)
                            <div class="text-center">
                                <div class="w-full h-16 bg-{{ $variant }}-100 border border-{{ $variant }}-200 rounded flex items-center justify-center mb-1">
                                    <span class="text-xs text-{{ $variant }}-600 font-medium">{{ ucfirst($variant) }}</span>
                                </div>
                                <button class="text-xs text-blue-600 hover:text-blue-800">Download</button>
                            </div>
                            @endforeach
                        </div>
                        
                        <div class="text-xs text-gray-600">
                            <strong>Messages:</strong>
                            <ul class="list-disc list-inside mt-1">
                                @foreach($banner['messages'] as $message)
                                <li>{{ $message }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Creative Assets -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Creative Assets</h3>
                <p class="text-sm text-gray-600">Images, videos, and infographics</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <!-- Images -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Images</h4>
                        @foreach($creatives['images'] as $category => $images)
                        <div class="mb-4">
                            <h5 class="text-xs font-medium text-gray-700 capitalize mb-2">{{ $category }}</h5>
                            <div class="grid grid-cols-3 gap-2">
                                @foreach($images as $image)
                                <div class="text-center">
                                    <div class="w-full h-20 bg-gray-100 border border-gray-200 rounded flex items-center justify-center mb-1">
                                        <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <p class="text-xs text-gray-600 truncate">{{ $image }}</p>
                                    <button class="text-xs text-blue-600 hover:text-blue-800">Download</button>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Videos -->
                    <div class="pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Videos</h4>
                        @foreach($creatives['videos'] as $category => $videos)
                        <div class="mb-4">
                            <h5 class="text-xs font-medium text-gray-700 capitalize mb-2">{{ $category }}</h5>
                            <div class="space-y-2">
                                @foreach($videos as $video)
                                <div class="flex items-center justify-between p-2 border border-gray-200 rounded">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">{{ $video }}</span>
                                    </div>
                                    <button class="text-sm text-blue-600 hover:text-blue-800">Download</button>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Infographics -->
                    <div class="pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Infographics</h4>
                        <div class="grid grid-cols-2 gap-3">
                            @foreach($creatives['infographics'] as $infographic)
                            <div class="text-center">
                                <div class="w-full h-24 bg-gray-100 border border-gray-200 rounded flex items-center justify-center mb-2">
                                    <svg class="w-10 h-10 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                                    </svg>
                                </div>
                                <p class="text-xs text-gray-600 mb-1">{{ $infographic }}</p>
                                <button class="text-xs text-blue-600 hover:text-blue-800">Download</button>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Tools -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Advanced Tools</h3>
                <p class="text-sm text-gray-600">Professional marketing tools</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- QR Code Generator -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">QR Code Generator</h4>
                    <div class="flex items-center space-x-4">
                        <div class="w-24 h-24 bg-gray-100 border border-gray-200 rounded flex items-center justify-center">
                            <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1V4a1 1 0 00-1-1h-3zm1 2v1h1V5h-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-600 mb-2">Generate QR codes for your awareness links</p>
                            <button class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Generate QR Code
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Link Shortener -->
                <div class="pt-4 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Custom Link Shortener</h4>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">Custom Alias (optional)</label>
                            <input type="text" 
                                   placeholder="my-sustainable-journey" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-green-500 focus:border-green-500">
                        </div>
                        <button class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Create Short Link
                        </button>
                    </div>
                </div>

                <!-- Email Signature -->
                <div class="pt-4 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Email Signature</h4>
                    <div class="bg-gray-50 border border-gray-200 rounded p-3 text-sm">
                        <div class="font-medium text-gray-900">{{ $user->name }}</div>
                        <div class="text-gray-600">Sustainable Living Advocate</div>
                        <div class="mt-2">
                            <a href="{{ $referralUrl }}" class="text-green-600 hover:text-green-800">
                                Join my sustainable living journey 🌱
                            </a>
                        </div>
                    </div>
                    <button class="mt-2 text-sm text-blue-600 hover:text-blue-800">Copy HTML</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show feedback
    const button = element.nextElementSibling;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.classList.add('text-green-600');
    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('text-green-600');
    }, 2000);
}
</script>
@endsection
