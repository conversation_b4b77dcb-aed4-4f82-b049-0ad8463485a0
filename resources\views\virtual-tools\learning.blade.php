@extends('layouts.app')

@section('title', 'Virtual Learning')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-lg p-8 mb-8 text-white">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-4">📚 Virtual Learning</h1>
            <p class="text-xl text-yellow-100">Comprehensive learning platform for personal and professional growth</p>
        </div>
    </div>

    <!-- Learning Categories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <a href="{{ url('/academy') }}" class="bg-white shadow rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Academy</h3>
            <p class="text-gray-600 text-sm">Structured learning paths and programs</p>
        </a>

        <a href="{{ url('/courses') }}" class="bg-white shadow rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Courses</h3>
            <p class="text-gray-600 text-sm">Individual courses and modules</p>
        </a>

        <a href="{{ url('/resources') }}" class="bg-white shadow rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Resources</h3>
            <p class="text-gray-600 text-sm">Downloadable materials and guides</p>
        </a>

        <a href="{{ url('/certifications') }}" class="bg-white shadow rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Certifications</h3>
            <p class="text-gray-600 text-sm">Professional certifications and badges</p>
        </a>
    </div>

    <!-- Featured Courses -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Featured Courses</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-r from-blue-400 to-purple-500"></div>
                <div class="p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Environmental Leadership</h3>
                    <p class="text-gray-600 text-sm mb-4">Learn to lead environmental initiatives in your community</p>
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm text-gray-500">12 lessons</span>
                        <span class="text-sm text-green-600 font-medium">Free</span>
                    </div>
                    <button class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">Start Course</button>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-r from-green-400 to-teal-500"></div>
                <div class="p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Sustainable Living</h3>
                    <p class="text-gray-600 text-sm mb-4">Practical guide to sustainable lifestyle choices</p>
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm text-gray-500">8 lessons</span>
                        <span class="text-sm text-green-600 font-medium">Free</span>
                    </div>
                    <button class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Start Course</button>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-r from-purple-400 to-pink-500"></div>
                <div class="p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Climate Action</h3>
                    <p class="text-gray-600 text-sm mb-4">Understanding climate change and taking action</p>
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm text-gray-500">15 lessons</span>
                        <span class="text-sm text-blue-600 font-medium">Premium</span>
                    </div>
                    <button class="w-full bg-purple-600 text-white py-2 rounded hover:bg-purple-700">Start Course</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Learning Progress -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-6">
            <!-- My Learning -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">My Learning Progress</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Environmental Leadership</h3>
                                <p class="text-sm text-gray-600">Lesson 8 of 12 • 67% complete</p>
                            </div>
                        </div>
                        <div class="w-16 h-2 bg-gray-200 rounded-full">
                            <div class="w-2/3 h-2 bg-blue-600 rounded-full"></div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Sustainable Living</h3>
                                <p class="text-sm text-gray-600">Completed • Earned certificate</p>
                            </div>
                        </div>
                        <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded">Completed</span>
                    </div>
                </div>
            </div>

            <!-- Upcoming Webinars -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Upcoming Webinars</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                            <h3 class="font-medium text-gray-900">Climate Change Solutions</h3>
                            <p class="text-sm text-gray-600">Tomorrow at 2:00 PM</p>
                        </div>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Register</button>
                    </div>
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                            <h3 class="font-medium text-gray-900">Renewable Energy Basics</h3>
                            <p class="text-sm text-gray-600">Friday at 10:00 AM</p>
                        </div>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Register</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
            <!-- Learning Stats -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Learning Statistics</h3>
                <div class="space-y-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">3</div>
                        <div class="text-sm text-gray-600">Courses Completed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">24</div>
                        <div class="text-sm text-gray-600">Hours Learned</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">2</div>
                        <div class="text-sm text-gray-600">Certificates Earned</div>
                    </div>
                </div>
            </div>

            <!-- Achievements -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Recent Achievements</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Course Completed</p>
                            <p class="text-xs text-gray-500">Sustainable Living</p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Learning Streak</p>
                            <p class="text-xs text-gray-500">7 days in a row</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <button class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">Browse All Courses</button>
                    <button class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">View Certificates</button>
                    <button class="w-full bg-purple-600 text-white py-2 rounded hover:bg-purple-700">Join Study Group</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
