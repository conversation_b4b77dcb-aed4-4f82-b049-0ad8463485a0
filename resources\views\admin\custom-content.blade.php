@extends('layouts.admin')

@section('title', 'Custom Content Management')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ route('admin.dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"></path>
                    </svg>
                    Admin Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ route('admin.settings.index') }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2">Settings</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Custom Content</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Custom Content Management</h1>
                <p class="text-gray-600 mt-1">Manage page titles, descriptions, and custom content across the platform</p>
            </div>
            <div class="flex space-x-3">
                <button class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Export Settings
                </button>
                <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    Add New Content
                </button>
            </div>
        </div>
    </div>

    <!-- Content Categories -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <a href="#" class="border-indigo-500 text-indigo-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Page Titles & Meta
                </a>
                <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Welcome Messages
                </a>
                <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Email Templates
                </a>
                <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    System Messages
                </a>
            </nav>
        </div>
    </div>

    <!-- Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Page Titles & Meta -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Homepage</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                </span>
            </div>
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Page Title</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Environmental Community Platform" placeholder="Enter page title">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter meta description">Join our environmental community and make a positive impact on the planet through stage-based activations and collaborative projects.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Hero Title</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Make a Difference for Our Planet" placeholder="Enter hero title">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Hero Subtitle</label>
                    <textarea rows="2" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter hero subtitle">Join thousands of environmental advocates in creating positive change through our innovative stage-based membership platform.</textarea>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Save Changes</button>
                </div>
            </form>
        </div>

        <!-- Dashboard Content -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Dashboard</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                </span>
            </div>
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Welcome Message</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter welcome message">Welcome back! Ready to continue your environmental journey? Check out your latest activities and explore new opportunities to make an impact.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Quick Actions Title</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Quick Actions" placeholder="Enter quick actions title">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Statistics Section Title</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Your Impact Overview" placeholder="Enter statistics title">
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Save Changes</button>
                </div>
            </form>
        </div>

        <!-- Stage Activation Content -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Stage Activation</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                </span>
            </div>
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Page Title</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Activate Your Environmental Journey" placeholder="Enter page title">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Introduction Text</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter introduction text">Choose your path and unlock new opportunities to make a positive environmental impact. Each stage offers unique benefits and earning potential.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Call to Action Text</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Start Your Activation Today" placeholder="Enter CTA text">
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Save Changes</button>
                </div>
            </form>
        </div>

        <!-- Email Templates -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Welcome Email</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                </span>
            </div>
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email Subject</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Welcome to Our Environmental Community!" placeholder="Enter email subject">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email Content</label>
                    <textarea rows="6" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter email content">Dear {{name}},

Welcome to our environmental community! We're thrilled to have you join us in making a positive impact on our planet.

Your journey starts as an Earth-Friendly Member, and you can activate additional stages to unlock more opportunities and earning potential.

Get started by:
- Exploring your dashboard
- Checking out available projects
- Connecting with other members

Best regards,
The Environmental Community Team</textarea>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Save Changes</button>
                </div>
            </form>
        </div>

        <!-- Notification Templates -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Stage Activation Notification</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                </span>
            </div>
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Notification Title</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Stage Activation Approved!" placeholder="Enter notification title">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Notification Message</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter notification message">Congratulations! Your {{stage_name}} activation has been approved. You can now access your new stage area and start earning points for {{stage_name}} activities.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email Subject</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Your {{stage_name}} Activation is Complete!" placeholder="Enter email subject">
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Save Changes</button>
                </div>
            </form>
        </div>

        <!-- Footer Content -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Footer Content</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                </span>
            </div>
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Environmental Community Platform" placeholder="Enter company name">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Copyright Text</label>
                    <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="All rights reserved." placeholder="Enter copyright text">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                    <input type="email" class="w-full border border-gray-300 rounded-md px-3 py-2" value="<EMAIL>" placeholder="Enter contact email">
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Available Variables -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-medium text-blue-900 mb-4">Available Template Variables</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <h4 class="font-medium text-blue-800 mb-2">User Variables</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li><code>{{name}}</code> - User's full name</li>
                    <li><code>{{email}}</code> - User's email address</li>
                    <li><code>{{first_name}}</code> - User's first name</li>
                    <li><code>{{membership_type}}</code> - Current membership type</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-blue-800 mb-2">Stage Variables</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li><code>{{stage_name}}</code> - Stage name</li>
                    <li><code>{{stage_price}}</code> - Stage activation price</li>
                    <li><code>{{activation_date}}</code> - Activation date</li>
                    <li><code>{{stage_benefits}}</code> - Stage benefits list</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-blue-800 mb-2">System Variables</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li><code>{{site_name}}</code> - Website name</li>
                    <li><code>{{site_url}}</code> - Website URL</li>
                    <li><code>{{current_date}}</code> - Current date</li>
                    <li><code>{{support_email}}</code> - Support email</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
