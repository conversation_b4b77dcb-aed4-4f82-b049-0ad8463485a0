<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BonusPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'bonus_type',
        'bonus_name',
        'amount',
        'description',
        'criteria',
        'status',
        'earned_at',
        'paid_at',
    ];

    protected $casts = [
        'criteria' => 'array',
        'earned_at' => 'datetime',
        'paid_at' => 'datetime',
        'amount' => 'decimal:2',
    ];

    /**
     * Get the user that owns the bonus payment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for pending bonuses.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for paid bonuses.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for milestone bonuses.
     */
    public function scopeMilestone($query)
    {
        return $query->where('bonus_type', 'milestone');
    }

    /**
     * Scope for time-based bonuses.
     */
    public function scopeTimeBased($query)
    {
        return $query->where('bonus_type', 'time_based');
    }

    /**
     * Scope for leaderboard bonuses.
     */
    public function scopeLeaderboard($query)
    {
        return $query->where('bonus_type', 'leaderboard');
    }

    /**
     * Mark bonus as paid.
     */
    public function markAsPaid()
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);

        // Add to user's balance
        $this->user->increment('available_balance', $this->amount);
        $this->user->increment('total_earnings', $this->amount);
    }
}
