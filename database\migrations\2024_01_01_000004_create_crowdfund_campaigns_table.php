<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('crowdfund_campaigns', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->string('category');
            $table->decimal('target_amount', 15, 2);
            $table->decimal('raised_amount', 15, 2)->default(0);
            $table->enum('campaign_type', ['donation', 'investment', 'loan'])->default('donation');
            $table->decimal('profit_percentage', 5, 2)->nullable(); // For investment type
            $table->integer('duration_days');
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['pending', 'approved', 'active', 'completed', 'cancelled', 'denied'])->default('pending');
            $table->json('requirements')->nullable(); // Store eligibility requirements
            $table->json('images')->nullable();
            $table->json('documents')->nullable();
            $table->text('admin_notes')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('crowdfund_campaigns');
    }
};
