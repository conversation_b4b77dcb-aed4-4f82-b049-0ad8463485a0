<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProjectTeamCredit extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'member_name',
        'role',
        'contribution',
        'contact_info',
        'social_links',
        'credit_points'
    ];

    // Relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(CommunityProject::class, 'project_id');
    }

    // Accessors
    public function getRoleDisplayAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->role));
    }

    public function getSocialLinksArrayAttribute(): array
    {
        if (empty($this->social_links)) {
            return [];
        }
        
        return json_decode($this->social_links, true) ?: [];
    }

    // Scopes
    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    public function scopeHighestCredits($query)
    {
        return $query->orderBy('credit_points', 'desc');
    }
}
