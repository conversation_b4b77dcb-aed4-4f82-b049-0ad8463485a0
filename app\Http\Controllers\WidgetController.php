<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class WidgetController extends Controller
{
    public function communityProjects(Request $request)
    {
        // Get admin settings for widget configuration
        $projectType = $request->get('type', 'all'); // all, crowdfund, volunteer, petition, donate
        $displayType = $request->get('display', 'latest'); // latest, top_performing, most_anticipated
        $showLeaderboard = $request->get('leaderboard', false);
        $showStageActivation = $request->get('stage_activation', false);
        $limit = $request->get('limit', 6);

        // Sample projects data (would come from database)
        $projects = $this->getProjectsData($projectType, $displayType, $limit);
        $leaderboard = $this->getLeaderboardData();
        $stageActivationPlans = $this->getStageActivationPlans();

        return view('widgets.community-projects', compact(
            'projects',
            'projectType',
            'displayType',
            'showLeaderboard',
            'leaderboard',
            'showStageActivation',
            'stageActivationPlans'
        ));
    }

    private function getProjectsData($type, $display, $limit)
    {
        // Sample data - replace with actual database queries
        $allProjects = [
            [
                'id' => 1,
                'title' => 'Clean Ocean Initiative',
                'description' => 'Join our mission to remove plastic waste from oceans and protect marine life for future generations.',
                'type' => 'crowdfund',
                'type_label' => 'Crowdfunding',
                'badge_class' => 'bg-green-100 text-green-800',
                'gradient' => 'from-blue-400 to-teal-500',
                'participants' => 245,
                'goal_amount' => 50000,
                'progress' => 65,
                'votes' => 189,
                'featured' => true
            ],
            [
                'id' => 2,
                'title' => 'Community Garden Project',
                'description' => 'Help us create sustainable community gardens in urban areas to promote local food production.',
                'type' => 'volunteer',
                'type_label' => 'Volunteer',
                'badge_class' => 'bg-purple-100 text-purple-800',
                'gradient' => 'from-green-400 to-emerald-500',
                'participants' => 156,
                'votes' => 234,
                'featured' => false
            ],
            [
                'id' => 3,
                'title' => 'Renewable Energy Petition',
                'description' => 'Support our petition for increased renewable energy adoption in local communities.',
                'type' => 'petition',
                'type_label' => 'Petition',
                'badge_class' => 'bg-orange-100 text-orange-800',
                'gradient' => 'from-yellow-400 to-orange-500',
                'participants' => 1250,
                'votes' => 892,
                'featured' => true
            ],
            [
                'id' => 4,
                'title' => 'Wildlife Conservation Fund',
                'description' => 'Donate to protect endangered species and preserve natural habitats worldwide.',
                'type' => 'donate',
                'type_label' => 'Donation',
                'badge_class' => 'bg-blue-100 text-blue-800',
                'gradient' => 'from-purple-400 to-pink-500',
                'goal_amount' => 25000,
                'progress' => 42,
                'votes' => 156,
                'featured' => false
            ]
        ];

        // Filter by type if specified
        if ($type !== 'all') {
            $allProjects = array_filter($allProjects, function($project) use ($type) {
                return $project['type'] === $type;
            });
        }

        // Sort by display type
        switch ($display) {
            case 'top_performing':
                usort($allProjects, function($a, $b) {
                    return ($b['votes'] ?? 0) - ($a['votes'] ?? 0);
                });
                break;
            case 'most_anticipated':
                usort($allProjects, function($a, $b) {
                    return ($b['participants'] ?? 0) - ($a['participants'] ?? 0);
                });
                break;
            case 'featured':
                $allProjects = array_filter($allProjects, function($project) {
                    return $project['featured'] ?? false;
                });
                break;
            default: // latest
                // Already in latest order
                break;
        }

        return array_slice(array_values($allProjects), 0, $limit);
    }

    private function getLeaderboardData()
    {
        return [
            [
                'name' => 'Sarah Johnson',
                'contribution_type' => 'Project Funding',
                'amount' => 2500
            ],
            [
                'name' => 'Michael Chen',
                'contribution_type' => 'Volunteer Hours',
                'amount' => 1800
            ],
            [
                'name' => 'Emma Rodriguez',
                'contribution_type' => 'Community Impact',
                'amount' => 1200
            ],
            [
                'name' => 'David Kim',
                'contribution_type' => 'Environmental Action',
                'amount' => 950
            ],
            [
                'name' => 'Lisa Thompson',
                'contribution_type' => 'Awareness Campaign',
                'amount' => 750
            ]
        ];
    }

    private function getStageActivationPlans()
    {
        return [
            [
                'name' => 'Earth Friendly',
                'price' => '0',
                'description' => 'Start your environmental journey',
                'features' => [
                    'Basic community access',
                    'Environmental tips',
                    'Progress tracking'
                ]
            ],
            [
                'name' => 'Light Activist',
                'price' => '29',
                'description' => 'Take meaningful action',
                'features' => [
                    'Project participation',
                    'Referral rewards',
                    'Advanced tools'
                ]
            ],
            [
                'name' => 'Green Leader',
                'price' => '99',
                'description' => 'Lead environmental change',
                'features' => [
                    'Create projects',
                    'Leadership tools',
                    'Premium support'
                ]
            ],
            [
                'name' => 'Eco Champion',
                'price' => '199',
                'description' => 'Maximum impact potential',
                'features' => [
                    'All features',
                    'Mentorship program',
                    'Exclusive events'
                ]
            ],
            [
                'name' => 'Planet Guardian',
                'price' => '499',
                'description' => 'Ultimate environmental advocate',
                'features' => [
                    'Global network access',
                    'Policy influence',
                    'Custom initiatives'
                ]
            ],
            [
                'name' => 'Earth Protector',
                'price' => '999',
                'description' => 'Transform the world',
                'features' => [
                    'Worldwide impact',
                    'Strategic partnerships',
                    'Legacy building'
                ]
            ]
        ];
    }
}
