<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserStageRewardProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'stage_reward_id',
        'progress_data',
        'current_progress',
        'required_progress',
        'is_completed',
        'completed_at',
        'completion_count',
    ];

    protected $casts = [
        'progress_data' => 'array',
        'is_completed' => 'boolean',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the stage reward.
     */
    public function stageReward()
    {
        return $this->belongsTo(StageReward::class);
    }

    /**
     * Get the redemptions.
     */
    public function redemptions()
    {
        return $this->hasMany(StageRewardRedemption::class, 'progress_id');
    }

    /**
     * Get progress percentage.
     */
    public function getProgressPercentageAttribute()
    {
        if ($this->required_progress == 0) {
            return 0;
        }

        return min(100, round(($this->current_progress / $this->required_progress) * 100, 2));
    }

    /**
     * Check if progress is complete.
     */
    public function checkCompletion()
    {
        if ($this->current_progress >= $this->required_progress && !$this->is_completed) {
            $this->update([
                'is_completed' => true,
                'completed_at' => now(),
            ]);
        }
    }

    /**
     * Update progress.
     */
    public function updateProgress($amount = 1, $data = [])
    {
        $this->increment('current_progress', $amount);

        if (!empty($data)) {
            $currentData = $this->progress_data ?? [];
            $this->update([
                'progress_data' => array_merge($currentData, $data)
            ]);
        }

        $this->checkCompletion();
    }
}
