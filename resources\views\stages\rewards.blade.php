@extends('layouts.app')

@section('title', $stage->name . ' Rewards')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $stage->name }} Rewards</h1>
                <p class="mt-2 text-gray-600">Earn rewards through instant payments and bonus achievements</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ url('/stages/' . $stage->slug) }}"
                   class="inline-flex items-center px-4 py-2 border border-indigo-300 text-sm font-medium rounded-md text-indigo-700 bg-indigo-50 hover:bg-indigo-100">
                    Back to Area
                </a>
            </div>
        </div>
    </div>

    <!-- Earnings Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Earned</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ number_format($stageEarnings['total_earned'], 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 7H7v6h6V7z"></path>
                            <path fill-rule="evenodd" d="M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2H3V5a2 2 0 012-2h2V2zM3 9h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Instant Payments</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ number_format($stageEarnings['instant_payments'], 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Bonus Payments</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ number_format($stageEarnings['bonus_payments'], 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Instant Payment Rewards -->
    @if($instantRewards->count() > 0)
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 7H7v6h6V7z"></path>
                    <path fill-rule="evenodd" d="M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2H3V5a2 2 0 012-2h2V2zM3 9h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" clip-rule="evenodd"></path>
                </svg>
                Instant Payment Rewards
            </h3>
            <p class="text-sm text-gray-600">Earn immediate rewards for direct referrals and quick actions</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                @foreach($instantRewards as $reward)
                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">{{ $reward->title }}</h4>
                            <p class="text-sm text-gray-600 mt-1">{{ $reward->description }}</p>
                            <div class="mt-3 flex items-center space-x-4">
                                @if($reward->reward_amount)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    ${{ number_format($reward->reward_amount, 2) }}
                                </span>
                                @endif
                                @if($reward->reward_points)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ number_format($reward->reward_points) }} points
                                </span>
                                @endif
                            </div>
                        </div>
                        <div class="ml-4">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Instant
                            </span>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Bonus Payment Rewards -->
    @if($bonusRewards->count() > 0)
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
                Bonus Payment Rewards
            </h3>
            <p class="text-sm text-gray-600">Complete requirements to unlock bonus rewards with progress tracking</p>
        </div>
        <div class="p-6">
            <div class="space-y-6">
                @foreach($bonusRewards as $reward)
                @php
                    $progress = $userProgress[$reward->id] ?? null;
                    $progressPercentage = $progress ? $progress->progress_percentage : 0;
                    $isCompleted = $progress ? $progress->is_completed : false;
                @endphp
                <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h4 class="text-lg font-medium text-gray-900">{{ $reward->title }}</h4>
                            <p class="text-sm text-gray-600 mt-1">{{ $reward->description }}</p>
                        </div>
                        <div class="ml-4 text-right">
                            @if($isCompleted)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                ✓ Completed
                            </span>
                            @else
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                In Progress
                            </span>
                            @endif
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">Progress</span>
                            <span class="text-sm text-gray-500">
                                {{ $progress ? $progress->current_progress : 0 }} / {{ $progress ? $progress->required_progress : ($reward->requirements['count'] ?? 1) }}
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="{{ $isCompleted ? 'bg-green-600' : 'bg-yellow-600' }} h-2 rounded-full transition-all duration-300"
                                 style="width: {{ min(100, $progressPercentage) }}%"></div>
                        </div>
                        <div class="flex items-center justify-between mt-1">
                            <span class="text-xs text-gray-500">{{ number_format($progressPercentage, 1) }}% complete</span>
                            @if($isCompleted)
                            <span class="text-xs text-green-600 font-medium">Goal achieved!</span>
                            @endif
                        </div>
                    </div>

                    <!-- Reward Details -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            @if($reward->reward_amount)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                💰 ${{ number_format($reward->reward_amount, 2) }}
                            </span>
                            @endif
                            @if($reward->reward_points)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                ⭐ {{ number_format($reward->reward_points) }} points
                            </span>
                            @endif
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ ucfirst(str_replace('_', ' ', $reward->requirement_type)) }}
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- No Rewards Message -->
    @if($stageRewards->count() === 0)
    <div class="bg-white shadow rounded-lg">
        <div class="p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 0v2m0 0V6a2 2 0 012 0v2m0 0h2m-6 0h2m0 0h2m-6 0h2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No rewards available yet</h3>
            <p class="mt-1 text-sm text-gray-500">Rewards for this stage are being configured. Check back soon!</p>
        </div>
    </div>
    @endif
</div>
@endsection
