<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\PointsService;

class PointsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(PointsService::class, function ($app) {
            return new PointsService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
