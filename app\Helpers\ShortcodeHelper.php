<?php

namespace App\Helpers;

use App\Models\CommunityProject;
use App\Models\MembershipStage;

class ShortcodeHelper
{
    /**
     * Process shortcodes in content
     */
    public static function processShortcodes($content)
    {
        // Community Projects shortcode
        $content = preg_replace_callback(
            '/\[community_projects([^\]]*)\]/',
            [self::class, 'renderCommunityProjects'],
            $content
        );

        // Stage Pricing shortcode
        $content = preg_replace_callback(
            '/\[stage_pricing([^\]]*)\]/',
            [self::class, 'renderStagePricing'],
            $content
        );

        // Leaderboard shortcode
        $content = preg_replace_callback(
            '/\[leaderboard([^\]]*)\]/',
            [self::class, 'renderLeaderboard'],
            $content
        );

        return $content;
    }

    /**
     * Render community projects widget
     */
    private static function renderCommunityProjects($matches)
    {
        $attributes = self::parseAttributes($matches[1] ?? '');
        
        $type = $attributes['type'] ?? 'all';
        $display = $attributes['display'] ?? 'latest';
        $limit = (int)($attributes['limit'] ?? 6);
        $leaderboard = $attributes['leaderboard'] ?? '1';

        $query = CommunityProject::query();

        if ($type !== 'all') {
            $query->where('type', $type);
        }

        switch ($display) {
            case 'featured':
                $query->where('is_featured', true);
                break;
            case 'top_performing':
                $query->orderBy('total_raised', 'desc');
                break;
            case 'most_anticipated':
                $query->orderBy('supporters_count', 'desc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }

        $projects = $query->limit($limit)->get();

        $html = '<div class="community-projects-widget">';
        $html .= '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">';

        foreach ($projects as $project) {
            $html .= '<div class="bg-white shadow rounded-lg overflow-hidden">';
            $html .= '<div class="p-6">';
            $html .= '<h3 class="text-lg font-medium text-gray-900 mb-2">' . htmlspecialchars($project->title) . '</h3>';
            $html .= '<p class="text-sm text-gray-600 mb-4">' . htmlspecialchars(substr($project->description, 0, 100)) . '...</p>';
            
            if ($project->type === 'crowdfund') {
                $html .= '<div class="mb-4">';
                $html .= '<div class="flex justify-between text-sm text-gray-600 mb-1">';
                $html .= '<span>Raised: $' . number_format($project->total_raised, 2) . '</span>';
                $html .= '<span>Goal: $' . number_format($project->funding_goal, 2) . '</span>';
                $html .= '</div>';
                $progress = ($project->funding_goal > 0) ? ($project->total_raised / $project->funding_goal) * 100 : 0;
                $html .= '<div class="w-full bg-gray-200 rounded-full h-2">';
                $html .= '<div class="bg-green-600 h-2 rounded-full" style="width: ' . min(100, $progress) . '%"></div>';
                $html .= '</div>';
                $html .= '</div>';
            }

            $html .= '<a href="/community/' . $project->id . '" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">';
            $html .= 'View Project';
            $html .= '</a>';
            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';

        if ($leaderboard === '1') {
            $html .= '<div class="mt-8">';
            $html .= '<h3 class="text-lg font-medium text-gray-900 mb-4">Top Contributors</h3>';
            $html .= '<div class="bg-white shadow rounded-lg p-6">';
            $html .= '<p class="text-gray-600">Leaderboard coming soon...</p>';
            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Render stage pricing widget
     */
    private static function renderStagePricing($matches)
    {
        $attributes = self::parseAttributes($matches[1] ?? '');
        
        $layout = $attributes['layout'] ?? 'grid';
        $stages = $attributes['stages'] ?? 'all';
        $benefits = $attributes['benefits'] ?? '1';

        $query = MembershipStage::query();

        if ($stages === 'paid') {
            $query->where('commission_rate', '>', 0);
        } elseif ($stages === 'featured') {
            $query->where('is_featured', true);
        }

        $membershipStages = $query->orderBy('sort_order')->get();

        $html = '<div class="stage-pricing-widget">';
        
        if ($layout === 'grid') {
            $html .= '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">';
        } else {
            $html .= '<div class="space-y-6">';
        }

        foreach ($membershipStages as $stage) {
            $html .= '<div class="bg-white shadow rounded-lg overflow-hidden">';
            $html .= '<div class="px-6 py-4 bg-gray-50 border-b border-gray-200">';
            $html .= '<h3 class="text-lg font-semibold text-gray-900">' . htmlspecialchars($stage->name) . '</h3>';
            $html .= '<p class="text-sm text-gray-600">' . htmlspecialchars($stage->requirements_text ?? 'No specific requirements') . '</p>';
            $html .= '</div>';

            $html .= '<div class="px-6 py-4">';
            $html .= '<div class="text-center mb-4">';
            
            if ($stage->commission_rate > 0) {
                $html .= '<p class="text-3xl font-bold text-gray-900">$' . number_format($stage->commission_rate, 2) . '</p>';
                $html .= '<p class="text-sm text-gray-500">activation price</p>';
            } else {
                $html .= '<p class="text-3xl font-bold text-green-600">FREE</p>';
                $html .= '<p class="text-sm text-gray-500">membership</p>';
            }
            
            $html .= '</div>';

            if ($benefits === '1' && !empty($stage->benefits)) {
                $html .= '<div class="space-y-2 mb-4">';
                $html .= '<h4 class="text-sm font-medium text-gray-900">Benefits:</h4>';
                $html .= '<ul class="text-sm text-gray-600 space-y-1">';
                foreach ($stage->benefits as $benefit) {
                    $html .= '<li class="flex items-start">';
                    $html .= '<svg class="flex-shrink-0 h-4 w-4 text-green-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">';
                    $html .= '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>';
                    $html .= '</svg>';
                    $html .= htmlspecialchars($benefit);
                    $html .= '</li>';
                }
                $html .= '</ul>';
                $html .= '</div>';
            }

            $html .= '</div>';

            $html .= '<div class="px-6 py-3 bg-gray-50 border-t border-gray-200">';
            $html .= '<a href="/activations" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">';
            
            if ($stage->commission_rate > 0) {
                $html .= 'Activate for $' . number_format($stage->commission_rate, 2);
            } else {
                $html .= 'Join Free';
            }
            
            $html .= '</a>';
            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Render leaderboard widget
     */
    private static function renderLeaderboard($matches)
    {
        $attributes = self::parseAttributes($matches[1] ?? '');
        
        $type = $attributes['type'] ?? 'referrals';
        $limit = (int)($attributes['limit'] ?? 10);

        $html = '<div class="leaderboard-widget">';
        $html .= '<div class="bg-white shadow rounded-lg p-6">';
        $html .= '<h3 class="text-lg font-medium text-gray-900 mb-4">Top ' . ucfirst($type) . '</h3>';
        $html .= '<div class="space-y-3">';

        // Sample leaderboard data
        for ($i = 1; $i <= min($limit, 5); $i++) {
            $html .= '<div class="flex items-center justify-between">';
            $html .= '<div class="flex items-center">';
            $html .= '<span class="text-sm font-medium text-gray-500 w-6">#' . $i . '</span>';
            $html .= '<div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center ml-3">';
            $html .= '<span class="text-sm font-medium text-gray-600">U' . $i . '</span>';
            $html .= '</div>';
            $html .= '<span class="ml-3 text-sm font-medium text-gray-900">User ' . $i . '</span>';
            $html .= '</div>';
            $html .= '<span class="text-sm text-gray-600">' . (50 - ($i * 5)) . ' ' . $type . '</span>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Parse shortcode attributes
     */
    private static function parseAttributes($attributeString)
    {
        $attributes = [];
        
        if (preg_match_all('/(\w+)=["\']([^"\']*)["\']/', $attributeString, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $attributes[$match[1]] = $match[2];
            }
        }

        return $attributes;
    }
}
