<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\UserStageActivation;
use App\Models\MembershipStage;
use App\Models\EarningHistory;
use App\Models\FeaturedProject;
use App\Models\CommunityProject;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{

    /**
     * Admin dashboard with comprehensive statistics
     */
    public function dashboard()
    {
        $user = Auth::user();

        // User Statistics
        $userStats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'earthfriendly_members' => User::where('membership_tier', 'earthfriendly')->count(),
            'light_members' => User::where('membership_tier', 'light')->count(),
            'new_users_this_month' => User::whereMonth('created_at', now()->month)->count(),
            'pending_approvals' => User::where('account_status', 'pending')->count(),
        ];

        // Financial Statistics
        $financialStats = [
            'total_earnings' => User::sum('total_earnings'),
            'total_balance' => User::sum('available_balance'),
            'pending_withdrawals' => EarningHistory::where('status', 'pending')->sum('amount'),
            'total_paid_out' => EarningHistory::where('status', 'paid')->sum('amount'),
        ];

        // Activation Statistics
        $activationStats = [
            'total_activations' => UserStageActivation::where('approval_status', 'approved')->count(),
            'pending_activations' => UserStageActivation::where('approval_status', 'pending')->count(),
            'rejected_activations' => UserStageActivation::where('approval_status', 'rejected')->count(),
        ];

        // Project Statistics
        $projectStats = [
            'total_projects' => CommunityProject::count(),
            'active_projects' => CommunityProject::where('status', 'featured')->count(),
            'completed_projects' => CommunityProject::where('status', 'closed')->count(),
            'featured_projects' => FeaturedProject::where('status', 'active')->count(),
        ];

        // Recent Activities
        $recentUsers = User::latest()->take(5)->get();
        $recentActivations = UserStageActivation::with(['user', 'membershipStage'])
            ->latest()->take(5)->get();
        $recentEarnings = EarningHistory::with('user')
            ->latest()->take(5)->get();

        return view('admin.dashboard', compact(
            'userStats', 'financialStats', 'activationStats', 'projectStats',
            'recentUsers', 'recentActivations', 'recentEarnings'
        ));
    }

    /**
     * User Management
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Filters
        if ($request->filled('status')) {
            $query->where('account_status', $request->status);
        }

        if ($request->filled('tier')) {
            $query->where('membership_tier', $request->tier);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%");
            });
        }

        $users = $query->withCount(['referrals', 'activeStageActivations'])
            ->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Approve/Reject User
     */
    public function updateUserStatus(Request $request, User $user)
    {
        $request->validate([
            'status' => 'required|in:approved,rejected,suspended',
            'notes' => 'nullable|string|max:500'
        ]);

        $user->update([
            'account_status' => $request->status,
            'approval_notes' => $request->notes,
            'approved_at' => $request->status === 'approved' ? now() : null,
            'approved_by' => Auth::id(),
        ]);

        return back()->with('success', "User status updated to {$request->status}");
    }

    /**
     * Stage Activation Management
     */
    public function activations(Request $request)
    {
        $query = UserStageActivation::with(['user', 'membershipStage']);

        if ($request->filled('status')) {
            $query->where('approval_status', $request->status);
        }

        if ($request->filled('stage')) {
            $query->where('membership_stage_id', $request->stage);
        }

        $activations = $query->latest()->paginate(20);
        $stages = MembershipStage::all();

        return view('admin.activations.index', compact('activations', 'stages'));
    }

    /**
     * Approve/Reject Stage Activation
     */
    public function updateActivationStatus(Request $request, UserStageActivation $activation)
    {
        $request->validate([
            'status' => 'required|in:approved,rejected',
            'notes' => 'nullable|string|max:500'
        ]);

        $activation->update([
            'approval_status' => $request->status,
            'admin_notes' => $request->notes,
            'approved_at' => $request->status === 'approved' ? now() : null,
            'approved_by' => Auth::id(),
        ]);

        // If approved, update user's membership tier
        if ($request->status === 'approved') {
            $activation->user->update([
                'membership_tier' => 'light',
                'light_member_activated_at' => now(),
            ]);
        }

        return back()->with('success', "Activation {$request->status} successfully");
    }

    /**
     * Earnings Management
     */
    public function earnings(Request $request)
    {
        $query = EarningHistory::with('user');

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $earnings = $query->latest()->paginate(20);

        return view('admin.earnings.index', compact('earnings'));
    }

    /**
     * Approve/Reject Earnings
     */
    public function updateEarningStatus(Request $request, EarningHistory $earning)
    {
        $request->validate([
            'status' => 'required|in:approved,rejected,paid',
        ]);

        $earning->update([
            'status' => $request->status,
            'paid_at' => $request->status === 'paid' ? now() : null,
        ]);

        // If approved and auto_pay is enabled, process payment
        if ($request->status === 'approved' && $earning->auto_pay) {
            $this->processAutoPayment($earning);
        }

        return back()->with('success', "Earning {$request->status} successfully");
    }

    /**
     * Process automatic payment
     */
    private function processAutoPayment(EarningHistory $earning)
    {
        // Add to user's available balance
        $earning->user->increment('available_balance', $earning->amount);

        // Update earning status
        $earning->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);
    }
}
