<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('points')->default(0);
            $table->string('source'); // daily_visit, project_completion, achievement, etc.
            $table->string('description')->nullable();
            $table->json('metadata')->nullable(); // Additional data about the point earning
            $table->boolean('is_redeemed')->default(false);
            $table->timestamp('redeemed_at')->nullable();
            $table->decimal('cash_value', 10, 2)->nullable(); // Cash equivalent when redeemed
            $table->timestamps();

            $table->index(['user_id', 'source']);
            $table->index(['user_id', 'is_redeemed']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_points');
    }
};
