@extends('layouts.app')

@section('title', 'Helpers - Point Sharing System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Helpers - Point Sharing System</h1>
                <p class="text-gray-600 mt-1">Share points with fellow light members and support meaningful causes</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="showRequestPoints()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    View Point Requests
                </button>
                <button onclick="showCreateRequest()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Create New Request
                </button>
            </div>
        </div>
    </div>

    <!-- Point Balance -->
    <div class="bg-white shadow-lg rounded-lg border border-gray-200 p-8 mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Your Point Balance</h2>
                <p class="text-gray-600 text-lg">Available for sharing and requests</p>
            </div>
            <div class="text-right">
                <div class="text-5xl font-bold text-indigo-600">12,450</div>
                <div class="text-gray-500 text-lg font-medium">Total Points</div>
            </div>
        </div>

        <!-- Additional Balance Info -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-700">8,230</div>
                <div class="text-sm text-green-600 font-medium">Available to Share</div>
            </div>
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-700">3,120</div>
                <div class="text-sm text-blue-600 font-medium">Shared This Month</div>
            </div>
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-purple-700">1,100</div>
                <div class="text-sm text-purple-600 font-medium">Reserved Points</div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'share' }">
                <button @click="activeTab = 'share'" :class="activeTab === 'share' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Share Points
                </button>
                <button @click="activeTab = 'requests'" :class="activeTab === 'requests' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Point Requests
                </button>
                <button @click="activeTab = 'history'" :class="activeTab === 'history' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Sharing History
                </button>
                <button @click="activeTab = 'events'" :class="activeTab === 'events' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Special Events
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'share' }">
        <!-- Share Points Tab -->
        <div x-show="activeTab === 'share'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Share Points with Light Members</h3>
                </div>
                <div class="p-6">
                    <form onsubmit="sharePoints(event)" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Point Type</label>
                                <select id="pointType" class="w-full border border-gray-300 rounded-md px-3 py-2" onchange="updatePointDescription()">
                                    <option value="">Select Point Type</option>
                                    <option value="free">Free Points - Gift to other users</option>
                                    <option value="orphanage">Orphanage Points - Assist orphanage individuals</option>
                                    <option value="scholarship">Scholarship Points - Award scholarships</option>
                                    <option value="medical">Medical Points - Support medical expenses</option>
                                    <option value="education">Education Points - Support educational needs</option>
                                    <option value="emergency">Emergency Points - Emergency assistance</option>
                                    <option value="community">Community Points - Community development</option>
                                    <option value="environment">Environment Points - Environmental causes</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Recipient (Light Member)</label>
                                <input type="text" id="recipient" placeholder="Enter member ID or email" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Point Amount</label>
                            <input type="number" id="pointAmount" min="1" max="12450" placeholder="Enter points to share" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Message/Purpose</label>
                            <textarea id="shareMessage" rows="4" placeholder="Describe the purpose of this point sharing..." class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
                        </div>

                        <div id="pointDescription" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-800 mb-2">Point Type Description</h4>
                            <p id="descriptionText" class="text-sm text-blue-700"></p>
                        </div>

                        <div class="flex items-center justify-end space-x-3">
                            <button type="button" onclick="resetForm()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Reset
                            </button>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                Share Points
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Point Requests Tab -->
        <div x-show="activeTab === 'requests'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Point Requests from Community</h3>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            23 Active Requests
                        </span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Request Item -->
                        <div class="border border-gray-200 rounded-lg p-6 hover:border-indigo-300 transition-colors">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Sarah+Johnson&background=10B981&color=fff" alt="">
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900">Sarah Johnson</h4>
                                            <p class="text-sm text-gray-500">Light Member • ID: LM12345</p>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Scholarship Points
                                        </span>
                                    </div>
                                    <p class="text-gray-700 mb-3">Requesting scholarship points to support my younger sister's college education. She has excellent grades but our family is facing financial difficulties.</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>Requested: 2,500 points</span>
                                        <span>•</span>
                                        <span>2 hours ago</span>
                                        <span>•</span>
                                        <span class="text-green-600">Admin Approved</span>
                                    </div>
                                </div>
                                <div class="ml-6 flex-shrink-0">
                                    <button onclick="fulfillRequest('req_001')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                        Fulfill Request
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Request Item -->
                        <div class="border border-gray-200 rounded-lg p-6 hover:border-indigo-300 transition-colors">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Michael+Chen&background=F59E0B&color=fff" alt="">
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900">Michael Chen</h4>
                                            <p class="text-sm text-gray-500">Light Member • ID: LM67890</p>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                            Medical Points
                                        </span>
                                    </div>
                                    <p class="text-gray-700 mb-3">Need medical points to help cover treatment costs for my elderly mother's surgery. Any support would be greatly appreciated.</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>Requested: 5,000 points</span>
                                        <span>•</span>
                                        <span>5 hours ago</span>
                                        <span>•</span>
                                        <span class="text-green-600">Admin Approved</span>
                                    </div>
                                </div>
                                <div class="ml-6 flex-shrink-0">
                                    <button onclick="fulfillRequest('req_002')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                        Fulfill Request
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Request Item -->
                        <div class="border border-gray-200 rounded-lg p-6 hover:border-indigo-300 transition-colors">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Emma+Wilson&background=8B5CF6&color=fff" alt="">
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900">Emma Wilson</h4>
                                            <p class="text-sm text-gray-500">Light Member • ID: LM11111</p>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                            Orphanage Points
                                        </span>
                                    </div>
                                    <p class="text-gray-700 mb-3">Organizing a support program for local orphanage children. Looking for points to provide educational materials and basic necessities.</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>Requested: 3,200 points</span>
                                        <span>•</span>
                                        <span>1 day ago</span>
                                        <span>•</span>
                                        <span class="text-green-600">Admin Approved</span>
                                    </div>
                                </div>
                                <div class="ml-6 flex-shrink-0">
                                    <button onclick="fulfillRequest('req_003')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                        Fulfill Request
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Load More -->
                    <div class="mt-6 text-center">
                        <button onclick="loadMoreRequests()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Load More Requests
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sharing History Tab -->
        <div x-show="activeTab === 'history'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Your Point Sharing History</h3>
                </div>
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipient</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-23</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Sarah Johnson</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Scholarship
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,500</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            Completed
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Michael Chen</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                            Medical
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            Completed
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Special Events Tab -->
        <div x-show="activeTab === 'events'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Special Point Events</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Event Item -->
                        <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-xl font-bold">Holiday Scholarship Drive</h4>
                                    <p class="text-purple-100 mt-1">Special scholarship points available for holiday season</p>
                                    <div class="mt-3 text-sm">
                                        <span class="bg-white bg-opacity-20 rounded px-2 py-1">Available: Dec 20-31, 2024</span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold">5,000</div>
                                    <div class="text-purple-100">Points Available</div>
                                </div>
                            </div>
                        </div>

                        <!-- Event Item -->
                        <div class="bg-gradient-to-r from-green-500 to-teal-500 rounded-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-xl font-bold">Medical Emergency Fund</h4>
                                    <p class="text-green-100 mt-1">Emergency medical points for urgent cases</p>
                                    <div class="mt-3 text-sm">
                                        <span class="bg-white bg-opacity-20 rounded px-2 py-1">Always Available</span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold">10,000</div>
                                    <div class="text-green-100">Points Available</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Request Modal -->
<div id="createRequestModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900">Create Point Request</h3>
                <button onclick="closeCreateRequest()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form onsubmit="submitRequest(event)" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Request Type</label>
                    <select id="requestType" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">Select Request Type</option>
                        <option value="scholarship">Scholarship Points</option>
                        <option value="medical">Medical Points</option>
                        <option value="orphanage">Orphanage Points</option>
                        <option value="emergency">Emergency Points</option>
                        <option value="education">Education Points</option>
                        <option value="community">Community Points</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Points Needed</label>
                    <input type="number" id="requestPoints" min="1" placeholder="Enter points needed" class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Detailed Description</label>
                    <textarea id="requestDescription" rows="4" placeholder="Provide detailed information about your request..." class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Review Process</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>Your request will be reviewed by administrators before being displayed to the community. Please provide accurate and detailed information.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-3">
                    <button type="button" onclick="closeCreateRequest()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updatePointDescription() {
    const pointType = document.getElementById('pointType').value;
    const descriptionDiv = document.getElementById('pointDescription');
    const descriptionText = document.getElementById('descriptionText');
    
    const descriptions = {
        'free': 'Free points can be gifted to any light member without specific requirements. These are general purpose points for showing appreciation or support.',
        'orphanage': 'Orphanage points are specifically designated to assist individuals in orphanages with their basic needs, education, and development.',
        'scholarship': 'Scholarship points are awarded to support educational pursuits, including tuition fees, books, and educational materials.',
        'medical': 'Medical points help cover medical expenses, treatments, surgeries, and healthcare-related costs for those in need.',
        'education': 'Education points support various educational initiatives, training programs, and skill development activities.',
        'emergency': 'Emergency points provide immediate assistance during urgent situations and unexpected crises.',
        'community': 'Community points support local community development projects and initiatives that benefit the wider community.',
        'environment': 'Environment points are dedicated to environmental conservation projects and sustainability initiatives.'
    };
    
    if (pointType && descriptions[pointType]) {
        descriptionText.textContent = descriptions[pointType];
        descriptionDiv.classList.remove('hidden');
    } else {
        descriptionDiv.classList.add('hidden');
    }
}

function sharePoints(event) {
    event.preventDefault();
    const pointType = document.getElementById('pointType').value;
    const recipient = document.getElementById('recipient').value;
    const amount = document.getElementById('pointAmount').value;
    const message = document.getElementById('shareMessage').value;
    
    if (!pointType || !recipient || !amount || !message) {
        alert('Please fill in all required fields.');
        return;
    }
    
    if (confirm(`Share ${amount} ${pointType} points with ${recipient}?`)) {
        alert('Points shared successfully! The transaction is being processed.');
        resetForm();
    }
}

function resetForm() {
    document.getElementById('pointType').value = '';
    document.getElementById('recipient').value = '';
    document.getElementById('pointAmount').value = '';
    document.getElementById('shareMessage').value = '';
    document.getElementById('pointDescription').classList.add('hidden');
}

function fulfillRequest(requestId) {
    if (confirm('Fulfill this point request? This action cannot be undone.')) {
        alert(`Request ${requestId} fulfilled successfully!`);
    }
}

function loadMoreRequests() {
    alert('Loading more requests...');
}

function showCreateRequest() {
    document.getElementById('createRequestModal').classList.remove('hidden');
}

function closeCreateRequest() {
    document.getElementById('createRequestModal').classList.add('hidden');
}

function showRequestPoints() {
    // Switch to requests tab
    alert('Switching to Point Requests view...');
}

function submitRequest(event) {
    event.preventDefault();
    const requestType = document.getElementById('requestType').value;
    const points = document.getElementById('requestPoints').value;
    const description = document.getElementById('requestDescription').value;
    
    if (!requestType || !points || !description) {
        alert('Please fill in all required fields.');
        return;
    }
    
    alert('Request submitted successfully! It will be reviewed by administrators before being displayed to the community.');
    closeCreateRequest();
}
</script>
@endsection
