<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProjectVote extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'user_id',
        'vote_type',
        'comment'
    ];

    // Relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(CommunityProject::class, 'project_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getVoteTypeDisplayAttribute(): string
    {
        return ucfirst($this->vote_type);
    }

    public function getVoteTypeColorAttribute(): string
    {
        return match($this->vote_type) {
            'support' => 'text-green-600',
            'against' => 'text-red-600',
            default => 'text-gray-600'
        };
    }

    // Scopes
    public function scopeSupport($query)
    {
        return $query->where('vote_type', 'support');
    }

    public function scopeAgainst($query)
    {
        return $query->where('vote_type', 'against');
    }
}
