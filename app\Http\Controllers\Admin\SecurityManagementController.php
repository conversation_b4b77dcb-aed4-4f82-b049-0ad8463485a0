<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class SecurityManagementController extends Controller
{
    public function index()
    {
        $securitySettings = $this->getSecuritySettings();
        $recentLogs = $this->getRecentSecurityLogs();
        $securityStats = $this->getSecurityStatistics();
        
        return view('admin.security-management.index', compact('securitySettings', 'recentLogs', 'securityStats'));
    }

    public function updateAuthenticationSettings(Request $request)
    {
        $request->validate([
            'require_2fa' => 'boolean',
            'password_min_length' => 'required|integer|min:6|max:50',
            'password_require_uppercase' => 'boolean',
            'password_require_lowercase' => 'boolean',
            'password_require_numbers' => 'boolean',
            'password_require_symbols' => 'boolean',
            'password_expiry_days' => 'nullable|integer|min:30|max:365',
            'max_login_attempts' => 'required|integer|min:3|max:10',
            'lockout_duration' => 'required|integer|min:5|max:1440',
            'session_timeout' => 'required|integer|min:15|max:480',
        ]);

        $settings = [
            'require_2fa' => $request->boolean('require_2fa'),
            'password_min_length' => $request->password_min_length,
            'password_require_uppercase' => $request->boolean('password_require_uppercase'),
            'password_require_lowercase' => $request->boolean('password_require_lowercase'),
            'password_require_numbers' => $request->boolean('password_require_numbers'),
            'password_require_symbols' => $request->boolean('password_require_symbols'),
            'password_expiry_days' => $request->password_expiry_days,
            'max_login_attempts' => $request->max_login_attempts,
            'lockout_duration' => $request->lockout_duration,
            'session_timeout' => $request->session_timeout,
        ];

        $this->updateSecuritySettings('authentication', $settings);

        return response()->json([
            'success' => true,
            'message' => 'Authentication settings updated successfully!'
        ]);
    }

    public function updateAccessControl(Request $request)
    {
        $request->validate([
            'ip_whitelist' => 'nullable|string',
            'ip_blacklist' => 'nullable|string',
            'enable_ip_blocking' => 'boolean',
            'enable_geo_blocking' => 'boolean',
            'blocked_countries' => 'nullable|array',
            'enable_rate_limiting' => 'boolean',
            'rate_limit_requests' => 'required|integer|min:10|max:1000',
            'rate_limit_window' => 'required|integer|min:1|max:60',
        ]);

        $settings = [
            'ip_whitelist' => $this->parseIpList($request->ip_whitelist),
            'ip_blacklist' => $this->parseIpList($request->ip_blacklist),
            'enable_ip_blocking' => $request->boolean('enable_ip_blocking'),
            'enable_geo_blocking' => $request->boolean('enable_geo_blocking'),
            'blocked_countries' => $request->blocked_countries ?? [],
            'enable_rate_limiting' => $request->boolean('enable_rate_limiting'),
            'rate_limit_requests' => $request->rate_limit_requests,
            'rate_limit_window' => $request->rate_limit_window,
        ];

        $this->updateSecuritySettings('access_control', $settings);

        return response()->json([
            'success' => true,
            'message' => 'Access control settings updated successfully!'
        ]);
    }

    public function updateSecurityPolicies(Request $request)
    {
        $request->validate([
            'enable_audit_logging' => 'boolean',
            'enable_failed_login_alerts' => 'boolean',
            'enable_suspicious_activity_detection' => 'boolean',
            'enable_data_encryption' => 'boolean',
            'enable_secure_headers' => 'boolean',
            'enable_csrf_protection' => 'boolean',
            'enable_xss_protection' => 'boolean',
            'enable_content_security_policy' => 'boolean',
        ]);

        $settings = [
            'enable_audit_logging' => $request->boolean('enable_audit_logging'),
            'enable_failed_login_alerts' => $request->boolean('enable_failed_login_alerts'),
            'enable_suspicious_activity_detection' => $request->boolean('enable_suspicious_activity_detection'),
            'enable_data_encryption' => $request->boolean('enable_data_encryption'),
            'enable_secure_headers' => $request->boolean('enable_secure_headers'),
            'enable_csrf_protection' => $request->boolean('enable_csrf_protection'),
            'enable_xss_protection' => $request->boolean('enable_xss_protection'),
            'enable_content_security_policy' => $request->boolean('enable_content_security_policy'),
        ];

        $this->updateSecuritySettings('policies', $settings);

        return response()->json([
            'success' => true,
            'message' => 'Security policies updated successfully!'
        ]);
    }

    public function getSecurityLogs(Request $request)
    {
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);
        $date = $request->get('date');
        $type = $request->get('type');
        $user = $request->get('user');

        $query = $this->buildSecurityLogsQuery($date, $type, $user);
        
        $logs = $query->orderBy('created_at', 'desc')
                     ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $logs->items(),
            'pagination' => [
                'current_page' => $logs->currentPage(),
                'last_page' => $logs->lastPage(),
                'per_page' => $logs->perPage(),
                'total' => $logs->total(),
            ]
        ]);
    }

    public function exportSecurityLogs(Request $request)
    {
        $date = $request->get('date');
        $type = $request->get('type');
        $user = $request->get('user');

        $logs = $this->buildSecurityLogsQuery($date, $type, $user)
                     ->orderBy('created_at', 'desc')
                     ->get();

        $filename = 'security_logs_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($logs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, ['Timestamp', 'Event Type', 'User', 'IP Address', 'User Agent', 'Details']);
            
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->created_at,
                    $log->event_type,
                    $log->user_email ?? 'N/A',
                    $log->ip_address,
                    $log->user_agent,
                    $log->details
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function getSecurityAnalytics(Request $request)
    {
        $period = $request->get('period', '7d'); // 7d, 30d, 90d
        
        $analytics = [
            'login_attempts' => $this->getLoginAttemptStats($period),
            'failed_logins' => $this->getFailedLoginStats($period),
            'blocked_ips' => $this->getBlockedIpStats($period),
            'security_events' => $this->getSecurityEventStats($period),
            'user_activity' => $this->getUserActivityStats($period),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics
        ]);
    }

    public function blockIpAddress(Request $request)
    {
        $request->validate([
            'ip_address' => 'required|ip',
            'reason' => 'nullable|string|max:255',
            'duration' => 'nullable|integer|min:1', // hours
        ]);

        $this->addToBlacklist($request->ip_address, $request->reason, $request->duration);

        $this->logSecurityEvent('ip_blocked', [
            'ip_address' => $request->ip_address,
            'reason' => $request->reason,
            'duration' => $request->duration,
            'blocked_by' => Auth::user()->email
        ]);

        return response()->json([
            'success' => true,
            'message' => 'IP address blocked successfully!'
        ]);
    }

    public function unblockIpAddress(Request $request)
    {
        $request->validate([
            'ip_address' => 'required|ip'
        ]);

        $this->removeFromBlacklist($request->ip_address);

        $this->logSecurityEvent('ip_unblocked', [
            'ip_address' => $request->ip_address,
            'unblocked_by' => Auth::user()->email
        ]);

        return response()->json([
            'success' => true,
            'message' => 'IP address unblocked successfully!'
        ]);
    }

    public function runSecurityScan()
    {
        $results = [
            'vulnerabilities' => $this->scanForVulnerabilities(),
            'weak_passwords' => $this->scanForWeakPasswords(),
            'outdated_sessions' => $this->scanForOutdatedSessions(),
            'suspicious_activity' => $this->scanForSuspiciousActivity(),
            'security_score' => 0
        ];

        // Calculate security score
        $totalChecks = 4;
        $passedChecks = 0;
        
        if (empty($results['vulnerabilities'])) $passedChecks++;
        if (empty($results['weak_passwords'])) $passedChecks++;
        if (empty($results['outdated_sessions'])) $passedChecks++;
        if (empty($results['suspicious_activity'])) $passedChecks++;
        
        $results['security_score'] = round(($passedChecks / $totalChecks) * 100);

        $this->logSecurityEvent('security_scan', [
            'score' => $results['security_score'],
            'vulnerabilities_found' => count($results['vulnerabilities']),
            'performed_by' => Auth::user()->email
        ]);

        return response()->json([
            'success' => true,
            'data' => $results
        ]);
    }

    private function getSecuritySettings()
    {
        return Cache::remember('security_settings', 3600, function () {
            return [
                'authentication' => [
                    'require_2fa' => false,
                    'password_min_length' => 8,
                    'password_require_uppercase' => true,
                    'password_require_lowercase' => true,
                    'password_require_numbers' => true,
                    'password_require_symbols' => false,
                    'password_expiry_days' => null,
                    'max_login_attempts' => 5,
                    'lockout_duration' => 15,
                    'session_timeout' => 120,
                ],
                'access_control' => [
                    'ip_whitelist' => [],
                    'ip_blacklist' => [],
                    'enable_ip_blocking' => true,
                    'enable_geo_blocking' => false,
                    'blocked_countries' => [],
                    'enable_rate_limiting' => true,
                    'rate_limit_requests' => 100,
                    'rate_limit_window' => 1,
                ],
                'policies' => [
                    'enable_audit_logging' => true,
                    'enable_failed_login_alerts' => true,
                    'enable_suspicious_activity_detection' => true,
                    'enable_data_encryption' => true,
                    'enable_secure_headers' => true,
                    'enable_csrf_protection' => true,
                    'enable_xss_protection' => true,
                    'enable_content_security_policy' => false,
                ]
            ];
        });
    }

    private function updateSecuritySettings($section, $settings)
    {
        $currentSettings = $this->getSecuritySettings();
        $currentSettings[$section] = array_merge($currentSettings[$section], $settings);
        
        Cache::put('security_settings', $currentSettings, 3600);
        
        $this->logSecurityEvent('settings_updated', [
            'section' => $section,
            'updated_by' => Auth::user()->email
        ]);
    }

    private function getRecentSecurityLogs($limit = 10)
    {
        // Mock data - replace with actual database query
        return collect([
            [
                'id' => 1,
                'event_type' => 'login_success',
                'user_email' => '<EMAIL>',
                'ip_address' => '***********',
                'created_at' => Carbon::now()->subMinutes(5),
                'details' => 'Successful login'
            ],
            [
                'id' => 2,
                'event_type' => 'login_failed',
                'user_email' => '<EMAIL>',
                'ip_address' => '***********',
                'created_at' => Carbon::now()->subMinutes(10),
                'details' => 'Invalid credentials'
            ]
        ]);
    }

    private function getSecurityStatistics()
    {
        return [
            'total_users' => 1247,
            'active_sessions' => 89,
            'failed_logins_today' => 23,
            'blocked_ips' => 5,
            'security_score' => 87
        ];
    }

    private function parseIpList($ipString)
    {
        if (empty($ipString)) {
            return [];
        }
        
        return array_filter(array_map('trim', explode("\n", $ipString)));
    }

    private function buildSecurityLogsQuery($date, $type, $user)
    {
        // Mock query builder - replace with actual implementation
        return collect([]);
    }

    private function logSecurityEvent($eventType, $data = [])
    {
        Log::channel('security')->info($eventType, array_merge($data, [
            'user_id' => Auth::id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]));
    }

    private function addToBlacklist($ipAddress, $reason, $duration)
    {
        // Implementation for adding IP to blacklist
    }

    private function removeFromBlacklist($ipAddress)
    {
        // Implementation for removing IP from blacklist
    }

    private function scanForVulnerabilities()
    {
        // Security vulnerability scanning logic
        return [];
    }

    private function scanForWeakPasswords()
    {
        // Weak password detection logic
        return [];
    }

    private function scanForOutdatedSessions()
    {
        // Outdated session detection logic
        return [];
    }

    private function scanForSuspiciousActivity()
    {
        // Suspicious activity detection logic
        return [];
    }

    private function getLoginAttemptStats($period)
    {
        // Login attempt statistics
        return ['total' => 1247, 'successful' => 1189, 'failed' => 58];
    }

    private function getFailedLoginStats($period)
    {
        // Failed login statistics
        return ['total' => 58, 'unique_ips' => 23, 'blocked' => 5];
    }

    private function getBlockedIpStats($period)
    {
        // Blocked IP statistics
        return ['total' => 15, 'active' => 5, 'expired' => 10];
    }

    private function getSecurityEventStats($period)
    {
        // Security event statistics
        return ['total' => 234, 'high_priority' => 12, 'medium_priority' => 45];
    }

    private function getUserActivityStats($period)
    {
        // User activity statistics
        return ['active_users' => 89, 'new_registrations' => 23, 'suspicious_accounts' => 3];
    }
}
