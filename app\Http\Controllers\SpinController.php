<?php

namespace App\Http\Controllers;

use App\Models\SpinSetting;
use App\Models\SpinPrize;
use App\Models\UserSpin;
use App\Models\UserSpinProgress;
use App\Models\User;
use App\Models\UserPoint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SpinController extends Controller
{
    /**
     * Display the spin page
     */
    public function index()
    {
        try {
            $user = Auth::user();

            // Get or create spin settings
            $settings = SpinSetting::first();
            if (!$settings) {
                $settings = SpinSetting::create([
                    'spins_required_to_win' => 10,
                    'spin_cost_points' => 100,
                    'is_active' => true,
                    'prize_delivery_methods' => ['instant', 'events', 'cash', 'pickup_station', 'digital'],
                ]);
            }

            // Check if spin system is active
            if (!$settings->is_active) {
                return redirect()->route('wallet.index')->with('error', 'Spin system is currently disabled.');
            }

            // Get or create user progress
            $progress = UserSpinProgress::where('user_id', $user->id)->first();
            if (!$progress) {
                $progress = UserSpinProgress::create([
                    'user_id' => $user->id,
                    'current_spin_count' => 0,
                    'last_free_spin_date' => null,
                ]);
            }

            // Get available prizes
            $availablePrizes = SpinPrize::where('is_active', true)->get()->map(function ($prize) {
                return [
                    'name' => $prize->name,
                    'description' => $prize->description,
                    'emoji' => $prize->emoji,
                    'delivery_method' => $prize->delivery_method_display,
                ];
            });

            // Get recent spins
            $recentSpins = UserSpin::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            $data = [
                'canFreeSpin' => $progress->canUseFreeSpin(),
                'nextFreeSpinTime' => $progress->getNextFreeSpinTime(),
                'userPoints' => $user->total_points ?? 0,
                'spinCost' => $settings->spin_cost_points,
                'spinsToWin' => $settings->spins_required_to_win,
                'currentSpinCount' => $progress->current_spin_count,
                'availablePrizes' => $availablePrizes,
                'recentSpins' => $recentSpins,
            ];

            return view('wallet.spin', $data);
        } catch (\Exception $e) {
            Log::error('Spin page error: ' . $e->getMessage() . ' | Line: ' . $e->getLine() . ' | File: ' . $e->getFile());
            return redirect()->route('wallet.index')->with('error', 'Unable to load spin page. Please try again.');
        }
    }

    /**
     * Process a spin
     */
    public function spin(Request $request)
    {
        $request->validate([
            'spin_type' => 'required|in:free,paid',
        ]);

        $user = Auth::user();
        $spinType = $request->spin_type;
        $settings = SpinSetting::current();

        // Check if spin system is active
        if (!$settings->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Spin system is currently disabled.',
            ]);
        }

        $progress = UserSpinProgress::getForUser($user->id);

        try {
            DB::beginTransaction();

            // Validate spin type
            if ($spinType === 'free') {
                if (!$progress->canUseFreeSpin()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You have already used your free spin today.',
                    ]);
                }
            } elseif ($spinType === 'paid') {
                if ($user->total_points < $settings->spin_cost_points) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Insufficient points for extra spin.',
                    ]);
                }

                // Deduct points by creating a negative point entry
                UserPoint::create([
                    'user_id' => $user->id,
                    'points' => -$settings->spin_cost_points,
                    'source' => 'spin_cost',
                    'description' => 'Extra spin cost',
                    'is_redeemed' => true,
                ]);
            }

            // Determine if this is a winning spin
            $isWinner = $this->determineWinner($progress);
            $prize = null;
            $prizeData = [];

            if ($isWinner) {
                $prize = $this->selectPrize($user->id);
                if ($prize) {
                    $prizeData = [
                        'prize_id' => $prize->id,
                        'prize_name' => $prize->name,
                        'prize_description' => $prize->description,
                        'delivery_method' => $prize->delivery_method,
                        'cash_value' => $prize->cash_value,
                        'points_value' => $prize->points_value,
                    ];

                    // If instant prize (points), add to user's account
                    if ($prize->delivery_method === 'instant' && $prize->points_value) {
                        UserPoint::create([
                            'user_id' => $user->id,
                            'points' => $prize->points_value,
                            'source' => 'spin_prize',
                            'description' => 'Lucky spin prize: ' . $prize->name,
                            'is_redeemed' => false,
                        ]);
                    }
                }
            }

            // Record the spin
            $spin = UserSpin::create([
                'user_id' => $user->id,
                'spin_type' => $spinType,
                'points_spent' => $spinType === 'paid' ? $settings->spin_cost_points : 0,
                'is_winner' => $isWinner,
                ...$prizeData,
            ]);

            // Update progress
            $progress->recordSpin(
                $spinType,
                $isWinner,
                $prize?->cash_value ?? 0,
                $prize?->points_value ?? 0
            );

            // Update daily statistics
            $this->updateDailyStatistics($spinType, $isWinner, $spin);

            DB::commit();

            Log::info('User spin completed', [
                'user_id' => $user->id,
                'spin_type' => $spinType,
                'is_winner' => $isWinner,
                'prize' => $prize?->name,
            ]);

            $response = [
                'success' => true,
                'is_winner' => $isWinner,
                'spins_to_win' => $progress->getSpinsToWin(),
            ];

            if ($isWinner && $prize) {
                $response = array_merge($response, [
                    'prize_name' => $prize->name,
                    'prize_description' => $prize->description,
                    'delivery_method' => $prize->delivery_method_display,
                ]);
            }

            return response()->json($response);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Spin error: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'spin_type' => $spinType,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your spin. Please try again.',
            ]);
        }
    }

    /**
     * Determine if this spin is a winner
     */
    private function determineWinner(UserSpinProgress $progress)
    {
        // Guaranteed win after required number of spins
        if ($progress->isDueForWin()) {
            return true;
        }

        // Random chance (adjust probability as needed)
        $winChance = 15; // 15% chance
        return mt_rand(1, 100) <= $winChance;
    }

    /**
     * Select a prize for the winner
     */
    private function selectPrize($userId)
    {
        $availablePrizes = SpinPrize::active()->get()->filter(function ($prize) use ($userId) {
            return $prize->canBeWonToday() && $prize->canBeWonByUser($userId);
        });

        if ($availablePrizes->isEmpty()) {
            // Fallback to points if no other prizes available
            return SpinPrize::active()->where('delivery_method', 'instant')->first();
        }

        // Select based on weights
        $totalWeight = $availablePrizes->sum('weight');
        $random = mt_rand(1, $totalWeight);
        
        $currentWeight = 0;
        foreach ($availablePrizes as $prize) {
            $currentWeight += $prize->weight;
            if ($random <= $currentWeight) {
                return $prize;
            }
        }

        return $availablePrizes->first();
    }

    /**
     * Update daily statistics
     */
    private function updateDailyStatistics($spinType, $isWinner, UserSpin $spin)
    {
        $today = today();
        
        DB::table('spin_statistics')->updateOrInsert(
            ['date' => $today],
            [
                'total_spins' => DB::raw('total_spins + 1'),
                $spinType === 'free' ? 'free_spins' : 'paid_spins' => DB::raw(($spinType === 'free' ? 'free_spins' : 'paid_spins') . ' + 1'),
                'total_winners' => $isWinner ? DB::raw('total_winners + 1') : DB::raw('total_winners'),
                'points_spent' => DB::raw('points_spent + ' . $spin->points_spent),
                'cash_prizes_given' => $isWinner && $spin->cash_value ? DB::raw('cash_prizes_given + ' . $spin->cash_value) : DB::raw('cash_prizes_given'),
                'points_prizes_given' => $isWinner && $spin->points_value ? DB::raw('points_prizes_given + ' . $spin->points_value) : DB::raw('points_prizes_given'),
                'updated_at' => now(),
            ]
        );
    }
}
