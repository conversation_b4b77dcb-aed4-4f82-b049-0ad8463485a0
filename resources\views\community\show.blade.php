@extends('layouts.app')

@section('title', $project->title)

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
            <div>
                <div class="flex items-center space-x-3 mb-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $project->status_color }}">
                        {{ $project->status_display }}
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                        {{ $project->project_type_display }}
                    </span>
                    <span class="text-sm text-gray-500">ID: {{ $project->project_id }}</span>
                </div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $project->title }}</h1>
                <p class="mt-2 text-gray-600">Created by {{ $project->user->name }} • {{ $project->created_at->diffForHumans() }}</p>
            </div>
            <a href="{{ route('community.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Projects
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Project Images -->
            @if($project->images && count($project->images) > 0)
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="h-64 bg-gray-200 overflow-hidden">
                    <img src="{{ Storage::url($project->images[0]) }}" 
                         alt="{{ $project->title }}" 
                         class="w-full h-full object-cover">
                </div>
                @if(count($project->images) > 1)
                <div class="p-4">
                    <div class="grid grid-cols-4 gap-2">
                        @foreach(array_slice($project->images, 1, 4) as $image)
                        <img src="{{ Storage::url($image) }}" 
                             alt="{{ $project->title }}" 
                             class="w-full h-16 object-cover rounded cursor-pointer hover:opacity-75">
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            @endif

            <!-- Project Description -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Project Description</h2>
                <div class="prose max-w-none">
                    <p class="text-gray-700 leading-relaxed">{{ $project->description }}</p>
                </div>
            </div>

            <!-- Project Requirements -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">What We Need</h2>
                <div class="prose max-w-none">
                    <p class="text-gray-700 leading-relaxed">{{ $project->requirements }}</p>
                </div>
            </div>

            <!-- Project Stages (for featured projects) -->
            @if($project->status === 'featured' && $project->stages->count() > 0)
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Project Roadmap</h2>
                <div class="space-y-4">
                    @foreach($project->stages as $stage)
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $stage->status === 'approved' ? 'bg-green-100 text-green-600' : ($stage->status === 'completed' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600') }}">
                                @if($stage->status === 'approved')
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                @else
                                <span class="text-sm font-medium">{{ $stage->stage_order }}</span>
                                @endif
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-sm font-medium text-gray-900">{{ $stage->title }}</h3>
                            <p class="text-sm text-gray-500">{{ $stage->description }}</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $stage->status_color }} mt-2">
                                {{ $stage->status_display }}
                            </span>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Votes (for petition projects) -->
            @if($project->status === 'petition' && $project->votes->count() > 0)
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Community Votes</h2>
                <div class="space-y-4">
                    @foreach($project->votes->take(10) as $vote)
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-xs font-medium text-gray-700">{{ substr($vote->user->name, 0, 2) }}</span>
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium text-gray-900">{{ $vote->user->name }}</span>
                                <span class="text-sm {{ $vote->vote_type_color }}">{{ $vote->vote_type_display }}</span>
                                <span class="text-xs text-gray-500">{{ $vote->created_at->diffForHumans() }}</span>
                            </div>
                            @if($vote->comment)
                            <p class="text-sm text-gray-600 mt-1">{{ $vote->comment }}</p>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Donations (for featured projects) -->
            @if($project->status === 'featured' && $project->donations->count() > 0)
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Donations</h2>
                <div class="space-y-4">
                    @foreach($project->donations->take(10) as $donation)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $donation->display_name }}</p>
                                <p class="text-xs text-gray-500">{{ $donation->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">{{ $donation->formatted_amount }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Project Stats -->
            @if($project->status === 'petition')
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Voting Progress</h3>
                <div class="space-y-4">
                    <div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="font-medium text-gray-700">{{ $project->votes_count }}/{{ $project->votes_required }} votes</span>
                            <span class="text-gray-500">{{ $project->days_remaining }} days left</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-green-600 h-3 rounded-full" style="width: {{ $project->voting_progress }}%"></div>
                        </div>
                    </div>
                    
                    @if($canVote)
                    <form method="POST" action="{{ route('community.vote', $project) }}" class="space-y-4">
                        @csrf
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Your Vote</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="vote_type" value="support" class="mr-2" required>
                                    <span class="text-sm text-green-600">Support this project</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="vote_type" value="against" class="mr-2" required>
                                    <span class="text-sm text-red-600">Vote against</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label for="comment" class="block text-sm font-medium text-gray-700">Comment (optional)</label>
                            <textarea name="comment" id="comment" rows="3" 
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                      placeholder="Share your thoughts..."></textarea>
                        </div>
                        <button type="submit" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Cast Vote
                        </button>
                    </form>
                    @elseif($userVote)
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">You voted: {{ $userVote->vote_type_display }}</h3>
                                @if($userVote->comment)
                                <p class="text-sm text-blue-700 mt-1">{{ $userVote->comment }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                    @else
                    <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                        <p class="text-sm text-gray-600">Voting period has ended or you cannot vote on this project.</p>
                    </div>
                    @endif
                </div>
            </div>
            @elseif($project->status === 'featured')
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Funding Progress</h3>
                <div class="space-y-4">
                    <div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="font-medium text-gray-700">${{ number_format($project->amount_raised) }} raised</span>
                            <span class="text-gray-500">{{ $project->days_remaining }} days left</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-blue-600 h-3 rounded-full" style="width: {{ $project->funding_progress }}%"></div>
                        </div>
                        <div class="flex items-center justify-between text-xs text-gray-500 mt-1">
                            <span>Goal: ${{ number_format($project->amount_needed) }}</span>
                            <span>{{ number_format($project->funding_progress, 1) }}%</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <p class="text-2xl font-bold text-gray-900">{{ $project->donations->count() }}</p>
                            <p class="text-xs text-gray-500">Donors</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900">{{ $project->volunteer_count }}</p>
                            <p class="text-xs text-gray-500">Volunteers</p>
                        </div>
                    </div>
                    
                    @if($canDonate)
                    <form method="POST" action="{{ route('community.donate', $project) }}" class="space-y-4">
                        @csrf
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700">Donation Amount ($)</label>
                            <input type="number" name="amount" id="amount" min="1" step="0.01" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                   placeholder="0.00">
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="is_anonymous" class="mr-2">
                                <span class="text-sm text-gray-700">Donate anonymously</span>
                            </label>
                        </div>
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700">Message (optional)</label>
                            <textarea name="message" id="message" rows="2" 
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                      placeholder="Leave a message..."></textarea>
                        </div>
                        <button type="submit" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
                            </svg>
                            Donate Now
                        </button>
                    </form>
                    @endif
                    
                    @if($canVolunteer)
                    <div class="border-t border-gray-200 pt-4">
                        <form method="POST" action="{{ route('community.volunteer', $project) }}" class="space-y-4">
                            @csrf
                            <div>
                                <label for="skills_offered" class="block text-sm font-medium text-gray-700">Skills You Can Offer</label>
                                <textarea name="skills_offered" id="skills_offered" rows="2" required
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                          placeholder="Describe your skills and how you can help..."></textarea>
                            </div>
                            <div>
                                <label for="availability" class="block text-sm font-medium text-gray-700">Your Availability</label>
                                <textarea name="availability" id="availability" rows="2" required
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                          placeholder="When are you available to help?"></textarea>
                            </div>
                            <button type="submit" 
                                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                </svg>
                                Volunteer
                            </button>
                        </form>
                    </div>
                    @elseif($userVolunteer)
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Volunteer Status: {{ $userVolunteer->status_display }}</h3>
                                <p class="text-sm text-blue-700 mt-1">Your volunteer application has been submitted.</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Project Creator -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Project Creator</h3>
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-700">{{ substr($project->user->name, 0, 2) }}</span>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">{{ $project->user->name }}</p>
                        <p class="text-xs text-gray-500">Member since {{ $project->user->created_at->format('M Y') }}</p>
                    </div>
                </div>
            </div>

            <!-- Delete Project (if denied and user is creator) -->
            @if($project->status === 'denied' && $project->user_id === Auth::id())
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Project Actions</h3>
                <form method="POST" action="{{ route('community.destroy', $project) }}" 
                      onsubmit="return confirm('Are you sure you want to delete this project? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        Delete Project
                    </button>
                </form>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
