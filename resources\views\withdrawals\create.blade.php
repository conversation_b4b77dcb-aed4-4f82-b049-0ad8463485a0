@extends('layouts.app')

@section('title', 'Request Withdrawal')

@section('content')
<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Request Withdrawal</h1>
        <p class="mt-2 text-gray-600">Withdraw your earnings to your preferred payment method</p>
    </div>

    <!-- Balance Overview -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Available Balance</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-3xl font-bold text-green-600">${{ number_format($user->available_balance, 2) }}</p>
                    <p class="text-sm text-gray-500">Available for withdrawal</p>
                </div>
                <div class="text-right">
                    <p class="text-lg font-semibold text-gray-900">${{ number_format($user->total_earnings, 2) }}</p>
                    <p class="text-sm text-gray-500">Total earnings</p>
                </div>
            </div>
            
            @if($user->available_balance < $minWithdrawal)
            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">
                            Minimum withdrawal amount not met
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>You need at least ${{ number_format($minWithdrawal, 2) }} to request a withdrawal. Keep referring to reach the minimum!</p>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    @if($user->available_balance >= $minWithdrawal)
    <!-- Withdrawal Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Withdrawal Details</h3>
        </div>
        <form method="POST" action="{{ route('withdrawals.store') }}" class="p-6 space-y-6">
            @csrf
            
            <!-- Amount -->
            <div>
                <label for="amount" class="block text-sm font-medium text-gray-700">Withdrawal Amount</label>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <input type="number" 
                           name="amount" 
                           id="amount" 
                           step="0.01" 
                           min="{{ $minWithdrawal }}" 
                           max="{{ $user->available_balance }}"
                           value="{{ old('amount') }}"
                           class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md @error('amount') border-red-300 @enderror" 
                           placeholder="0.00">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 sm:text-sm">USD</span>
                    </div>
                </div>
                @error('amount')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">
                    Minimum: ${{ number_format($minWithdrawal, 2) }} | Maximum: ${{ number_format($user->available_balance, 2) }}
                </p>
            </div>

            <!-- Payment Method -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Payment Method</label>
                <div class="mt-2 space-y-4">
                    <!-- Bank Transfer -->
                    <div class="flex items-center">
                        <input id="bank_transfer" 
                               name="payment_method" 
                               type="radio" 
                               value="bank_transfer"
                               class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                               {{ old('payment_method') === 'bank_transfer' ? 'checked' : '' }}>
                        <label for="bank_transfer" class="ml-3 block text-sm font-medium text-gray-700">
                            Bank Transfer
                        </label>
                    </div>
                    
                    <!-- PayPal -->
                    <div class="flex items-center">
                        <input id="paypal" 
                               name="payment_method" 
                               type="radio" 
                               value="paypal"
                               class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                               {{ old('payment_method') === 'paypal' ? 'checked' : '' }}>
                        <label for="paypal" class="ml-3 block text-sm font-medium text-gray-700">
                            PayPal
                        </label>
                    </div>
                    
                    <!-- Cryptocurrency -->
                    <div class="flex items-center">
                        <input id="crypto" 
                               name="payment_method" 
                               type="radio" 
                               value="crypto"
                               class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                               {{ old('payment_method') === 'crypto' ? 'checked' : '' }}>
                        <label for="crypto" class="ml-3 block text-sm font-medium text-gray-700">
                            Cryptocurrency
                        </label>
                    </div>
                </div>
                @error('payment_method')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Payment Details -->
            <div id="payment-details" class="space-y-4" style="display: none;">
                <!-- Bank Transfer Details -->
                <div id="bank-details" class="space-y-4" style="display: none;">
                    <div>
                        <label for="account_name" class="block text-sm font-medium text-gray-700">Account Holder Name</label>
                        <input type="text" 
                               name="payment_details[account_name]" 
                               id="account_name"
                               value="{{ old('payment_details.account_name') }}"
                               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="account_number" class="block text-sm font-medium text-gray-700">Account Number</label>
                        <input type="text" 
                               name="payment_details[account_number]" 
                               id="account_number"
                               value="{{ old('payment_details.account_number') }}"
                               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="bank_name" class="block text-sm font-medium text-gray-700">Bank Name</label>
                        <input type="text" 
                               name="payment_details[bank_name]" 
                               id="bank_name"
                               value="{{ old('payment_details.bank_name') }}"
                               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="routing_number" class="block text-sm font-medium text-gray-700">Routing Number (Optional)</label>
                        <input type="text" 
                               name="payment_details[routing_number]" 
                               id="routing_number"
                               value="{{ old('payment_details.routing_number') }}"
                               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>

                <!-- PayPal Details -->
                <div id="paypal-details" class="space-y-4" style="display: none;">
                    <div>
                        <label for="paypal_email" class="block text-sm font-medium text-gray-700">PayPal Email Address</label>
                        <input type="email" 
                               name="payment_details[paypal_email]" 
                               id="paypal_email"
                               value="{{ old('payment_details.paypal_email') }}"
                               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>

                <!-- Crypto Details -->
                <div id="crypto-details" class="space-y-4" style="display: none;">
                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700">Cryptocurrency</label>
                        <select name="payment_details[currency]" 
                                id="currency"
                                class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select cryptocurrency</option>
                            <option value="BTC" {{ old('payment_details.currency') === 'BTC' ? 'selected' : '' }}>Bitcoin (BTC)</option>
                            <option value="ETH" {{ old('payment_details.currency') === 'ETH' ? 'selected' : '' }}>Ethereum (ETH)</option>
                            <option value="USDT" {{ old('payment_details.currency') === 'USDT' ? 'selected' : '' }}>Tether (USDT)</option>
                        </select>
                    </div>
                    <div>
                        <label for="wallet_address" class="block text-sm font-medium text-gray-700">Wallet Address</label>
                        <input type="text" 
                               name="payment_details[wallet_address]" 
                               id="wallet_address"
                               value="{{ old('payment_details.wallet_address') }}"
                               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>
            </div>

            <!-- Terms and Processing Info -->
            <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Processing Information</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Withdrawals are processed within 3-5 business days</li>
                    <li>• Minimum withdrawal amount: ${{ number_format($minWithdrawal, 2) }}</li>
                    <li>• Processing fees may apply depending on payment method</li>
                    <li>• All withdrawal requests are reviewed for security</li>
                </ul>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-3">
                <a href="{{ route('wallet.index') }}"
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </a>
                <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Request Withdrawal
                </button>
            </div>
        </form>
    </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const paymentDetails = document.getElementById('payment-details');
    const bankDetails = document.getElementById('bank-details');
    const paypalDetails = document.getElementById('paypal-details');
    const cryptoDetails = document.getElementById('crypto-details');

    paymentMethods.forEach(function(method) {
        method.addEventListener('change', function() {
            // Hide all details first
            bankDetails.style.display = 'none';
            paypalDetails.style.display = 'none';
            cryptoDetails.style.display = 'none';
            
            // Show payment details section
            paymentDetails.style.display = 'block';
            
            // Show relevant details based on selection
            switch(this.value) {
                case 'bank_transfer':
                    bankDetails.style.display = 'block';
                    break;
                case 'paypal':
                    paypalDetails.style.display = 'block';
                    break;
                case 'crypto':
                    cryptoDetails.style.display = 'block';
                    break;
            }
        });
    });

    // Trigger change event if a method is already selected (for form validation errors)
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
    if (selectedMethod) {
        selectedMethod.dispatchEvent(new Event('change'));
    }
});
</script>
@endpush
@endsection
