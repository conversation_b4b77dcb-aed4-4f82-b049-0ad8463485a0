<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Referral extends Model
{
    use HasFactory;

    protected $fillable = [
        'referrer_id',
        'referred_id',
        'status',
        'activated_at',
    ];

    protected function casts(): array
    {
        return [
            'activated_at' => 'datetime',
        ];
    }

    /**
     * Get the user who made the referral.
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Get the user who was referred.
     */
    public function referred()
    {
        return $this->belongsTo(User::class, 'referred_id');
    }

    /**
     * Get commissions generated from this referral.
     */
    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }

    /**
     * Activate the referral.
     */
    public function activate(): bool
    {
        if ($this->status === 'active') {
            return true;
        }

        $this->status = 'active';
        $this->activated_at = now();
        $this->save();

        // Update referrer's total referrals count
        $referrer = $this->referrer;
        $referrer->total_referrals = $referrer->activeReferrals()->count();
        $referrer->save();

        // Check if referrer can upgrade stage
        $referrer->upgradeStage();

        // Generate commission for the referrer
        $this->generateCommission();

        return true;
    }

    /**
     * Generate commission for the referrer based on their activated stages.
     * Note: In multi-stage system, commissions are handled by stage activations.
     */
    private function generateCommission(): void
    {
        // Multi-stage system: Commissions are now handled when users activate stages
        // This method is kept for backward compatibility but doesn't generate commissions
        // See UserStageActivation::awardReferrerCommissionForActivation() for new logic
        return;
    }
}
