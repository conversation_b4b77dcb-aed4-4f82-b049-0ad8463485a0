<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('membership_stages', function (Blueprint $table) {
            $table->integer('min_years')->nullable()->after('max_referrals');
            $table->integer('max_years')->nullable()->after('min_years');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('membership_stages', function (Blueprint $table) {
            $table->dropColumn(['min_years', 'max_years']);
        });
    }
};
