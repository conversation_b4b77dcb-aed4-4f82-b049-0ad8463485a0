<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserActivityCompletion extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'activity_id',
        'started_at',
        'completed_at',
        'status',
        'notes',
        'submission_data',
        'points_earned',
        'verified_at',
        'verified_by',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'verified_at' => 'datetime',
        'submission_data' => 'array',
    ];

    /**
     * Status options
     */
    const STATUSES = [
        'not_started' => 'Not Started',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'verified' => 'Verified',
    ];

    /**
     * Get the user that owns the completion.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the activity that owns the completion.
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class);
    }

    /**
     * Get the user who verified this completion.
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope for completed activities.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for verified activities.
     */
    public function scopeVerified($query)
    {
        return $query->where('status', 'verified');
    }

    /**
     * Scope for in progress activities.
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Get the status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return self::STATUSES[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the status color class.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'not_started' => 'bg-gray-100 text-gray-800',
            'in_progress' => 'bg-blue-100 text-blue-800',
            'completed' => 'bg-green-100 text-green-800',
            'verified' => 'bg-purple-100 text-purple-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Mark activity as started.
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'in_progress',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark activity as completed.
     */
    public function markAsCompleted(string $notes = null, array $submissionData = []): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'notes' => $notes,
            'submission_data' => $submissionData,
            'points_earned' => $this->activity->points,
        ]);
    }

    /**
     * Mark activity as verified.
     */
    public function markAsVerified(User $verifier): void
    {
        $this->update([
            'status' => 'verified',
            'verified_at' => now(),
            'verified_by' => $verifier->id,
        ]);
    }

    /**
     * Get completion percentage.
     */
    public function getCompletionPercentage(): int
    {
        return match ($this->status) {
            'not_started' => 0,
            'in_progress' => 50,
            'completed' => 100,
            'verified' => 100,
            default => 0,
        };
    }

    /**
     * Check if activity is completed.
     */
    public function isCompleted(): bool
    {
        return in_array($this->status, ['completed', 'verified']);
    }

    /**
     * Check if activity is verified.
     */
    public function isVerified(): bool
    {
        return $this->status === 'verified';
    }

    /**
     * Get duration in human readable format.
     */
    public function getDurationAttribute(): ?string
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        $duration = $this->completed_at->diffInMinutes($this->started_at);
        
        if ($duration < 60) {
            return $duration . ' minutes';
        } elseif ($duration < 1440) {
            return round($duration / 60, 1) . ' hours';
        } else {
            return round($duration / 1440, 1) . ' days';
        }
    }
}
