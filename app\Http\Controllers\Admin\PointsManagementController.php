<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\UserPoint;
use App\Models\PointRequest;
use App\Models\SpecialEvent;
use Carbon\Carbon;

class PointsManagementController extends Controller
{

    /**
     * Get points statistics
     */
    public function getPointsStats()
    {
        try {
            $stats = [
                'total_points_issued' => UserPoint::sum('points'),
                'total_points_redeemed' => 0, // Placeholder - implement based on your redemption system
                'total_points_shared' => 0, // Placeholder - implement based on your sharing system
                'pending_requests' => PointRequest::where('status', 'pending')->count(),
                'approved_requests_today' => PointRequest::where('status', 'approved')
                    ->whereDate('updated_at', Carbon::today())->count(),
                'active_sharers' => User::whereHas('userPoints')->count(),
                'points_shared_today' => 0, // Placeholder
                'average_daily_sharing' => 0, // Placeholder
            ];

            return response()->json(['success' => true, 'data' => $stats]);
        } catch (\Exception $e) {
            Log::error('Error fetching points stats: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching points statistics']);
        }
    }

    /**
     * Get pending point requests
     */
    public function getPendingRequests()
    {
        try {
            $requests = PointRequest::with(['user', 'approvedBy'])
                ->where('status', 'pending')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json(['success' => true, 'data' => $requests]);
        } catch (\Exception $e) {
            Log::error('Error fetching pending requests: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching pending requests']);
        }
    }

    /**
     * Approve point request
     */
    public function approveRequest(Request $request, $requestId)
    {
        try {
            DB::beginTransaction();

            $pointRequest = PointRequest::findOrFail($requestId);
            
            if ($pointRequest->status !== 'pending') {
                return response()->json(['success' => false, 'message' => 'Request already processed']);
            }

            // Update request status
            $pointRequest->update([
                'status' => 'approved',
                'approved_by' => Auth::id(),
                'approved_at' => now(),
                'admin_notes' => $request->admin_notes,
            ]);

            // Award points to user
            $pointRequest->user->increment('total_points', $pointRequest->amount);

            // Create or update user points
            UserPoint::updateOrCreate(
                ['user_id' => $pointRequest->user_id],
                ['points' => DB::raw('points + ' . $pointRequest->amount)]
            );

            DB::commit();

            Log::channel('admin')->info('Point request approved', [
                'admin_id' => Auth::id(),
                'request_id' => $requestId,
                'user_id' => $pointRequest->user_id,
                'amount' => $pointRequest->amount,
            ]);

            return response()->json(['success' => true, 'message' => 'Request approved successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error approving request: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error approving request']);
        }
    }

    /**
     * Reject point request
     */
    public function rejectRequest(Request $request, $requestId)
    {
        try {
            $pointRequest = PointRequest::findOrFail($requestId);
            
            if ($pointRequest->status !== 'pending') {
                return response()->json(['success' => false, 'message' => 'Request already processed']);
            }

            $pointRequest->update([
                'status' => 'rejected',
                'approved_by' => auth()->id(),
                'approved_at' => now(),
                'admin_notes' => $request->admin_notes,
            ]);

            Log::channel('admin')->info('Point request rejected', [
                'admin_id' => auth()->id(),
                'request_id' => $requestId,
                'user_id' => $pointRequest->user_id,
                'reason' => $request->admin_notes,
            ]);

            return response()->json(['success' => true, 'message' => 'Request rejected successfully']);
        } catch (\Exception $e) {
            Log::error('Error rejecting request: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error rejecting request']);
        }
    }

    /**
     * Approve all pending requests
     */
    public function approveAllRequests()
    {
        try {
            DB::beginTransaction();

            $pendingRequests = PointRequest::where('status', 'pending')->get();
            $totalApproved = 0;
            $totalPoints = 0;

            foreach ($pendingRequests as $pointRequest) {
                // Update request status
                $pointRequest->update([
                    'status' => 'approved',
                    'approved_by' => auth()->id(),
                    'approved_at' => now(),
                    'admin_notes' => 'Bulk approval by admin',
                ]);

                // Award points to user
                $pointRequest->user->increment('total_points', $pointRequest->amount);

                // Create point transaction
                PointTransaction::create([
                    'user_id' => $pointRequest->user_id,
                    'type' => 'admin_award',
                    'amount' => $pointRequest->amount,
                    'description' => "Admin bulk approval: {$pointRequest->point_type}",
                    'reference_id' => $pointRequest->id,
                    'reference_type' => 'point_request',
                ]);

                $totalApproved++;
                $totalPoints += $pointRequest->amount;
            }

            DB::commit();

            Log::channel('admin')->info('Bulk point requests approved', [
                'admin_id' => auth()->id(),
                'total_approved' => $totalApproved,
                'total_points' => $totalPoints,
            ]);

            return response()->json([
                'success' => true, 
                'message' => "Successfully approved {$totalApproved} requests totaling {$totalPoints} points"
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error bulk approving requests: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error bulk approving requests']);
        }
    }

    /**
     * Get point sharing activity
     */
    public function getSharingActivity()
    {
        try {
            $activity = PointTransaction::with(['user', 'recipient'])
                ->where('type', 'shared')
                ->orderBy('created_at', 'desc')
                ->limit(50)
                ->get();

            return response()->json(['success' => true, 'data' => $activity]);
        } catch (\Exception $e) {
            Log::error('Error fetching sharing activity: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching sharing activity']);
        }
    }

    /**
     * Create special event
     */
    public function createSpecialEvent(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'required|string',
                'point_type' => 'required|string',
                'total_points' => 'required|integer|min:1',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after:start_date',
                'max_per_user' => 'nullable|integer|min:1',
                'eligibility_criteria' => 'nullable|string',
            ]);

            $event = SpecialEvent::create([
                ...$validated,
                'created_by' => auth()->id(),
                'status' => 'active',
            ]);

            Log::channel('admin')->info('Special event created', [
                'admin_id' => auth()->id(),
                'event_id' => $event->id,
                'event_name' => $event->name,
            ]);

            return response()->json(['success' => true, 'message' => 'Special event created successfully']);
        } catch (\Exception $e) {
            Log::error('Error creating special event: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error creating special event']);
        }
    }

    /**
     * Get special events
     */
    public function getSpecialEvents()
    {
        try {
            $events = SpecialEvent::with('createdBy')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json(['success' => true, 'data' => $events]);
        } catch (\Exception $e) {
            Log::error('Error fetching special events: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching special events']);
        }
    }

    /**
     * Update point settings
     */
    public function updatePointSettings(Request $request)
    {
        try {
            $settings = $request->validate([
                'daily_sharing_limit' => 'required|integer|min:0',
                'minimum_share_amount' => 'required|integer|min:1',
                'maximum_request_amount' => 'required|integer|min:1',
                'require_admin_approval' => 'boolean',
                'light_members_only' => 'boolean',
                'enabled_point_types' => 'array',
            ]);

            foreach ($settings as $key => $value) {
                if ($key === 'enabled_point_types') {
                    $value = json_encode($value);
                }
                
                DB::table('settings')->updateOrInsert(
                    ['key' => "points.{$key}"],
                    ['value' => $value, 'updated_at' => now()]
                );
            }

            Log::channel('admin')->info('Point settings updated', [
                'admin_id' => auth()->id(),
                'settings' => $settings,
            ]);

            return response()->json(['success' => true, 'message' => 'Point settings updated successfully']);
        } catch (\Exception $e) {
            Log::error('Error updating point settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating point settings']);
        }
    }

    /**
     * Award points to user
     */
    public function awardPoints(Request $request)
    {
        try {
            $validated = $request->validate([
                'user_id' => 'required|exists:users,id',
                'amount' => 'required|integer|min:1',
                'reason' => 'required|string|max:255',
                'point_type' => 'required|string',
            ]);

            DB::beginTransaction();

            $user = User::findOrFail($validated['user_id']);
            $user->increment('total_points', $validated['amount']);

            PointTransaction::create([
                'user_id' => $validated['user_id'],
                'type' => 'admin_award',
                'amount' => $validated['amount'],
                'description' => $validated['reason'],
                'metadata' => json_encode(['point_type' => $validated['point_type']]),
            ]);

            DB::commit();

            Log::channel('admin')->info('Points awarded by admin', [
                'admin_id' => auth()->id(),
                'user_id' => $validated['user_id'],
                'amount' => $validated['amount'],
                'reason' => $validated['reason'],
            ]);

            return response()->json(['success' => true, 'message' => 'Points awarded successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error awarding points: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error awarding points']);
        }
    }
}
