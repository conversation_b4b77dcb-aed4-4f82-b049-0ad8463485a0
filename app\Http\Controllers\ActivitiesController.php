<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\ActivityResource;
use App\Models\MembershipStage;
use App\Models\UserActivityCompletion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ActivitiesController extends Controller
{
    /**
     * Display activities for a specific stage.
     */
    public function index(Request $request, MembershipStage $stage)
    {
        $user = Auth::user();

        // Check if user has access to this stage
        // For now, allow access to all stages for testing
        // TODO: Implement proper stage activation checking
        $hasAccess = true; // Temporary - allow all access

        if (!$hasAccess) {
            abort(403, 'You do not have access to this stage.');
        }

        $type = $request->get('type', 'all');
        
        $activitiesQuery = Activity::byStage($stage->id)
            ->active()
            ->ordered();

        if ($type !== 'all') {
            $activitiesQuery->byType($type);
        }

        $activities = $activitiesQuery->get();

        // Get user's completions for these activities
        $completions = UserActivityCompletion::where('user_id', $user->id)
            ->whereIn('activity_id', $activities->pluck('id'))
            ->get()
            ->keyBy('activity_id');

        $activityTypes = Activity::TYPES;

        return view('stages.activities.index', compact('stage', 'activities', 'completions', 'type', 'activityTypes'));
    }

    /**
     * Show a specific activity.
     */
    public function show(MembershipStage $stage, Activity $activity)
    {
        $user = Auth::user();

        // Check if user has access to this stage
        // For now, allow access to all stages for testing
        // TODO: Implement proper stage activation checking
        $hasAccess = true; // Temporary - allow all access

        if (!$hasAccess) {
            abort(403, 'You do not have access to this stage.');
        }

        // Check if activity belongs to this stage
        if ($activity->stage_id !== $stage->id) {
            abort(404, 'Activity not found in this stage.');
        }

        // Get user's completion for this activity
        $completion = UserActivityCompletion::where('user_id', $user->id)
            ->where('activity_id', $activity->id)
            ->first();

        // Get activity resources
        $resources = $activity->resources()->ordered()->get();

        return view('stages.activities.show', compact('stage', 'activity', 'completion', 'resources'));
    }

    /**
     * Start an activity.
     */
    public function start(Request $request, MembershipStage $stage, Activity $activity)
    {
        $user = Auth::user();

        // Check if user has access to this stage
        // For now, allow access to all stages for testing
        // TODO: Implement proper stage activation checking
        $hasAccess = true; // Temporary - allow all access

        if (!$hasAccess) {
            abort(403, 'You do not have access to this stage.');
        }

        // Check if activity belongs to this stage
        if ($activity->stage_id !== $stage->id) {
            abort(404, 'Activity not found in this stage.');
        }

        // Get or create completion record
        $completion = UserActivityCompletion::firstOrCreate([
            'user_id' => $user->id,
            'activity_id' => $activity->id,
        ]);

        // Mark as started if not already started
        if ($completion->status === 'not_started') {
            $completion->markAsStarted();
        }

        return redirect()->route('stages.activities.show', [$stage, $activity])
            ->with('success', 'Activity started successfully!');
    }

    /**
     * Complete an activity.
     */
    public function complete(Request $request, MembershipStage $stage, Activity $activity)
    {
        $user = Auth::user();

        // Temporarily disable access check for testing
        // TODO: Re-enable after fixing stage activation system
        /*
        // Check if user has access to this stage
        if (!$user->hasAccessToStage($stage)) {
            abort(403, 'You do not have access to this stage.');
        }
        */

        // Check if activity belongs to this stage
        if ($activity->stage_id !== $stage->id) {
            abort(404, 'Activity not found in this stage.');
        }

        $request->validate([
            'notes' => 'nullable|string|max:1000',
            'submission_data' => 'nullable|array',
        ]);

        // Get completion record
        $completion = UserActivityCompletion::where('user_id', $user->id)
            ->where('activity_id', $activity->id)
            ->first();

        if (!$completion) {
            return back()->with('error', 'You must start the activity first.');
        }

        if ($completion->isCompleted()) {
            return back()->with('error', 'Activity is already completed.');
        }

        // Mark as completed
        $completion->markAsCompleted(
            $request->input('notes'),
            $request->input('submission_data', [])
        );

        return redirect()->route('stages.activities.show', [$stage, $activity])
            ->with('success', 'Activity completed successfully! You earned ' . $activity->points . ' points.');
    }

    /**
     * Show activities by type.
     */
    public function byType(MembershipStage $stage, string $type)
    {
        $user = Auth::user();

        // Temporarily disable access check for testing
        // TODO: Re-enable after fixing stage activation system
        /*
        // Check if user has access to this stage
        if (!$user->hasAccessToStage($stage)) {
            abort(403, 'You do not have access to this stage.');
        }
        */

        // Validate activity type
        if (!array_key_exists($type, Activity::TYPES)) {
            abort(404, 'Invalid activity type.');
        }

        $activities = Activity::byStage($stage->id)
            ->byType($type)
            ->active()
            ->ordered()
            ->get();

        // Get user's completions for these activities
        $completions = UserActivityCompletion::where('user_id', $user->id)
            ->whereIn('activity_id', $activities->pluck('id'))
            ->get()
            ->keyBy('activity_id');

        $typeDisplay = Activity::TYPES[$type];

        return view('stages.activities.by-type', compact('stage', 'activities', 'completions', 'type', 'typeDisplay'));
    }

    /**
     * Get user's activity progress.
     */
    public function progress(MembershipStage $stage)
    {
        $user = Auth::user();

        // Temporarily disable access check for testing
        // TODO: Re-enable after fixing stage activation system
        /*
        // Check if user has access to this stage
        if (!$user->hasAccessToStage($stage)) {
            abort(403, 'You do not have access to this stage.');
        }
        */

        $activities = Activity::byStage($stage->id)->active()->get();
        $completions = UserActivityCompletion::where('user_id', $user->id)
            ->whereIn('activity_id', $activities->pluck('id'))
            ->get()
            ->keyBy('activity_id');

        $progress = [];
        foreach (Activity::TYPES as $type => $typeDisplay) {
            $typeActivities = $activities->where('type', $type);
            $typeCompletions = $completions->whereIn('activity_id', $typeActivities->pluck('id'));
            $completedCount = $typeCompletions->where('status', 'completed')->count() + 
                            $typeCompletions->where('status', 'verified')->count();

            $progress[$type] = [
                'display' => $typeDisplay,
                'total' => $typeActivities->count(),
                'completed' => $completedCount,
                'percentage' => $typeActivities->count() > 0 ? round(($completedCount / $typeActivities->count()) * 100) : 0,
                'points_earned' => $typeCompletions->sum('points_earned'),
                'total_points' => $typeActivities->sum('points'),
            ];
        }

        return view('stages.activities.progress', compact('stage', 'progress'));
    }

    /**
     * Download activity resource.
     */
    public function downloadResource(ActivityResource $resource)
    {
        $user = Auth::user();

        // Temporarily disable access check for testing
        // TODO: Re-enable after fixing stage activation system
        /*
        // Check if user has access to the stage this activity belongs to
        if (!$user->hasAccessToStage($resource->activity->stage)) {
            abort(403, 'You do not have access to this resource.');
        }
        */

        if ($resource->type === 'link' && $resource->url) {
            return redirect($resource->url);
        }

        if ($resource->file_path && file_exists(storage_path('app/' . $resource->file_path))) {
            return response()->download(
                storage_path('app/' . $resource->file_path),
                $resource->title . '.' . pathinfo($resource->file_path, PATHINFO_EXTENSION)
            );
        }

        abort(404, 'Resource not found.');
    }
}
