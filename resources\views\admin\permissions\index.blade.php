@extends('layouts.admin')

@section('title', 'Admin Permissions')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Admin Permission Management</h1>
        <p class="mt-2 text-gray-600">Assign specific privileges to admin users</p>
        @if(!auth()->user()->is_top_admin)
        <div class="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L10 11.414l2.707-2.707a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">
                        <strong>Access Denied:</strong> Only the Top Administrator can manage admin permissions.
                    </p>
                </div>
            </div>
        </div>
        @endif
    </div>

    @if(auth()->user()->is_top_admin)
    <!-- Admin Users List -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Admin Users & Permissions</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Permissions</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($adminUsers as $admin)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-10 w-10 flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">{{ substr($admin->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ $admin->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $admin->email }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($admin->is_top_admin)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Top Admin
                            </span>
                            @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Admin
                            </span>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900">
                                @if($admin->is_top_admin)
                                <span class="text-yellow-600 font-medium">All Permissions (Top Admin)</span>
                                @else
                                @php
                                    $permissions = $admin->getAdminPermissions();
                                    $activePermissions = array_filter($permissions);
                                @endphp
                                <div class="flex flex-wrap gap-1">
                                    @foreach($activePermissions as $permission => $value)
                                    @if($value)
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        {{ ucwords(str_replace('_', ' ', $permission)) }}
                                    </span>
                                    @endif
                                    @endforeach
                                </div>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            @if(!$admin->is_top_admin)
                            <button type="button" onclick="editPermissions({{ $admin->id }}, '{{ $admin->name }}', {{ json_encode($admin->getAdminPermissions()) }})" 
                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-indigo-600 hover:bg-indigo-700">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                </svg>
                                Edit Permissions
                            </button>
                            @else
                            <span class="text-sm text-gray-500">Protected Account</span>
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Permission Definitions -->
    <div class="mt-8 bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Permission Definitions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-2">Standard Admin Permissions</h4>
                <div class="space-y-2 text-sm text-gray-600">
                    <div>• <strong>User Management:</strong> View, activate/deactivate users</div>
                    <div>• <strong>Currency Management:</strong> Update exchange rates, toggle currencies</div>
                    <div>• <strong>Language Management:</strong> Enable/disable languages</div>
                    <div>• <strong>Financial Overview:</strong> View revenue and transaction data</div>
                    <div>• <strong>Approval Management:</strong> Approve/reject users, activations, commissions</div>
                    <div>• <strong>Bulk Operations:</strong> Mass user operations</div>
                    <div>• <strong>Export Data:</strong> Download user and financial reports</div>
                </div>
            </div>
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-2">Top Admin Exclusive Permissions</h4>
                <div class="space-y-2 text-sm text-gray-600">
                    <div>• <strong>Stage Pricing:</strong> Modify activation prices and commission rates</div>
                    <div>• <strong>Admin Approval:</strong> Approve/reject admin requests</div>
                    <div>• <strong>System Settings:</strong> Access to system configuration</div>
                    <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <strong class="text-yellow-800">Note:</strong> Top Admin permissions cannot be assigned to other admins and are exclusive to the Top Administrator.
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Edit Permissions Modal -->
<div id="permissionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Edit Admin Permissions</h3>
            <form id="permissionsForm" method="POST">
                @csrf
                @method('PUT')
                <div class="mt-4 space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" id="user_management" name="permissions[user_management]" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <label for="user_management" class="ml-2 text-sm text-gray-700">User Management</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="currency_management" name="permissions[currency_management]" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <label for="currency_management" class="ml-2 text-sm text-gray-700">Currency Management</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="language_management" name="permissions[language_management]" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <label for="language_management" class="ml-2 text-sm text-gray-700">Language Management</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="financial_overview" name="permissions[financial_overview]" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <label for="financial_overview" class="ml-2 text-sm text-gray-700">Financial Overview</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="approval_management" name="permissions[approval_management]" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <label for="approval_management" class="ml-2 text-sm text-gray-700">Approval Management</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="bulk_operations" name="permissions[bulk_operations]" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <label for="bulk_operations" class="ml-2 text-sm text-gray-700">Bulk Operations</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="export_data" name="permissions[export_data]" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <label for="export_data" class="ml-2 text-sm text-gray-700">Export Data</label>
                    </div>
                </div>
                <div class="flex items-center justify-between mt-6">
                    <button type="button" onclick="hidePermissionsModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Update Permissions
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPermissions(userId, userName, currentPermissions) {
    document.getElementById('modalTitle').textContent = `Edit Permissions for ${userName}`;
    document.getElementById('permissionsForm').action = `/admin/permissions/${userId}`;
    
    // Reset all checkboxes
    const checkboxes = document.querySelectorAll('#permissionsForm input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
    
    // Set current permissions
    Object.keys(currentPermissions).forEach(permission => {
        const checkbox = document.getElementById(permission);
        if (checkbox && currentPermissions[permission]) {
            checkbox.checked = true;
        }
    });
    
    document.getElementById('permissionsModal').classList.remove('hidden');
}

function hidePermissionsModal() {
    document.getElementById('permissionsModal').classList.add('hidden');
}
</script>
@endsection
