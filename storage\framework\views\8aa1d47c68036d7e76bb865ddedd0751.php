<?php $__env->startSection('title', 'Payment Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <!-- Clean Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Payment Management</h1>
                <p class="text-gray-600 mt-1">Manage payment gateways, transactions, and financial settings</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportPaymentData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export Data
                </button>
                <button onclick="savePaymentSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Clean Navigation -->
    <div class="mb-8" x-data="{ activeTab: 'gateways' }">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <nav class="flex space-x-0">
                <button @click="activeTab = 'gateways'" :class="activeTab === 'gateways' ? 'bg-green-50 text-green-700 border-green-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 first:rounded-l-lg transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
                    </svg>
                    Payment Gateways
                </button>
                <button @click="activeTab = 'transactions'" :class="activeTab === 'transactions' ? 'bg-green-50 text-green-700 border-green-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                    </svg>
                    Transactions
                </button>
                <button @click="activeTab = 'methods'" :class="activeTab === 'methods' ? 'bg-green-50 text-green-700 border-green-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"></path>
                    </svg>
                    Payment Methods
                </button>
                <button @click="activeTab = 'security'" :class="activeTab === 'security' ? 'bg-green-50 text-green-700 border-green-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                    Security
                </button>
                <button @click="activeTab = 'reports'" :class="activeTab === 'reports' ? 'bg-green-50 text-green-700 border-green-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium last:rounded-r-lg transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                    </svg>
                    Reports
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div>
            <!-- Payment Gateways Tab -->
            <div x-show="activeTab === 'gateways'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">💳 Payment Gateways</h3>

                <div class="space-y-6">
                    <!-- Gateway Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M13.5 2c-5.621 0-10.211 4.443-10.475 10h3.025c.264-3.292 2.917-5.945 6.209-6.209V2.525C12.259 2.264 12.879 2 13.5 2z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-medium text-gray-900">Stripe</h4>
                                        <p class="text-sm text-gray-500">Credit cards, digital wallets</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Processing Fee:</span>
                                    <span class="text-gray-900">2.9% + $0.30</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Settlement:</span>
                                    <span class="text-gray-900">2 business days</span>
                                </div>
                                <button class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md text-sm hover:bg-indigo-700">Configure</button>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h8.418c.524 0 .972.382 1.054.901l3.107 19.696a.641.641 0 0 1-.633.74h-4.606L7.076 21.337z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-medium text-gray-900">PayPal</h4>
                                        <p class="text-sm text-gray-500">PayPal, credit cards</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Inactive</span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Processing Fee:</span>
                                    <span class="text-gray-900">2.9% + $0.30</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Settlement:</span>
                                    <span class="text-gray-900">1 business day</span>
                                </div>
                                <button class="w-full bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-400">Enable</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transactions Tab -->
            <div x-show="activeTab === 'transactions'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📊 Transaction Management</h3>

                <div class="space-y-6">
                    <!-- Transaction Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                            <div class="text-2xl font-bold">$45,678</div>
                            <div class="text-green-100">Total Revenue</div>
                        </div>
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                            <div class="text-2xl font-bold">1,234</div>
                            <div class="text-blue-100">Transactions</div>
                        </div>
                        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
                            <div class="text-2xl font-bold">$1,234</div>
                            <div class="text-yellow-100">Pending</div>
                        </div>
                        <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-6 text-white">
                            <div class="text-2xl font-bold">23</div>
                            <div class="text-red-100">Failed</div>
                        </div>
                    </div>

                    <!-- Recent Transactions -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Recent Transactions</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#TXN-001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">John Doe</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$299.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods Tab -->
            <div x-show="activeTab === 'methods'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">💰 Payment Methods</h3>

                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="text-sm font-medium text-gray-900">Credit Cards</h5>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                </label>
                            </div>
                            <p class="text-sm text-gray-500">Visa, Mastercard, American Express</p>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="text-sm font-medium text-gray-900">Digital Wallets</h5>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                </label>
                            </div>
                            <p class="text-sm text-gray-500">Apple Pay, Google Pay, PayPal</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div x-show="activeTab === 'security'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🔒 Payment Security</h3>

                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">PCI DSS Compliance</h5>
                                <p class="text-sm text-gray-500">Payment card industry compliance</p>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Compliant</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">SSL Encryption</h5>
                                <p class="text-sm text-gray-500">Secure data transmission</p>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports Tab -->
            <div x-show="activeTab === 'reports'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📈 Payment Reports</h3>

                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <button class="border border-gray-200 rounded-lg p-4 text-left hover:bg-gray-50">
                            <h5 class="text-sm font-medium text-gray-900 mb-2">Daily Revenue Report</h5>
                            <p class="text-sm text-gray-500">Daily transaction summary</p>
                        </button>

                        <button class="border border-gray-200 rounded-lg p-4 text-left hover:bg-gray-50">
                            <h5 class="text-sm font-medium text-gray-900 mb-2">Monthly Statement</h5>
                            <p class="text-sm text-gray-500">Comprehensive monthly report</p>
                        </button>

                        <button class="border border-gray-200 rounded-lg p-4 text-left hover:bg-gray-50">
                            <h5 class="text-sm font-medium text-gray-900 mb-2">Failed Transactions</h5>
                            <p class="text-sm text-gray-500">Analysis of failed payments</p>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Payment Management JavaScript
function exportPaymentData() {
    alert('Exporting payment data...');
}

function savePaymentSettings() {
    alert('Payment settings saved successfully!');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Payment Management loaded');
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/admin/payment-management/index.blade.php ENDPATH**/ ?>