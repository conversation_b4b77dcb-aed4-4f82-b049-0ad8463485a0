@extends('layouts.admin')

@section('title', 'Referral Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Referral Management System</h1>
                <p class="text-gray-600 mt-1">Comprehensive referral tracking, management, and analytics</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportReferralData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Data
                </button>
                <button onclick="bulkReferralActions()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Bulk Actions
                </button>
            </div>
        </div>
    </div>

    <!-- Referral Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">24,847</h3>
                    <p class="text-sm text-gray-600">Total Referrals</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">18,234</h3>
                    <p class="text-sm text-gray-600">Active Referrals</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">3,456</h3>
                    <p class="text-sm text-gray-600">Pending Activations</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">$189,420</h3>
                    <p class="text-sm text-gray-600">Total Earnings</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">$45,890</h3>
                    <p class="text-sm text-gray-600">Withheld Earnings</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'overview' }">
                <button @click="activeTab = 'overview'" :class="activeTab === 'overview' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Referral Overview
                </button>
                <button @click="activeTab = 'stages'" :class="activeTab === 'stages' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Stage Analytics
                </button>
                <button @click="activeTab = 'earnings'" :class="activeTab === 'earnings' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Earnings Management
                </button>
                <button @click="activeTab = 'bonuses'" :class="activeTab === 'bonuses' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Bonus System
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Advanced Analytics
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'overview' }">
        <!-- Referral Overview Tab -->
        <div x-show="activeTab === 'overview'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Referral Management</h3>
                        <div class="flex items-center space-x-3">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">All Referrals</option>
                                <option value="active">Active</option>
                                <option value="pending">Pending Activation</option>
                                <option value="withheld">Withheld Earnings</option>
                                <option value="approved">Approved Earnings</option>
                            </select>
                            <input type="text" placeholder="Search referrals..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" class="rounded border-gray-300">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Referrer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Referred User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activation Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Earning Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-20</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">John Smith</div>
                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Sarah Johnson</div>
                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$125.00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="viewReferral(1)" class="text-indigo-600 hover:text-indigo-900">View</button>
                                        <button onclick="approveEarning(1)" class="text-green-600 hover:text-green-900">Approve</button>
                                        <button onclick="withholdEarning(1)" class="text-red-600 hover:text-red-900">Withhold</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-19</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Mike Davis</div>
                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Alex Thompson</div>
                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                        Withheld
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$0.00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="viewReferral(2)" class="text-indigo-600 hover:text-indigo-900">View</button>
                                        <button onclick="activateReferral(2)" class="text-green-600 hover:text-green-900">Activate</button>
                                        <button onclick="rejectReferral(2)" class="text-red-600 hover:text-red-900">Reject</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Stage Analytics Tab -->
        <div x-show="activeTab === 'stages'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Referral Analytics by Stage</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Stage 1 -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 1</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    247 Referrals
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Active Referrals:</span>
                                    <span class="font-medium text-green-600">189</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Pending Activations:</span>
                                    <span class="font-medium text-yellow-600">58</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Total Earnings:</span>
                                    <span class="font-medium text-gray-900">$12,450.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withheld Earnings:</span>
                                    <span class="font-medium text-red-600">$2,890.00</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="viewStageDetails(1)" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    View Details
                                </button>
                            </div>
                        </div>

                        <!-- Stage 2 -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 2</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    156 Referrals
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Active Referrals:</span>
                                    <span class="font-medium text-green-600">123</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Pending Activations:</span>
                                    <span class="font-medium text-yellow-600">33</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Total Earnings:</span>
                                    <span class="font-medium text-gray-900">$8,750.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withheld Earnings:</span>
                                    <span class="font-medium text-red-600">$1,650.00</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="viewStageDetails(2)" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    View Details
                                </button>
                            </div>
                        </div>

                        <!-- Stage 3 -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 3</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    89 Referrals
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Active Referrals:</span>
                                    <span class="font-medium text-green-600">67</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Pending Activations:</span>
                                    <span class="font-medium text-yellow-600">22</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Total Earnings:</span>
                                    <span class="font-medium text-gray-900">$5,340.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withheld Earnings:</span>
                                    <span class="font-medium text-red-600">$1,100.00</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="viewStageDetails(3)" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    View Details
                                </button>
                            </div>
                        </div>

                        <!-- Stage 4 -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 4</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    67 Referrals
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Active Referrals:</span>
                                    <span class="font-medium text-green-600">45</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Pending Activations:</span>
                                    <span class="font-medium text-yellow-600">22</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Total Earnings:</span>
                                    <span class="font-medium text-gray-900">$4,020.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withheld Earnings:</span>
                                    <span class="font-medium text-red-600">$1,320.00</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="viewStageDetails(4)" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    View Details
                                </button>
                            </div>
                        </div>

                        <!-- Stage 5 -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 5</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    34 Referrals
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Active Referrals:</span>
                                    <span class="font-medium text-green-600">23</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Pending Activations:</span>
                                    <span class="font-medium text-yellow-600">11</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Total Earnings:</span>
                                    <span class="font-medium text-gray-900">$2,040.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withheld Earnings:</span>
                                    <span class="font-medium text-red-600">$660.00</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="viewStageDetails(5)" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    View Details
                                </button>
                            </div>
                        </div>

                        <!-- Stage 6 -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 6</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    12 Referrals
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Active Referrals:</span>
                                    <span class="font-medium text-green-600">8</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Pending Activations:</span>
                                    <span class="font-medium text-yellow-600">4</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Total Earnings:</span>
                                    <span class="font-medium text-gray-900">$720.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withheld Earnings:</span>
                                    <span class="font-medium text-red-600">$240.00</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="viewStageDetails(6)" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    View Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Earnings Management Tab -->
        <div x-show="activeTab === 'earnings'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Earnings Management</h3>
                        <div class="flex items-center space-x-3">
                            <button onclick="bulkApproveEarnings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                Bulk Approve
                            </button>
                            <button onclick="bulkWithholdEarnings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                Bulk Withhold
                            </button>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Pending Earnings -->
                        <div class="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Pending Earnings Review</h4>
                                    <p class="text-sm text-gray-600">247 earnings pending approval - Total: $12,450.00</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="reviewPendingEarnings()" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">Review All</button>
                                </div>
                            </div>
                        </div>

                        <!-- Withheld Earnings -->
                        <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Withheld Earnings</h4>
                                    <p class="text-sm text-gray-600">89 earnings currently withheld - Total: $45,890.00</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="reviewWithheldEarnings()" class="text-red-600 hover:text-red-900 text-sm font-medium">Review All</button>
                                </div>
                            </div>
                        </div>

                        <!-- Auto-Release Settings -->
                        <div class="mt-6 border border-green-200 rounded-lg p-6 bg-green-50">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="ml-3 flex-1">
                                    <h3 class="text-lg font-medium text-green-800">Auto-Release Earnings System</h3>
                                    <div class="mt-2 text-sm text-green-700">
                                        <p class="mb-3">Automatically releases withheld earnings when sponsors activate the same stage as their referrals. This ensures fair compensation and encourages stage progression.</p>

                                        <div class="bg-white rounded-lg p-4 border border-green-200">
                                            <h4 class="font-medium text-green-800 mb-3">How Auto-Release Works:</h4>
                                            <ul class="space-y-2 text-sm text-green-700">
                                                <li class="flex items-start">
                                                    <span class="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                                    <span>When a user activates a stage, system checks their referrals' activations</span>
                                                </li>
                                                <li class="flex items-start">
                                                    <span class="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                                    <span>If sponsor has same or higher stage activation, earnings are released</span>
                                                </li>
                                                <li class="flex items-start">
                                                    <span class="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                                    <span>Automatic processing occurs within 5 minutes of stage activation</span>
                                                </li>
                                                <li class="flex items-start">
                                                    <span class="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                                    <span>Email notifications sent to both sponsor and referral</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="mt-4 flex items-center justify-between">
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300" checked onchange="toggleAutoRelease()">
                                            <span class="ml-2 text-sm font-medium text-green-700">Enable auto-release of withheld earnings</span>
                                        </label>
                                        <button onclick="viewAutoReleaseLog()" class="text-sm text-green-600 hover:text-green-800 font-medium">
                                            View Release Log
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Auto-Release Statistics -->
                        <div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="text-2xl font-bold text-green-600">1,247</div>
                                <div class="text-sm text-gray-600">Auto-Released Today</div>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="text-2xl font-bold text-blue-600">$18,450</div>
                                <div class="text-sm text-gray-600">Total Released</div>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="text-2xl font-bold text-yellow-600">456</div>
                                <div class="text-sm text-gray-600">Pending Release</div>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="text-2xl font-bold text-purple-600">98.5%</div>
                                <div class="text-sm text-gray-600">Success Rate</div>
                            </div>
                        </div>

                        <!-- Recent Auto-Releases -->
                        <div class="mt-6">
                            <h5 class="text-md font-medium text-gray-900 mb-4">Recent Auto-Releases</h5>
                            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sponsor</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Referral</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Stage</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-3 text-sm text-gray-900">14:35:22</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">John Smith</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">Sarah Johnson</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">Stage 3</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">$25.00</td>
                                            <td class="px-4 py-3">
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                    Released
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-3 text-sm text-gray-900">14:32:15</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">Mike Davis</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">Lisa Wilson</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">Stage 2</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">$10.00</td>
                                            <td class="px-4 py-3">
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                    Released
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-3 text-sm text-gray-900">14:28:45</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">Emma Brown</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">David Miller</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">Stage 1</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">$3.75</td>
                                            <td class="px-4 py-3">
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                    Released
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReferralData() {
    alert('Export referral data functionality will be implemented');
}

function bulkReferralActions() {
    alert('Bulk referral actions functionality will be implemented');
}

function viewReferral(id) {
    alert(`View referral ${id} functionality will be implemented`);
}

function approveEarning(id) {
    if (confirm('Approve this earning?')) {
        alert(`Approve earning ${id} functionality will be implemented`);
    }
}

function withholdEarning(id) {
    if (confirm('Withhold this earning?')) {
        alert(`Withhold earning ${id} functionality will be implemented`);
    }
}

function activateReferral(id) {
    if (confirm('Activate this referral?')) {
        alert(`Activate referral ${id} functionality will be implemented`);
    }
}

function rejectReferral(id) {
    if (confirm('Reject this referral?')) {
        alert(`Reject referral ${id} functionality will be implemented`);
    }
}

function viewStageDetails(stage) {
    alert(`View stage ${stage} details functionality will be implemented`);
}

function bulkApproveEarnings() {
    if (confirm('Approve all selected earnings?')) {
        alert('Bulk approve earnings functionality will be implemented');
    }
}

function bulkWithholdEarnings() {
    if (confirm('Withhold all selected earnings?')) {
        alert('Bulk withhold earnings functionality will be implemented');
    }
}

function reviewPendingEarnings() {
    alert('Review pending earnings functionality will be implemented');
}

function reviewWithheldEarnings() {
    alert('Review withheld earnings functionality will be implemented');
}

function toggleAutoRelease() {
    const checkbox = event.target;
    if (checkbox.checked) {
        alert('Auto-release feature enabled. Withheld earnings will be automatically released when sponsors activate the same stage as their referrals.');
    } else {
        if (confirm('Disable auto-release feature? This will require manual approval for all earnings releases.')) {
            alert('Auto-release feature disabled. All earnings will require manual approval.');
        } else {
            checkbox.checked = true;
        }
    }
}

function viewAutoReleaseLog() {
    alert('Auto-release log viewer will be implemented - showing detailed history of all automatic releases');
}
</script>
@endsection
