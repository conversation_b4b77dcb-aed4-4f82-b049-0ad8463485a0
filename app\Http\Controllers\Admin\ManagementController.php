<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\MembershipStage;
use App\Models\Transaction;
use App\Models\Withdrawal;
use App\Models\PointTransaction;
use Carbon\Carbon;

class ManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * User Management System
     */
    public function getUserStats()
    {
        try {
            $stats = [
                'total_users' => User::count(),
                'active_users' => User::where('last_login_at', '>=', Carbon::now()->subDays(30))->count(),
                'new_users_today' => User::whereDate('created_at', Carbon::today())->count(),
                'verified_users' => User::whereNotNull('email_verified_at')->count(),
                'light_members' => User::where('membership_stage', 'light')->count(),
                'earthfriendly_members' => User::where('membership_stage', 'earthfriendly')->count(),
                'banned_users' => User::where('status', 'banned')->count(),
                'pending_verifications' => User::whereNull('email_verified_at')->count(),
            ];

            return response()->json(['success' => true, 'data' => $stats]);
        } catch (\Exception $e) {
            Log::error('Error fetching user stats: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching user statistics']);
        }
    }

    public function getUsers(Request $request)
    {
        try {
            $query = User::query();

            // Apply filters
            if ($request->has('status') && $request->status !== 'all') {
                $query->where('status', $request->status);
            }

            if ($request->has('membership') && $request->membership !== 'all') {
                $query->where('membership_stage', $request->membership);
            }

            if ($request->has('search') && !empty($request->search)) {
                $query->where(function($q) use ($request) {
                    $q->where('first_name', 'like', '%' . $request->search . '%')
                      ->orWhere('last_name', 'like', '%' . $request->search . '%')
                      ->orWhere('email', 'like', '%' . $request->search . '%');
                });
            }

            $users = $query->with(['referrals', 'activations'])
                          ->orderBy('created_at', 'desc')
                          ->paginate(20);

            return response()->json(['success' => true, 'data' => $users]);
        } catch (\Exception $e) {
            Log::error('Error fetching users: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching users']);
        }
    }

    public function updateUserStatus(Request $request, $userId)
    {
        try {
            $user = User::findOrFail($userId);
            $user->status = $request->status;
            $user->save();

            Log::channel('admin')->info('User status updated', [
                'admin_id' => auth()->id(),
                'user_id' => $userId,
                'new_status' => $request->status,
            ]);

            return response()->json(['success' => true, 'message' => 'User status updated successfully']);
        } catch (\Exception $e) {
            Log::error('Error updating user status: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating user status']);
        }
    }

    /**
     * Financial Management System
     */
    public function getFinancialStats()
    {
        try {
            $stats = [
                'total_revenue' => Transaction::where('type', 'deposit')->sum('amount'),
                'total_withdrawals' => Withdrawal::where('status', 'completed')->sum('amount'),
                'pending_withdrawals' => Withdrawal::where('status', 'pending')->sum('amount'),
                'total_commissions' => Transaction::where('type', 'commission')->sum('amount'),
                'admin_earnings' => Transaction::where('type', 'admin_fee')->sum('amount'),
                'platform_balance' => $this->calculatePlatformBalance(),
                'monthly_revenue' => Transaction::where('type', 'deposit')
                    ->whereMonth('created_at', Carbon::now()->month)
                    ->sum('amount'),
                'daily_transactions' => Transaction::whereDate('created_at', Carbon::today())->count(),
            ];

            return response()->json(['success' => true, 'data' => $stats]);
        } catch (\Exception $e) {
            Log::error('Error fetching financial stats: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching financial statistics']);
        }
    }

    public function calculateAdminEarnings(Request $request)
    {
        try {
            $stage = $request->stage;
            $referrals = $request->referrals;
            $adminCommission = $request->admin_commission;

            $stagePrices = [1 => 25, 2 => 50, 3 => 100, 4 => 200, 5 => 500, 6 => 1000];
            $stageCommissions = [1 => 15, 2 => 20, 3 => 25, 4 => 30, 5 => 35, 6 => 40];

            $stagePrice = $stagePrices[$stage];
            $userCommissionRate = $stageCommissions[$stage];

            $totalValue = $stagePrice * $referrals;
            $userCommissionAmount = ($totalValue * $userCommissionRate) / 100;
            $adminEarningsAmount = ($totalValue * $adminCommission) / 100;
            $netRevenue = $totalValue - $userCommissionAmount - $adminEarningsAmount;

            $calculation = [
                'total_value' => $totalValue,
                'user_commission' => $userCommissionAmount,
                'admin_earnings' => $adminEarningsAmount,
                'net_revenue' => $netRevenue,
                'stage_price' => $stagePrice,
                'user_commission_rate' => $userCommissionRate,
            ];

            return response()->json(['success' => true, 'data' => $calculation]);
        } catch (\Exception $e) {
            Log::error('Error calculating admin earnings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error calculating earnings']);
        }
    }

    /**
     * System Maintenance Tools
     */
    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');

            Log::channel('admin')->info('System cache cleared', [
                'admin_id' => auth()->id(),
                'timestamp' => now(),
            ]);

            return response()->json(['success' => true, 'message' => 'All caches cleared successfully']);
        } catch (\Exception $e) {
            Log::error('Error clearing cache: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error clearing cache']);
        }
    }

    public function optimizeSystem()
    {
        try {
            Artisan::call('optimize');
            Artisan::call('config:cache');
            Artisan::call('route:cache');

            Log::channel('admin')->info('System optimized', [
                'admin_id' => auth()->id(),
                'timestamp' => now(),
            ]);

            return response()->json(['success' => true, 'message' => 'System optimized successfully']);
        } catch (\Exception $e) {
            Log::error('Error optimizing system: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error optimizing system']);
        }
    }

    public function getSystemInfo()
    {
        try {
            $info = [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'server_time' => now()->toDateTimeString(),
                'timezone' => config('app.timezone'),
                'environment' => app()->environment(),
                'debug_mode' => config('app.debug'),
                'cache_driver' => config('cache.default'),
                'session_driver' => config('session.driver'),
                'queue_driver' => config('queue.default'),
                'mail_driver' => config('mail.default'),
                'database_connection' => config('database.default'),
                'storage_disk' => config('filesystems.default'),
            ];

            return response()->json(['success' => true, 'data' => $info]);
        } catch (\Exception $e) {
            Log::error('Error fetching system info: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching system information']);
        }
    }

    /**
     * Security Management
     */
    public function getSecurityLogs()
    {
        try {
            $logs = DB::table('security_logs')
                     ->orderBy('created_at', 'desc')
                     ->limit(100)
                     ->get();

            return response()->json(['success' => true, 'data' => $logs]);
        } catch (\Exception $e) {
            Log::error('Error fetching security logs: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching security logs']);
        }
    }

    public function updateSecuritySettings(Request $request)
    {
        try {
            $settings = $request->validate([
                'max_login_attempts' => 'required|integer|min:1|max:10',
                'lockout_duration' => 'required|integer|min:300|max:3600',
                'session_timeout' => 'required|integer|min:900|max:7200',
                'require_2fa' => 'boolean',
                'password_min_length' => 'required|integer|min:6|max:20',
            ]);

            foreach ($settings as $key => $value) {
                DB::table('settings')->updateOrInsert(
                    ['key' => "security.{$key}"],
                    ['value' => $value, 'updated_at' => now()]
                );
            }

            Log::channel('admin')->info('Security settings updated', [
                'admin_id' => auth()->id(),
                'settings' => $settings,
            ]);

            return response()->json(['success' => true, 'message' => 'Security settings updated successfully']);
        } catch (\Exception $e) {
            Log::error('Error updating security settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating security settings']);
        }
    }

    /**
     * Helper Methods
     */
    private function calculatePlatformBalance()
    {
        $totalDeposits = Transaction::where('type', 'deposit')->sum('amount');
        $totalWithdrawals = Withdrawal::where('status', 'completed')->sum('amount');
        $totalCommissions = Transaction::where('type', 'commission')->sum('amount');
        
        return $totalDeposits - $totalWithdrawals - $totalCommissions;
    }
}
