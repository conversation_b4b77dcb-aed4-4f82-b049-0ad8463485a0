<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectParticipation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'featured_project_id',
        'status',
        'submitted_proof',
        'admin_notes',
        'points_earned',
        'bonus_earned',
        'enrolled_at',
        'completed_at',
        'verified_at',
    ];

    protected $casts = [
        'submitted_proof' => 'array',
        'points_earned' => 'decimal:2',
        'bonus_earned' => 'decimal:2',
        'enrolled_at' => 'datetime',
        'completed_at' => 'datetime',
        'verified_at' => 'datetime',
    ];

    /**
     * Get the user that owns the participation.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the featured project.
     */
    public function featuredProject()
    {
        return $this->belongsTo(FeaturedProject::class);
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute()
    {
        return match($this->status) {
            'enrolled' => 'blue',
            'in_progress' => 'yellow',
            'completed' => 'orange',
            'verified' => 'green',
            'rejected' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get status display text.
     */
    public function getStatusDisplayAttribute()
    {
        return match($this->status) {
            'enrolled' => 'Enrolled',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'verified' => 'Verified',
            'rejected' => 'Rejected',
            default => 'Unknown',
        };
    }
}
