<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\MembershipStage;
use App\Models\StageActivation;
use App\Models\User;
use App\Models\Activity;
use App\Models\StageResource;
use Carbon\Carbon;

class StageManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Get stage statistics
     */
    public function getStageStats()
    {
        try {
            $stats = [
                'total_stages' => MembershipStage::count(),
                'total_activations' => StageActivation::count(),
                'active_members' => User::whereHas('activations')->count(),
                'total_revenue' => StageActivation::join('membership_stages', 'stage_activations.stage_id', '=', 'membership_stages.id')
                    ->sum('membership_stages.price'),
                'activations_today' => StageActivation::whereDate('activated_at', Carbon::today())->count(),
                'most_popular_stage' => $this->getMostPopularStage(),
                'completion_rate' => $this->getCompletionRate(),
                'average_progression_time' => $this->getAverageProgressionTime(),
            ];

            // Stage-specific stats
            for ($i = 1; $i <= 6; $i++) {
                $stage = MembershipStage::where('number', $i)->first();
                if ($stage) {
                    $stats["stage_{$i}_activations"] = StageActivation::where('stage_id', $stage->id)->count();
                    $stats["stage_{$i}_revenue"] = StageActivation::where('stage_id', $stage->id)->count() * $stage->price;
                }
            }

            return response()->json(['success' => true, 'data' => $stats]);
        } catch (\Exception $e) {
            Log::error('Error fetching stage stats: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching stage statistics']);
        }
    }

    public function show(MembershipStage $stage)
    {
        $stage->load('userActivations.user');
        
        $activations = $stage->userActivations()
            ->with('user')
            ->latest()
            ->paginate(20);

        $stats = [
            'total_activations' => $stage->userActivations->count(),
            'active_activations' => $stage->userActivations->where('is_active', true)->count(),
            'pending_approvals' => $stage->userActivations->where('approval_status', 'pending')->count(),
            'total_revenue' => $stage->userActivations->sum('activation_bonus_paid'),
        ];

        return view('admin.stages.show', compact('stage', 'activations', 'stats'));
    }

    public function edit(MembershipStage $stage)
    {
        return view('admin.stages.edit', compact('stage'));
    }

    public function update(Request $request, MembershipStage $stage)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'benefits' => 'required|array',
            'benefits.*' => 'string|max:255',
            'pricing_amount' => 'required|numeric|min:0',
            'referral_requirement_min' => 'required|integer|min:0',
            'referral_requirement_max' => 'required|integer|min:0',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'color' => 'nullable|string|max:7',
            'icon' => 'nullable|string|max:50',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $stage->name) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Format referral requirement
        if ($validated['referral_requirement_min'] == $validated['referral_requirement_max']) {
            $validated['referral_requirement'] = $validated['referral_requirement_min'] . ' referrals';
        } else {
            $validated['referral_requirement'] = $validated['referral_requirement_min'] . '-' . $validated['referral_requirement_max'] . ' referrals';
        }

        // Convert benefits array to JSON
        $validated['benefits'] = json_encode($validated['benefits']);

        // Remove the min/max fields as they're not in the database
        unset($validated['referral_requirement_min'], $validated['referral_requirement_max']);

        $stage->update($validated);

        return redirect()->route('admin.stages.show', $stage)
            ->with('success', 'Stage updated successfully.');
    }

    public function create()
    {
        return view('admin.stages.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:membership_stages,name',
            'description' => 'required|string',
            'benefits' => 'required|array',
            'benefits.*' => 'string|max:255',
            'pricing_amount' => 'required|numeric|min:0',
            'referral_requirement_min' => 'required|integer|min:0',
            'referral_requirement_max' => 'required|integer|min:0',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'color' => 'nullable|string|max:7',
            'icon' => 'nullable|string|max:50',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        // Format referral requirement
        if ($validated['referral_requirement_min'] == $validated['referral_requirement_max']) {
            $validated['referral_requirement'] = $validated['referral_requirement_min'] . ' referrals';
        } else {
            $validated['referral_requirement'] = $validated['referral_requirement_min'] . '-' . $validated['referral_requirement_max'] . ' referrals';
        }

        // Convert benefits array to JSON
        $validated['benefits'] = json_encode($validated['benefits']);

        // Remove the min/max fields as they're not in the database
        unset($validated['referral_requirement_min'], $validated['referral_requirement_max']);

        $stage = MembershipStage::create($validated);

        return redirect()->route('admin.stages.show', $stage)
            ->with('success', 'Stage created successfully.');
    }

    public function updatePricing(Request $request, MembershipStage $stage)
    {
        $validated = $request->validate([
            'pricing_amount' => 'required|numeric|min:0',
            'referral_requirement_min' => 'required|integer|min:0',
            'referral_requirement_max' => 'required|integer|min:0',
        ]);

        // Format referral requirement
        if ($validated['referral_requirement_min'] == $validated['referral_requirement_max']) {
            $referralRequirement = $validated['referral_requirement_min'] . ' referrals';
        } else {
            $referralRequirement = $validated['referral_requirement_min'] . '-' . $validated['referral_requirement_max'] . ' referrals';
        }

        $stage->update([
            'pricing_amount' => $validated['pricing_amount'],
            'referral_requirement' => $referralRequirement,
        ]);

        return redirect()->route('admin.stages.show', $stage)
            ->with('success', 'Pricing updated successfully.');
    }

    public function updateBenefits(Request $request, MembershipStage $stage)
    {
        $validated = $request->validate([
            'benefits' => 'required|array',
            'benefits.*' => 'string|max:255',
        ]);

        $stage->update([
            'benefits' => json_encode($validated['benefits']),
        ]);

        return redirect()->route('admin.stages.show', $stage)
            ->with('success', 'Benefits updated successfully.');
    }

    public function toggleStatus(MembershipStage $stage)
    {
        $stage->update([
            'is_active' => !$stage->is_active,
        ]);

        $status = $stage->is_active ? 'activated' : 'deactivated';
        
        return redirect()->route('admin.stages.show', $stage)
            ->with('success', "Stage {$status} successfully.");
    }

    public function destroy(MembershipStage $stage)
    {
        if ($stage->userActivations()->count() > 0) {
            return redirect()->route('admin.stages.index')
                ->with('error', 'Cannot delete stage with existing activations.');
        }

        $stage->delete();

        return redirect()->route('admin.stages.index')
            ->with('success', 'Stage deleted successfully.');
    }

    public function reorder(Request $request)
    {
        $request->validate([
            'stages' => 'required|array',
            'stages.*.id' => 'required|exists:membership_stages,id',
            'stages.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->stages as $stageData) {
            MembershipStage::where('id', $stageData['id'])
                ->update(['sort_order' => $stageData['sort_order']]);
        }

        return response()->json(['success' => true]);
    }
}
