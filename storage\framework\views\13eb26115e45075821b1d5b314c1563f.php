<?php $__env->startSection('title', 'System Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">System Settings</h1>
        <p class="mt-2 text-gray-600">Customize your platform appearance, content, and functionality</p>
        <?php if(!auth()->user()->is_top_admin): ?>
        <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        <strong>Limited Access:</strong> Only Top Administrator can modify system settings.
                    </p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Simple Test -->
    <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 class="text-sm font-medium text-yellow-800 mb-2">🔧 JavaScript Test</h3>
        <button onclick="alert('Basic onclick works!')" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">Test Basic Click</button>
        <button id="testBtn" class="ml-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">Test Event Listener</button>
        <script>
            document.getElementById('testBtn').addEventListener('click', function() {
                alert('Event listener works!');
            });
        </script>
    </div>

    <!-- Management Tools -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Management Tools</h3>
            <p class="text-sm text-gray-500 mt-1">Quick access to platform management features</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a href="<?php echo e(route('admin.stage-activations.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-orange-200">
                        <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Stage Activations</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Approve and manage user stage activations</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.custom-content.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-indigo-200">
                        <svg class="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Custom Content</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage page titles, descriptions, and content</p>
                    </div>
                </a>









                <a href="<?php echo e(route('admin.profile-requests.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-200">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Profile Requests</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage user profile information requests</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.chat-monitoring.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-200">
                        <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Chat Monitoring</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Monitor messages and manage alert words</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.wallet-management.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-200">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"></path>
                            <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Wallet Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage currencies, wallets, and payments</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.points-rewards.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-200">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Points & Rewards</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage points system and rewards</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.achievements.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-amber-200">
                        <svg class="w-5 h-5 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Achievement System</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage achievements and badges</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.awareness-tools.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-teal-200">
                        <svg class="w-5 h-5 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Awareness Tools</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage referral tools and creatives</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.withheld-earnings.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-200">
                        <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Withheld Earnings</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage withheld referral earnings</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.club-management.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-purple-200">
                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Club Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage VIP clubs and invitations</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.rank-management.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-200">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Divine Lights Rank Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage user rankings and achievements</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.notifications.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-200">
                        <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Notification Alerts</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Monitor system updates and alerts</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.community-management.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-200">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Community Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage projects, events, and discussions</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.referral-tools.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-200">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Referral Tools Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage referral tools and campaigns</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.referral-management.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-orange-200">
                        <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Referral Management System</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Comprehensive referral tracking and analytics</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.virtual-tools.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-teal-200">
                        <svg class="w-5 h-5 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Virtual Tools Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage virtual tools and section visibility</p>
                    </div>
                </a>



                <a href="<?php echo e(route('admin.languages.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-emerald-200">
                        <svg class="w-5 h-5 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Language Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage multilingual content and translations</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.admin-management.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-violet-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-violet-200">
                        <svg class="w-5 h-5 text-violet-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Admin Management System</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage admin users and permissions</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.system-maintenance.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-200">
                        <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">System Maintenance</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Backup, restore, logs, and maintenance tools</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.platform-customization.index')); ?>" class="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer text-left">
                    <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-indigo-200">
                        <svg class="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Platform Customization</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Complete branding, pricing, VIP features, and appearance</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.user-management.index')); ?>" class="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer text-left">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-200">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">User Management System</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Complete user account, profile, wallet, and analytics management</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.security-management.index')); ?>" class="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer text-left">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-200">
                        <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Security Management System</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Authentication, permissions, and security controls</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.helpers-management.index')); ?>" class="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer text-left">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-200">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Helpers Management System</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Point sharing, requests, and special events management</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.financial-overview.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-200">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Financial Overview & Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Complete financial statistics and admin earnings calculator</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.stage-management-system.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer">
                    <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-indigo-200">
                        <svg class="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Stage Management System</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage all six stages with individual controls and settings</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.website-management.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-cyan-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-cyan-200">
                        <svg class="w-5 h-5 text-cyan-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Website Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Complete website settings, SEO, and security</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.payments.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-200">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Payment Management</h4>
                        <p class="text-xs text-gray-500 group-hover:text-indigo-700">Manage payment methods and transactions</p>
                    </div>
                </a>
            </div>
        </div>
    </div>



    <?php if(auth()->user()->is_top_admin): ?>
    <!-- Settings Form -->
    <form method="POST" action="<?php echo e(route('admin.settings.update')); ?>" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
    <?php endif; ?>

    <!-- Settings Sections -->
    <div class="space-y-6">
        <?php $__currentLoopData = $settingGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupKey => $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900"><?php echo e($group['label']); ?></h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php $__currentLoopData = $group['settings']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="<?php echo e($setting['type'] === 'textarea' ? 'md:col-span-2' : ''); ?>">
                        <label for="setting_<?php echo e($setting['key']); ?>" class="block text-sm font-medium text-gray-700">
                            <?php echo e($setting['label']); ?>

                            <?php if($setting['description']): ?>
                            <span class="text-xs text-gray-500 block"><?php echo e($setting['description']); ?></span>
                            <?php endif; ?>
                        </label>

                        <?php if($setting['type'] === 'text' || $setting['type'] === 'email' || $setting['type'] === 'url'): ?>
                        <input type="<?php echo e($setting['type']); ?>"
                               id="setting_<?php echo e($setting['key']); ?>"
                               name="settings[<?php echo e($setting['key']); ?>]"
                               value="<?php echo e($setting['value']); ?>"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                               <?php echo e(!auth()->user()->is_top_admin ? 'readonly' : ''); ?>>

                        <?php elseif($setting['type'] === 'number'): ?>
                        <input type="number"
                               id="setting_<?php echo e($setting['key']); ?>"
                               name="settings[<?php echo e($setting['key']); ?>]"
                               value="<?php echo e($setting['value']); ?>"
                               step="0.01"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                               <?php echo e(!auth()->user()->is_top_admin ? 'readonly' : ''); ?>>

                        <?php elseif($setting['type'] === 'textarea'): ?>
                        <textarea id="setting_<?php echo e($setting['key']); ?>"
                                  name="settings[<?php echo e($setting['key']); ?>]"
                                  rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                  <?php echo e(!auth()->user()->is_top_admin ? 'readonly' : ''); ?>><?php echo e($setting['value']); ?></textarea>

                        <?php elseif($setting['type'] === 'boolean'): ?>
                        <select id="setting_<?php echo e($setting['key']); ?>"
                                name="settings[<?php echo e($setting['key']); ?>]"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                <?php echo e(!auth()->user()->is_top_admin ? 'disabled' : ''); ?>>
                            <option value="1" <?php echo e($setting['value'] ? 'selected' : ''); ?>>Enabled</option>
                            <option value="0" <?php echo e(!$setting['value'] ? 'selected' : ''); ?>>Disabled</option>
                        </select>

                        <?php elseif($setting['type'] === 'image'): ?>
                        <div class="mt-1">
                            <?php if($setting['value']): ?>
                            <div class="mb-2">
                                <img src="<?php echo e(asset('storage/' . $setting['value'])); ?>" alt="<?php echo e($setting['label']); ?>" class="h-20 w-auto">
                            </div>
                            <?php endif; ?>
                            <?php if(auth()->user()->is_top_admin): ?>
                            <input type="file"
                                   id="setting_<?php echo e($setting['key']); ?>"
                                   name="settings[<?php echo e($setting['key']); ?>]"
                                   accept="image/*"
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                            <?php else: ?>
                            <p class="text-sm text-gray-500">Image upload restricted to Top Administrator</p>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </div>

    <?php if(auth()->user()->is_top_admin): ?>
        <!-- Save Button -->
        <div class="mt-8 flex justify-end">
            <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                Save Settings
            </button>
        </div>
    </form>
    <?php endif; ?>

    <!-- Info Box -->
    <?php if(!auth()->user()->is_top_admin): ?>
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Settings Information</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>Only the Top Administrator can modify system settings. These settings control the appearance, functionality, and content of your platform.</p>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">Customization Features</h3>
                <div class="mt-2 text-sm text-green-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>Company Branding:</strong> Customize your company name, logo, and description</li>
                        <li><strong>Page Content:</strong> Edit welcome messages and page texts</li>
                        <li><strong>Pricing Control:</strong> Set default commission rates and activation bonuses</li>
                        <li><strong>Appearance:</strong> Customize colors and visual elements</li>
                        <li><strong>System Settings:</strong> Control platform functionality and features</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Platform Customization Modal -->
<div id="platformCustomizationModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-2xl font-bold text-gray-900">Platform Customization</h3>
                <button onclick="closePlatformCustomization()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Tabs -->
            <div class="mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'branding' }">
                        <button @click="activeTab = 'branding'" :class="activeTab === 'branding' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Branding & Logo
                        </button>
                        <button @click="activeTab = 'appearance'" :class="activeTab === 'appearance' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Appearance
                        </button>
                        <button @click="activeTab = 'content'" :class="activeTab === 'content' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Content
                        </button>
                        <button @click="activeTab = 'features'" :class="activeTab === 'features' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Features
                        </button>
                        <button @click="activeTab = 'pricing'" :class="activeTab === 'pricing' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Stage Pricing
                        </button>
                        <button @click="activeTab = 'vip'" :class="activeTab === 'vip' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            VIP & Barcode
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Tab Content -->
            <div x-data="{ activeTab: 'branding' }" class="max-h-96 overflow-y-auto">
                <!-- Branding Tab -->
                <div x-show="activeTab === 'branding'" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Logo Upload -->
                        <div class="space-y-6">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Platform Logo</h4>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                    <div class="mb-4">
                                        <img src="/images/default-logo.png" alt="Current Logo" class="mx-auto h-16 w-auto">
                                    </div>
                                    <div class="space-y-2">
                                        <p class="text-sm text-gray-600">Upload a new logo</p>
                                        <input type="file" accept="image/*" class="hidden" id="logoUpload" onchange="handleLogoUpload(event)">
                                        <button onclick="document.getElementById('logoUpload').click()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                            Choose Logo
                                        </button>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-2">Recommended: PNG or SVG, max 2MB</p>
                                </div>
                            </div>

                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Admin Logo</h4>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                    <div class="mb-4">
                                        <img src="/images/admin-logo.png" alt="Admin Logo" class="mx-auto h-12 w-auto">
                                    </div>
                                    <div class="space-y-2">
                                        <p class="text-sm text-gray-600">Upload admin panel logo</p>
                                        <input type="file" accept="image/*" class="hidden" id="adminLogoUpload" onchange="handleAdminLogoUpload(event)">
                                        <button onclick="document.getElementById('adminLogoUpload').click()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                            Choose Admin Logo
                                        </button>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-2">Recommended: PNG or SVG, max 1MB</p>
                                </div>
                            </div>
                        </div>

                        <!-- Brand Settings -->
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Platform Name</label>
                                <input type="text" value="Divine Lights Platform" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tagline</label>
                                <input type="text" value="Illuminating Paths to Awareness" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Primary Brand Color</label>
                                <div class="flex items-center space-x-3">
                                    <input type="color" value="#4F46E5" class="h-10 w-20 border border-gray-300 rounded-md">
                                    <input type="text" value="#4F46E5" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Secondary Brand Color</label>
                                <div class="flex items-center space-x-3">
                                    <input type="color" value="#10B981" class="h-10 w-20 border border-gray-300 rounded-md">
                                    <input type="text" value="#10B981" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appearance Tab -->
                <div x-show="activeTab === 'appearance'" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Theme Settings -->
                        <div class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900">Theme Settings</h4>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Color Scheme</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="light" selected>Light Theme</option>
                                    <option value="dark">Dark Theme</option>
                                    <option value="auto">Auto (System)</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Font Family</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="inter" selected>Inter</option>
                                    <option value="roboto">Roboto</option>
                                    <option value="opensans">Open Sans</option>
                                    <option value="lato">Lato</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Button Style</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="rounded" selected>Rounded</option>
                                    <option value="square">Square</option>
                                    <option value="pill">Pill</option>
                                </select>
                            </div>
                        </div>

                        <!-- Layout Settings -->
                        <div class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900">Layout Settings</h4>

                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Fixed Header</span>
                                </label>
                            </div>

                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Sidebar Navigation</span>
                                </label>
                            </div>

                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">Breadcrumb Navigation</span>
                                </label>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Container Width</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="fluid">Full Width</option>
                                    <option value="container" selected>Container</option>
                                    <option value="narrow">Narrow</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Tab -->
                <div x-show="activeTab === 'content'" class="space-y-6">
                    <div class="space-y-6">
                        <h4 class="text-lg font-medium text-gray-900">Page Content Management</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Homepage Title</label>
                                <input type="text" value="Welcome to Divine Lights Platform" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Homepage Subtitle</label>
                                <input type="text" value="Your Journey to Spiritual Awareness Begins Here" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Homepage Description</label>
                            <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2">Join thousands of members on a transformative journey through six stages of awareness. Build community, earn rewards, and unlock your spiritual potential with our comprehensive platform.</textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Footer Copyright</label>
                                <input type="text" value="© 2024 Divine Lights Platform. All rights reserved." class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                                <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features Tab -->
                <div x-show="activeTab === 'features'" class="space-y-6">
                    <div class="space-y-6">
                        <h4 class="text-lg font-medium text-gray-900">Platform Features</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Core Features -->
                            <div class="space-y-4">
                                <h5 class="text-md font-medium text-gray-900">Core Features</h5>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">User Registration</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Email Verification</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Referral System</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Stage Activations</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Wallet System</span>
                                </label>
                            </div>

                            <!-- Advanced Features -->
                            <div class="space-y-4">
                                <h5 class="text-md font-medium text-gray-900">Advanced Features</h5>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Community Features</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">VIP Club System</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Virtual Tools</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Multilingual Support</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Auto-Release Earnings</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stage Pricing Tab -->
                <div x-show="activeTab === 'pricing'" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Stage 1 Pricing -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 1 - Individual</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                                    <div class="flex items-center">
                                        <span class="text-gray-500 mr-2">$</span>
                                        <input type="number" value="25.00" step="0.01" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Referral Commission</label>
                                    <div class="flex items-center">
                                        <input type="number" value="15" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                        <span class="text-gray-500 ml-2">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Stage 2 Pricing -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 2 - Family</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                                    <div class="flex items-center">
                                        <span class="text-gray-500 mr-2">$</span>
                                        <input type="number" value="50.00" step="0.01" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Referral Commission</label>
                                    <div class="flex items-center">
                                        <input type="number" value="20" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                        <span class="text-gray-500 ml-2">%</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enable recurring payments</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Stage 3 Pricing -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 3 - Community</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                                    <div class="flex items-center">
                                        <span class="text-gray-500 mr-2">$</span>
                                        <input type="number" value="100.00" step="0.01" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Referral Commission</label>
                                    <div class="flex items-center">
                                        <input type="number" value="25" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                        <span class="text-gray-500 ml-2">%</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enable recurring payments</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Stage 4 Pricing -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 4 - Social</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                                    <div class="flex items-center">
                                        <span class="text-gray-500 mr-2">$</span>
                                        <input type="number" value="200.00" step="0.01" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Referral Commission</label>
                                    <div class="flex items-center">
                                        <input type="number" value="30" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                        <span class="text-gray-500 ml-2">%</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enable recurring payments</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Stage 5 Pricing -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 5 - Global</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                                    <div class="flex items-center">
                                        <span class="text-gray-500 mr-2">$</span>
                                        <input type="number" value="500.00" step="0.01" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Referral Commission</label>
                                    <div class="flex items-center">
                                        <input type="number" value="35" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                        <span class="text-gray-500 ml-2">%</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enable recurring payments</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Stage 6 Pricing -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Stage 6 - Universal</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Activation Price</label>
                                    <div class="flex items-center">
                                        <span class="text-gray-500 mr-2">$</span>
                                        <input type="number" value="1000.00" step="0.01" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Referral Commission</label>
                                    <div class="flex items-center">
                                        <input type="number" value="40" class="flex-1 border border-gray-300 rounded-md px-3 py-2">
                                        <span class="text-gray-500 ml-2">%</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enable recurring payments</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Auto-Release Settings -->
                    <div class="mt-8 border-t border-gray-200 pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Auto-Release Earnings System</h4>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800">Auto-Release Feature</h3>
                                    <div class="mt-2 text-sm text-green-700">
                                        <p>Automatically releases withheld earnings when sponsors activate the same stage as their referrals.</p>
                                    </div>
                                    <div class="mt-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300" checked>
                                            <span class="ml-2 text-sm text-green-700">Enable auto-release of withheld earnings</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- VIP & Barcode Tab -->
                <div x-show="activeTab === 'vip'" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- VIP Club Tiers -->
                        <div class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900">VIP Club Tiers</h4>

                            <!-- VIP Tier -->
                            <div class="border border-gray-200 rounded-lg p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-md font-medium text-gray-900">VIP Club</h5>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Active
                                    </span>
                                </div>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Required Stages</label>
                                        <input type="text" value="Stage 1-3" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                    </div>
                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300" checked>
                                            <span class="ml-2 text-sm text-gray-700">Enable barcode generation</span>
                                        </label>
                                    </div>
                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded border-gray-300" checked>
                                            <span class="ml-2 text-sm text-gray-700">Access to offline activities</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Barcode Generation Settings -->
                        <div class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900">Barcode Generation Settings</h4>

                            <div class="border border-gray-200 rounded-lg p-6">
                                <h5 class="text-md font-medium text-gray-900 mb-4">Barcode Configuration</h5>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Barcode Format</label>
                                        <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option value="qr">QR Code</option>
                                            <option value="code128">Code 128</option>
                                            <option value="ean13">EAN-13</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Barcode Size</label>
                                        <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option value="small">Small (200x200)</option>
                                            <option value="medium" selected>Medium (300x300)</option>
                                            <option value="large">Large (500x500)</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Include User Info</label>
                                        <div class="space-y-2">
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded border-gray-300" checked>
                                                <span class="ml-2 text-sm text-gray-700">User Name</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded border-gray-300" checked>
                                                <span class="ml-2 text-sm text-gray-700">Member ID</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded border-gray-300">
                                                <span class="ml-2 text-sm text-gray-700">VIP Tier</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded border-gray-300" checked>
                                                <span class="ml-2 text-sm text-gray-700">Expiry Date</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sample Barcode Preview -->
                            <div class="border border-gray-200 rounded-lg p-6">
                                <h5 class="text-md font-medium text-gray-900 mb-4">Barcode Preview</h5>
                                <div class="text-center">
                                    <div class="inline-block border-2 border-dashed border-gray-300 p-4 rounded-lg">
                                        <div class="w-32 h-32 bg-gray-100 flex items-center justify-center mb-2">
                                            <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v1.5h16V5a2 2 0 00-2-2H4zm14 4.5H2V14a2 2 0 002 2h12a2 2 0 002-2V7.5zM5 9a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm0 2.5a1 1 0 011-1h4a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <p class="text-xs text-gray-500">VIP Barcode Preview</p>
                                    </div>
                                    <button onclick="generateSampleBarcode()" class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                        Generate Sample
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="mt-6 flex items-center justify-end space-x-3 border-t border-gray-200 pt-4">
                <button onclick="closePlatformCustomization()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </button>
                <button onclick="savePlatformCustomization()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Save Changes
                </button>
            </div>
        </div>
    </div>
</div>





<!-- Security Management Modal -->
<div id="securityManagementModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-2xl font-bold text-gray-900">Security Management System</h3>
                <button onclick="closeSecurityManagement()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Security Management Content -->
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Authentication Settings -->
                    <div class="space-y-6">
                        <h4 class="text-lg font-medium text-gray-900">Authentication & Access Control</h4>

                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h5 class="text-md font-medium text-gray-900 mb-4">Authentication Settings</h5>
                            <div class="space-y-4">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Two-Factor Authentication</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Strong Password Requirements</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">Login Rate Limiting</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Session Timeout</span>
                                </label>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h5 class="text-md font-medium text-gray-900 mb-4">Security Policies</h5>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Session Timeout (minutes)</label>
                                    <input type="number" value="30" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Maximum Login Attempts</label>
                                    <input type="number" value="5" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Password Minimum Length</label>
                                    <input type="number" value="8" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Protection -->
                    <div class="space-y-6">
                        <h4 class="text-lg font-medium text-gray-900">Data Protection & Privacy</h4>

                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h5 class="text-md font-medium text-gray-900 mb-4">Data Protection</h5>
                            <div class="space-y-4">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">SSL/HTTPS Enforcement</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">Data Encryption</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300" checked>
                                    <span class="ml-2 text-sm text-gray-700">GDPR Compliance</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">Automatic Backups</span>
                                </label>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h5 class="text-md font-medium text-gray-900 mb-4">Security Monitoring</h5>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Failed Login Attempts (24h):</span>
                                    <span class="text-sm font-medium text-red-600">23</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Suspicious Activities:</span>
                                    <span class="text-sm font-medium text-yellow-600">5</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Security Score:</span>
                                    <span class="text-sm font-medium text-green-600">95/100</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Actions -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Security Actions</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <button onclick="runSecurityScan()" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Run Security Scan
                        </button>
                        <button onclick="viewSecurityLogs()" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            View Security Logs
                        </button>
                        <button onclick="generateSecurityReport()" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                            Generate Security Report
                        </button>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="mt-6 flex items-center justify-end space-x-3 border-t border-gray-200 pt-4">
                <button onclick="closeSecurityManagement()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Close
                </button>
                <button onclick="saveSecuritySettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                    Save Security Settings
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Helpers Management Modal -->
<div id="helpersManagementModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-2xl font-bold text-gray-900">Helpers Management System</h3>
                <button onclick="closeHelpersManagement()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Helpers Management Tabs -->
            <div class="mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'requests' }">
                        <button @click="activeTab = 'requests'" :class="activeTab === 'requests' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Point Requests
                        </button>
                        <button @click="activeTab = 'sharing'" :class="activeTab === 'sharing' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Point Sharing
                        </button>
                        <button @click="activeTab = 'events'" :class="activeTab === 'events' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Special Events
                        </button>
                        <button @click="activeTab = 'settings'" :class="activeTab === 'settings' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Point Settings
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Helpers Management Tab Content -->
            <div x-data="{ activeTab: 'requests' }" class="max-h-96 overflow-y-auto">
                <!-- Point Requests Tab -->
                <div x-show="activeTab === 'requests'" class="space-y-6">
                    <div class="flex items-center justify-between">
                        <h4 class="text-lg font-medium text-gray-900">Pending Point Requests</h4>
                        <div class="flex items-center space-x-3">
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                15 Pending Review
                            </span>
                            <button onclick="approveAllRequests()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                Approve All
                            </button>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <!-- Request Item -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Sarah+Johnson&background=10B981&color=fff" alt="">
                                        <div>
                                            <h5 class="text-lg font-medium text-gray-900">Sarah Johnson</h5>
                                            <p class="text-sm text-gray-500">Light Member • ID: LM12345</p>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Scholarship Points
                                        </span>
                                    </div>
                                    <p class="text-gray-700 mb-3">Requesting scholarship points to support my younger sister's college education. She has excellent grades but our family is facing financial difficulties.</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>Requested: 2,500 points</span>
                                        <span>•</span>
                                        <span>Submitted: 2 hours ago</span>
                                    </div>
                                </div>
                                <div class="ml-6 flex-shrink-0 space-x-2">
                                    <button onclick="approveRequest('req_001')" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                        Approve
                                    </button>
                                    <button onclick="rejectRequest('req_001')" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                        Reject
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Request Item -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Michael+Chen&background=F59E0B&color=fff" alt="">
                                        <div>
                                            <h5 class="text-lg font-medium text-gray-900">Michael Chen</h5>
                                            <p class="text-sm text-gray-500">Light Member • ID: LM67890</p>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                            Medical Points
                                        </span>
                                    </div>
                                    <p class="text-gray-700 mb-3">Need medical points to help cover treatment costs for my elderly mother's surgery. Any support would be greatly appreciated.</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>Requested: 5,000 points</span>
                                        <span>•</span>
                                        <span>Submitted: 5 hours ago</span>
                                    </div>
                                </div>
                                <div class="ml-6 flex-shrink-0 space-x-2">
                                    <button onclick="approveRequest('req_002')" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                        Approve
                                    </button>
                                    <button onclick="rejectRequest('req_002')" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                        Reject
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Point Sharing Tab -->
                <div x-show="activeTab === 'sharing'" class="space-y-6">
                    <h4 class="text-lg font-medium text-gray-900">Point Sharing Activity</h4>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <div class="text-2xl font-bold text-green-600">1,247</div>
                            <div class="text-sm text-gray-600">Points Shared Today</div>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <div class="text-2xl font-bold text-blue-600">156,789</div>
                            <div class="text-sm text-gray-600">Total Points Shared</div>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <div class="text-2xl font-bold text-purple-600">2,456</div>
                            <div class="text-sm text-gray-600">Active Sharers</div>
                        </div>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sender</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Recipient</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Points</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-900">2024-06-23</td>
                                    <td class="px-4 py-3 text-sm text-gray-900">John Smith</td>
                                    <td class="px-4 py-3 text-sm text-gray-900">Sarah Johnson</td>
                                    <td class="px-4 py-3">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Scholarship
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-900">1,500</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Special Events Tab -->
                <div x-show="activeTab === 'events'" class="space-y-6">
                    <div class="flex items-center justify-between">
                        <h4 class="text-lg font-medium text-gray-900">Special Point Events</h4>
                        <button onclick="createSpecialEvent()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Create Event
                        </button>
                    </div>

                    <div class="space-y-4">
                        <!-- Event Item -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h5 class="text-lg font-medium text-gray-900">Holiday Scholarship Drive</h5>
                                    <p class="text-gray-600 mt-1">Special scholarship points available for holiday season</p>
                                    <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                        <span>Available: Dec 20-31, 2024</span>
                                        <span>•</span>
                                        <span>5,000 points allocated</span>
                                        <span>•</span>
                                        <span class="text-green-600">Active</span>
                                    </div>
                                </div>
                                <div class="ml-6 flex-shrink-0 space-x-2">
                                    <button onclick="editEvent('event_001')" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        Edit
                                    </button>
                                    <button onclick="deactivateEvent('event_001')" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                        Deactivate
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Point Settings Tab -->
                <div x-show="activeTab === 'settings'" class="space-y-6">
                    <h4 class="text-lg font-medium text-gray-900">Point Type Configuration</h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Point Types -->
                        <div class="space-y-4">
                            <h5 class="text-md font-medium text-gray-900">Available Point Types</h5>

                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <span class="text-sm font-medium text-gray-900">Free Points</span>
                                        <p class="text-xs text-gray-500">General purpose gift points</p>
                                    </div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enabled</span>
                                    </label>
                                </div>

                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <span class="text-sm font-medium text-gray-900">Scholarship Points</span>
                                        <p class="text-xs text-gray-500">Educational support points</p>
                                    </div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enabled</span>
                                    </label>
                                </div>

                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <span class="text-sm font-medium text-gray-900">Medical Points</span>
                                        <p class="text-xs text-gray-500">Healthcare assistance points</p>
                                    </div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enabled</span>
                                    </label>
                                </div>

                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <span class="text-sm font-medium text-gray-900">Orphanage Points</span>
                                        <p class="text-xs text-gray-500">Orphanage support points</p>
                                    </div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Enabled</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Point Limits -->
                        <div class="space-y-4">
                            <h5 class="text-md font-medium text-gray-900">Point Limits & Rules</h5>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Daily Sharing Limit</label>
                                    <input type="number" value="10000" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Share Amount</label>
                                    <input type="number" value="10" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Maximum Request Amount</label>
                                    <input type="number" value="50000" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>

                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Require admin approval for requests</span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Light members only sharing</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="mt-6 flex items-center justify-end space-x-3 border-t border-gray-200 pt-4">
                <button onclick="closeHelpersManagement()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Close
                </button>
                <button onclick="saveHelpersSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Save Settings
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Financial Overview Modal -->
<div id="financialOverviewModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-2xl font-bold text-gray-900">Financial Overview & Management</h3>
                <button onclick="closeFinancialOverview()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Financial Overview Content -->
            <div class="space-y-6">
                <!-- Financial Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg p-6 text-white">
                        <div class="text-3xl font-bold">$2,456,789</div>
                        <div class="text-green-100">Total Revenue</div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-6 text-white">
                        <div class="text-3xl font-bold">$1,234,567</div>
                        <div class="text-blue-100">User Earnings</div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg p-6 text-white">
                        <div class="text-3xl font-bold">$456,789</div>
                        <div class="text-purple-100">Admin Earnings</div>
                    </div>
                    <div class="bg-gradient-to-r from-yellow-500 to-orange-600 rounded-lg p-6 text-white">
                        <div class="text-3xl font-bold">$765,433</div>
                        <div class="text-yellow-100">Pending Payouts</div>
                    </div>
                </div>

                <!-- Admin Earnings Calculator -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Admin Earnings Calculator</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Stage Level</label>
                                <select id="calcStage" class="w-full border border-gray-300 rounded-md px-3 py-2" onchange="calculateAdminEarnings()">
                                    <option value="1">Stage 1 - $25.00</option>
                                    <option value="2">Stage 2 - $50.00</option>
                                    <option value="3">Stage 3 - $100.00</option>
                                    <option value="4">Stage 4 - $200.00</option>
                                    <option value="5">Stage 5 - $500.00</option>
                                    <option value="6">Stage 6 - $1000.00</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Number of Referrals</label>
                                <input type="number" id="calcReferrals" value="100" min="1" class="w-full border border-gray-300 rounded-md px-3 py-2" onchange="calculateAdminEarnings()">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Admin Commission (%)</label>
                                <input type="number" id="calcCommission" value="10" min="0" max="100" class="w-full border border-gray-300 rounded-md px-3 py-2" onchange="calculateAdminEarnings()">
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="text-md font-medium text-gray-900 mb-3">Calculation Results</h5>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Total Activation Value:</span>
                                        <span id="totalValue" class="text-sm font-medium text-gray-900">$2,500.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">User Commission:</span>
                                        <span id="userCommission" class="text-sm font-medium text-gray-900">$375.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Admin Earnings:</span>
                                        <span id="adminEarnings" class="text-sm font-medium text-green-600">$250.00</span>
                                    </div>
                                    <div class="flex justify-between border-t border-gray-200 pt-2">
                                        <span class="text-sm font-medium text-gray-900">Net Platform Revenue:</span>
                                        <span id="netRevenue" class="text-sm font-bold text-indigo-600">$1,875.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Management Tools -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Financial Reports</h4>
                        <div class="space-y-3">
                            <button onclick="generateRevenueReport()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Generate Revenue Report
                            </button>
                            <button onclick="generateCommissionReport()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Commission Analysis Report
                            </button>
                            <button onclick="generatePayoutReport()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Payout Summary Report
                            </button>
                        </div>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Financial Controls</h4>
                        <div class="space-y-3">
                            <button onclick="processPendingPayouts()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                Process Pending Payouts
                            </button>
                            <button onclick="adjustCommissionRates()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                Adjust Commission Rates
                            </button>
                            <button onclick="manageWithdrawals()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                                Manage Withdrawals
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="mt-6 flex items-center justify-end space-x-3 border-t border-gray-200 pt-4">
                <button onclick="closeFinancialOverview()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Close
                </button>
                <button onclick="exportFinancialData()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    Export Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Stage Management Modal -->
<div id="stageManagementModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-2xl font-bold text-gray-900">Stage Management System</h3>
                <button onclick="closeStageManagement()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Stage Management Content -->
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Stage 1 Management -->
                    <div onclick="manageStage(1)" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-200">
                            <span class="text-blue-600 font-bold">1</span>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Stage 1 - Personal</h4>
                            <p class="text-xs text-gray-500 group-hover:text-indigo-700">Individual development stage</p>
                        </div>
                    </div>

                    <!-- Stage 2 Management -->
                    <div onclick="manageStage(2)" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-200">
                            <span class="text-green-600 font-bold">2</span>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Stage 2 - Family</h4>
                            <p class="text-xs text-gray-500 group-hover:text-indigo-700">Family-focused development</p>
                        </div>
                    </div>

                    <!-- Stage 3 Management -->
                    <div onclick="manageStage(3)" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-purple-200">
                            <span class="text-purple-600 font-bold">3</span>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Stage 3 - Community</h4>
                            <p class="text-xs text-gray-500 group-hover:text-indigo-700">Community engagement stage</p>
                        </div>
                    </div>

                    <!-- Stage 4 Management -->
                    <div onclick="manageStage(4)" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer">
                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-200">
                            <span class="text-yellow-600 font-bold">4</span>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Stage 4 - Social</h4>
                            <p class="text-xs text-gray-500 group-hover:text-indigo-700">Social impact and networking</p>
                        </div>
                    </div>

                    <!-- Stage 5 Management -->
                    <div onclick="manageStage(5)" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer">
                        <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-200">
                            <span class="text-red-600 font-bold">5</span>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Stage 5 - Global</h4>
                            <p class="text-xs text-gray-500 group-hover:text-indigo-700">Global awareness and action</p>
                        </div>
                    </div>

                    <!-- Stage 6 Management -->
                    <div onclick="manageStage(6)" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group cursor-pointer">
                        <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-indigo-200">
                            <span class="text-indigo-600 font-bold">6</span>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-indigo-900">Stage 6 - Universal</h4>
                            <p class="text-xs text-gray-500 group-hover:text-indigo-700">Universal consciousness</p>
                        </div>
                    </div>
                </div>

                <!-- Stage Management Overview -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Stage Management Overview</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">6</div>
                            <div class="text-sm text-gray-600">Total Stages</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">2,456</div>
                            <div class="text-sm text-gray-600">Active Members</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">$156,789</div>
                            <div class="text-sm text-gray-600">Total Stage Revenue</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="mt-6 flex items-center justify-end space-x-3 border-t border-gray-200 pt-4">
                <button onclick="closeStageManagement()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Close
                </button>
                <button onclick="exportStageData()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Export Stage Data
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showPlatformCustomization() {
    const modal = document.getElementById('platformCustomizationModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

function closePlatformCustomization() {
    const modal = document.getElementById('platformCustomizationModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function savePlatformCustomization() {
    alert('Platform customization settings saved successfully!');
    closePlatformCustomization();
}

function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        alert(`Logo uploaded: ${file.name}`);
    }
}

function handleAdminLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        alert(`Admin logo uploaded: ${file.name}`);
    }
}

function generateSampleBarcode() {
    alert('Sample barcode generated successfully!');
}



function createUser() {
    alert('Create user functionality will be implemented');
}

function editUser(id) {
    alert(`Edit user ${id} functionality will be implemented`);
}

function viewUser(id) {
    alert(`View user ${id} functionality will be implemented`);
}

function exportProfiles() {
    alert('Export profiles functionality will be implemented');
}

function bulkProfileUpdate() {
    alert('Bulk profile update functionality will be implemented');
}

function generateUserReport() {
    alert('User activity report generation will be implemented');
}

function generateFinancialReport() {
    alert('Financial report generation will be implemented');
}

function generateReferralReport() {
    alert('Referral report generation will be implemented');
}

// Security Management Modal Functions
function showSecurityManagement() {
    console.log('showSecurityManagement called');
    const modal = document.getElementById('securityManagementModal');
    if (modal) {
        modal.classList.remove('hidden');
        console.log('Security management modal opened');
    } else {
        console.error('Security management modal not found');
    }
}

function closeSecurityManagement() {
    const modal = document.getElementById('securityManagementModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function runSecurityScan() {
    alert('Security scan initiated. This may take a few minutes...');
}

function viewSecurityLogs() {
    alert('Security logs viewer will be implemented');
}

function generateSecurityReport() {
    alert('Security report generation will be implemented');
}

function saveSecuritySettings() {
    alert('Security settings saved successfully!');
    closeSecurityManagement();
}

// Helpers Management Modal Functions
function showHelpersManagement() {
    document.getElementById('helpersManagementModal').classList.remove('hidden');
}

function closeHelpersManagement() {
    document.getElementById('helpersManagementModal').classList.add('hidden');
}

function approveRequest(requestId) {
    if (confirm('Approve this point request?')) {
        alert(`Request ${requestId} approved successfully!`);
    }
}

function rejectRequest(requestId) {
    if (confirm('Reject this point request? This action cannot be undone.')) {
        alert(`Request ${requestId} rejected.`);
    }
}

function approveAllRequests() {
    if (confirm('Approve all pending requests?')) {
        alert('All pending requests approved successfully!');
    }
}

function createSpecialEvent() {
    alert('Create special event functionality will be implemented');
}

function editEvent(eventId) {
    alert(`Edit event ${eventId} functionality will be implemented`);
}

function deactivateEvent(eventId) {
    if (confirm('Deactivate this special event?')) {
        alert(`Event ${eventId} deactivated successfully!`);
    }
}

function saveHelpersSettings() {
    alert('Helpers settings saved successfully!');
    closeHelpersManagement();
}

// Financial Overview Modal Functions
function showFinancialOverview() {
    console.log('showFinancialOverview called');
    const modal = document.getElementById('financialOverviewModal');
    if (modal) {
        modal.classList.remove('hidden');
        calculateAdminEarnings(); // Initialize calculator
        console.log('Financial overview modal opened');
    } else {
        console.error('Financial overview modal not found');
    }
}

function closeFinancialOverview() {
    const modal = document.getElementById('financialOverviewModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function calculateAdminEarnings() {
    const stage = parseInt(document.getElementById('calcStage').value);
    const referrals = parseInt(document.getElementById('calcReferrals').value);
    const adminCommission = parseFloat(document.getElementById('calcCommission').value);

    const stagePrices = {1: 25, 2: 50, 3: 100, 4: 200, 5: 500, 6: 1000};
    const stageCommissions = {1: 15, 2: 20, 3: 25, 4: 30, 5: 35, 6: 40};

    const stagePrice = stagePrices[stage];
    const userCommissionRate = stageCommissions[stage];

    const totalValue = stagePrice * referrals;
    const userCommissionAmount = (totalValue * userCommissionRate) / 100;
    const adminEarningsAmount = (totalValue * adminCommission) / 100;
    const netRevenue = totalValue - userCommissionAmount - adminEarningsAmount;

    // Make API call to calculate earnings
    fetch('/admin/api/financial/calculate-earnings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            stage: stage,
            referrals: referrals,
            admin_commission: adminCommission
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('totalValue').textContent = `$${data.data.total_value.toLocaleString()}.00`;
            document.getElementById('userCommission').textContent = `$${data.data.user_commission.toLocaleString()}.00`;
            document.getElementById('adminEarnings').textContent = `$${data.data.admin_earnings.toLocaleString()}.00`;
            document.getElementById('netRevenue').textContent = `$${data.data.net_revenue.toLocaleString()}.00`;
        }
    })
    .catch(error => {
        console.error('Error calculating earnings:', error);
        // Fallback to local calculation
        document.getElementById('totalValue').textContent = `$${totalValue.toLocaleString()}.00`;
        document.getElementById('userCommission').textContent = `$${userCommissionAmount.toLocaleString()}.00`;
        document.getElementById('adminEarnings').textContent = `$${adminEarningsAmount.toLocaleString()}.00`;
        document.getElementById('netRevenue').textContent = `$${netRevenue.toLocaleString()}.00`;
    });
}

function generateRevenueReport() {
    alert('Revenue report generation will be implemented');
}

function generateCommissionReport() {
    alert('Commission analysis report generation will be implemented');
}

function generatePayoutReport() {
    alert('Payout summary report generation will be implemented');
}

function processPendingPayouts() {
    if (confirm('Process all pending payouts? This action cannot be undone.')) {
        alert('Pending payouts processed successfully!');
    }
}

function adjustCommissionRates() {
    alert('Commission rate adjustment interface will be implemented');
}

function manageWithdrawals() {
    alert('Withdrawal management interface will be implemented');
}

function exportFinancialData() {
    alert('Financial data export will be implemented');
}

// Stage Management Modal Functions
function showStageManagement() {
    document.getElementById('stageManagementModal').classList.remove('hidden');
}

function closeStageManagement() {
    document.getElementById('stageManagementModal').classList.add('hidden');
}

function manageStage(stageNumber) {
    alert(`Managing Stage ${stageNumber} - Individual stage management interface will be implemented`);
}

function exportStageData() {
    alert('Stage data export will be implemented');
}

// Ensure all modal functions are working properly
function ensureModalFunctions() {
    // Check if modals exist and functions are properly bound
    console.log('Modal functions initialized');

    // Test if all modals exist
    const modals = [
        'platformCustomizationModal',
        'securityManagementModal',
        'financialOverviewModal',
        'helpersManagementModal',
        'stageManagementModal'
    ];

    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) {
            console.log(`✓ ${modalId} found`);
        } else {
            console.error(`✗ ${modalId} not found`);
        }
    });
}

// Test function for debugging
function testModal(modalName) {
    console.log(`Testing ${modalName} modal`);
    switch(modalName) {
        case 'platform':
            showPlatformCustomization();
            break;
        case 'security':
            showSecurityManagement();
            break;
        case 'financial':
            showFinancialOverview();
            break;
        case 'helpers':
            showHelpersManagement();
            break;
        case 'stage':
            showStageManagement();
            break;
        default:
            console.log('Unknown modal:', modalName);
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    ensureModalFunctions();
    console.log('Page loaded, all functions should be available');

    // Add event listeners as backup for onclick attributes
    const platformBtn = document.getElementById('platformCustomizationBtn');
    if (platformBtn) {
        platformBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showPlatformCustomization();
        });
    }



    const securityBtn = document.getElementById('securityManagementBtn');
    if (securityBtn) {
        securityBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showSecurityManagement();
        });
    }

    const helpersBtn = document.getElementById('helpersManagementBtn');
    if (helpersBtn) {
        helpersBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showHelpersManagement();
        });
    }

    const financialBtn = document.getElementById('financialOverviewBtn');
    if (financialBtn) {
        financialBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showFinancialOverview();
        });
    }

    const stageBtn = document.getElementById('stageManagementBtn');
    if (stageBtn) {
        stageBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showStageManagement();
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>