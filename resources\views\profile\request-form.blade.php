@extends('layouts.app')

@section('title', 'Request Profile Information')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                @if($user->profile_picture)
                    <img src="{{ $user->profile_picture }}" alt="{{ $user->name }}" class="w-16 h-16 rounded-full object-cover">
                @else
                    <span class="text-xl font-medium text-gray-600">{{ substr($user->name, 0, 1) }}</span>
                @endif
            </div>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Request Information from {{ $user->name }}</h1>
                <p class="text-gray-600">Request access to view specific profile details</p>
            </div>
        </div>
    </div>

    <!-- Request Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Profile Information Request</h2>
            <p class="text-sm text-gray-600 mt-1">Please explain why you need access to this information. Your request will be reviewed by an administrator.</p>
        </div>

        <form method="POST" action="{{ route('profile.request.submit', $user->id) }}" class="p-6 space-y-6">
            @csrf

            <!-- Explanation -->
            <div>
                <label for="explanation" class="block text-sm font-medium text-gray-700 mb-2">
                    Explanation <span class="text-red-500">*</span>
                </label>
                <textarea 
                    id="explanation" 
                    name="explanation" 
                    rows="4" 
                    required
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Please explain why you need access to this user's profile information. Be specific about your purpose and how you plan to use this information."
                >{{ old('explanation') }}</textarea>
                @error('explanation')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-xs text-gray-500">Minimum 50 characters required. Be clear and professional in your explanation.</p>
            </div>

            <!-- Requested Information -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    Requested Information <span class="text-red-500">*</span>
                </label>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="phone" 
                            name="requested_fields[]" 
                            value="phone"
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="phone" class="ml-3 text-sm text-gray-700">
                            <span class="font-medium">Phone Number</span>
                            <span class="text-gray-500">- Contact phone number</span>
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="email" 
                            name="requested_fields[]" 
                            value="email"
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="email" class="ml-3 text-sm text-gray-700">
                            <span class="font-medium">Email Address</span>
                            <span class="text-gray-500">- Primary email contact</span>
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="address" 
                            name="requested_fields[]" 
                            value="address"
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="address" class="ml-3 text-sm text-gray-700">
                            <span class="font-medium">Address</span>
                            <span class="text-gray-500">- Physical address information</span>
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="date_of_birth" 
                            name="requested_fields[]" 
                            value="date_of_birth"
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="date_of_birth" class="ml-3 text-sm text-gray-700">
                            <span class="font-medium">Date of Birth</span>
                            <span class="text-gray-500">- Birth date information</span>
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="bank_details" 
                            name="requested_fields[]" 
                            value="bank_details"
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="bank_details" class="ml-3 text-sm text-gray-700">
                            <span class="font-medium">Bank Details</span>
                            <span class="text-gray-500">- Banking information (requires special justification)</span>
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="referral_info" 
                            name="requested_fields[]" 
                            value="referral_info"
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        >
                        <label for="referral_info" class="ml-3 text-sm text-gray-700">
                            <span class="font-medium">Referral Information</span>
                            <span class="text-gray-500">- Referral code and statistics</span>
                        </label>
                    </div>
                </div>
                @error('requested_fields')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-xs text-gray-500">Select at least one type of information you need access to.</p>
            </div>

            <!-- Privacy Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Privacy & Security Notice</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>Your request will be reviewed by our admin team</li>
                                <li>The user will be notified of your request</li>
                                <li>Access is granted only for legitimate purposes</li>
                                <li>Misuse of personal information may result in account suspension</li>
                                <li>All requests are logged for security purposes</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Terms Agreement -->
            <div class="flex items-start">
                <input 
                    type="checkbox" 
                    id="agree_terms" 
                    name="agree_terms" 
                    required
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mt-1"
                >
                <label for="agree_terms" class="ml-3 text-sm text-gray-700">
                    I agree to use the requested information responsibly and in accordance with the platform's 
                    <a href="/privacy-policy" class="text-indigo-600 hover:text-indigo-800">Privacy Policy</a> 
                    and <a href="/terms-of-service" class="text-indigo-600 hover:text-indigo-800">Terms of Service</a>.
                    <span class="text-red-500">*</span>
                </label>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{{ route('profile.show', $user->id) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Back to Profile
                </a>
                <button type="submit" class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    Submit Request
                </button>
            </div>
        </form>
    </div>

    <!-- Previous Requests -->
    @if($previousRequests->count() > 0)
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Your Previous Requests</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                @foreach($previousRequests as $request)
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">Request #{{ $request->id }}</p>
                            <p class="text-xs text-gray-500">{{ $request->created_at->format('M j, Y H:i') }}</p>
                        </div>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium 
                            {{ $request->status === 'approved' ? 'bg-green-100 text-green-800' : 
                               ($request->status === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                            {{ ucfirst($request->status) }}
                        </span>
                    </div>
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">{{ Str::limit($request->explanation, 100) }}</p>
                        <p class="text-xs text-gray-500 mt-1">
                            Requested: {{ implode(', ', $request->requested_fields) }}
                        </p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const checkboxes = document.querySelectorAll('input[name="requested_fields[]"]');
    const explanation = document.getElementById('explanation');
    
    // Validate at least one checkbox is selected
    form.addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('input[name="requested_fields[]"]:checked');
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('Please select at least one type of information to request.');
            return false;
        }
        
        if (explanation.value.length < 50) {
            e.preventDefault();
            alert('Please provide a more detailed explanation (minimum 50 characters).');
            explanation.focus();
            return false;
        }
    });
    
    // Character counter for explanation
    const charCounter = document.createElement('div');
    charCounter.className = 'text-xs text-gray-500 mt-1';
    explanation.parentNode.appendChild(charCounter);
    
    function updateCharCounter() {
        const length = explanation.value.length;
        charCounter.textContent = `${length}/50 characters minimum`;
        charCounter.className = length >= 50 ? 'text-xs text-green-600 mt-1' : 'text-xs text-gray-500 mt-1';
    }
    
    explanation.addEventListener('input', updateCharCounter);
    updateCharCounter();
});
</script>
@endsection
