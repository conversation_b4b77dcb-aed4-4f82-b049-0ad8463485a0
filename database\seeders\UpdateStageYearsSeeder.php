<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MembershipStage;

class UpdateStageYearsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stages = [
            1 => ['min_years' => 1, 'max_years' => 10],
            2 => ['min_years' => 10, 'max_years' => 20],
            3 => ['min_years' => 20, 'max_years' => 30],
            4 => ['min_years' => 30, 'max_years' => 40],
            5 => ['min_years' => 40, 'max_years' => 50],
            6 => ['min_years' => 50, 'max_years' => null]
        ];

        foreach ($stages as $sortOrder => $data) {
            MembershipStage::where('sort_order', $sortOrder)->update($data);
        }

        $this->command->info('Updated stages with years requirements');
    }
}
