@extends('layouts.admin')

@section('title', 'User Account Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">User Account Management System</h1>
                <p class="text-gray-600 mt-1">Comprehensive user account management and administration</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportUserData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Users
                </button>
                <button onclick="createUser()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Create User
                </button>
            </div>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">12,847</h3>
                    <p class="text-sm text-gray-600">Total Users</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">9,234</h3>
                    <p class="text-sm text-gray-600">Active Users</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">247</h3>
                    <p class="text-sm text-gray-600">Pending Verification</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">1,456</h3>
                    <p class="text-sm text-gray-600">Light Members</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">89</h3>
                    <p class="text-sm text-gray-600">Suspended Users</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'accounts' }">
                <button @click="activeTab = 'accounts'" :class="activeTab === 'accounts' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Account Management
                </button>
                <button @click="activeTab = 'verification'" :class="activeTab === 'verification' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Email Verification
                </button>
                <button @click="activeTab = 'security'" :class="activeTab === 'security' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Security Management
                </button>
                <button @click="activeTab = 'bulk'" :class="activeTab === 'bulk' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Bulk Operations
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    User Analytics
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'accounts' }">
        <!-- Account Management Tab -->
        <div x-show="activeTab === 'accounts'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">User Account Management</h3>
                        <div class="flex items-center space-x-3">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">All Users</option>
                                <option value="active">Active Users</option>
                                <option value="pending">Pending Verification</option>
                                <option value="suspended">Suspended Users</option>
                                <option value="light_members">Light Members</option>
                                <option value="earth_friendly">Earth-Friendly Members</option>
                            </select>
                            <input type="text" placeholder="Search users..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" class="rounded border-gray-300">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Membership</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Divine Lights</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-600">JS</span>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">John Smith</div>
                                            <div class="text-sm text-gray-500"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Light Member</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-1">
                                        <!-- White Stars -->
                                        <svg class="w-3 h-3 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20" stroke="#d1d5db" stroke-width="1">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        <svg class="w-3 h-3 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20" stroke="#d1d5db" stroke-width="1">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        <span class="text-xs text-gray-500 ml-1">2/3</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="editUser(1)" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                        <button onclick="viewUser(1)" class="text-green-600 hover:text-green-900">View</button>
                                        <button onclick="suspendUser(1)" class="text-red-600 hover:text-red-900">Suspend</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Email Verification Tab -->
        <div x-show="activeTab === 'verification'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Email Verification Management</h3>
                        <button onclick="resendAllVerifications()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Resend All Pending
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Pending Verification Users -->
                        <div class="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-sm font-medium text-gray-600">SJ</span>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Sarah Johnson</h4>
                                        <p class="text-sm text-gray-600"><EMAIL></p>
                                        <p class="text-xs text-gray-500">Registered 2 days ago</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="resendVerification(2)" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">Resend Email</button>
                                    <button onclick="manualVerify(2)" class="text-green-600 hover:text-green-900 text-sm font-medium">Manual Verify</button>
                                    <button onclick="deleteUnverified(2)" class="text-red-600 hover:text-red-900 text-sm font-medium">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Management Tab -->
        <div x-show="activeTab === 'security'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Security Management</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Password Reset Requests -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Password Reset Requests</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Mike Davis</p>
                                        <p class="text-xs text-gray-500"><EMAIL></p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button onclick="approveReset(3)" class="text-green-600 hover:text-green-900 text-sm">Approve</button>
                                        <button onclick="denyReset(3)" class="text-red-600 hover:text-red-900 text-sm">Deny</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Suspicious Activity -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Suspicious Activity</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-red-50 rounded">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Multiple Login Attempts</p>
                                        <p class="text-xs text-gray-500"><EMAIL> - 15 failed attempts</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button onclick="investigateActivity(4)" class="text-indigo-600 hover:text-indigo-900 text-sm">Investigate</button>
                                        <button onclick="blockUser(4)" class="text-red-600 hover:text-red-900 text-sm">Block</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportUserData() {
    alert('Export user data functionality will be implemented');
}

function createUser() {
    alert('Create user functionality will be implemented');
}

function editUser(id) {
    alert(`Edit user ${id} functionality will be implemented`);
}

function viewUser(id) {
    alert(`View user ${id} functionality will be implemented`);
}

function suspendUser(id) {
    if (confirm('Are you sure you want to suspend this user?')) {
        alert(`Suspend user ${id} functionality will be implemented`);
    }
}

function resendAllVerifications() {
    if (confirm('Resend verification emails to all pending users?')) {
        alert('Resend all verifications functionality will be implemented');
    }
}

function resendVerification(id) {
    alert(`Resend verification for user ${id} functionality will be implemented`);
}

function manualVerify(id) {
    if (confirm('Manually verify this user?')) {
        alert(`Manual verify user ${id} functionality will be implemented`);
    }
}

function deleteUnverified(id) {
    if (confirm('Delete this unverified user?')) {
        alert(`Delete unverified user ${id} functionality will be implemented`);
    }
}

function approveReset(id) {
    if (confirm('Approve password reset request?')) {
        alert(`Approve reset for user ${id} functionality will be implemented`);
    }
}

function denyReset(id) {
    if (confirm('Deny password reset request?')) {
        alert(`Deny reset for user ${id} functionality will be implemented`);
    }
}

function investigateActivity(id) {
    alert(`Investigate activity for user ${id} functionality will be implemented`);
}

function blockUser(id) {
    if (confirm('Block this user due to suspicious activity?')) {
        alert(`Block user ${id} functionality will be implemented`);
    }
}
</script>
@endsection
