@extends('layouts.app')

@section('title', 'Membership Stages')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Membership Stages</h1>
        <p class="mt-2 text-gray-600">Progress through our six-stage referral system to unlock higher commissions</p>
    </div>

    <!-- Current Progress -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Your Progress</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h4 class="text-xl font-semibold text-gray-900">
                        @if($user->membership_tier === 'earthfriendly')
                            Earth-Friendly Member
                        @else
                            Light Member ({{ $user->activeStageActivations->count() }} stages)
                        @endif
                    </h4>
                    <p class="text-gray-600">{{ $stats['active_referrals'] }} active referrals</p>
                </div>
                <div class="text-right">
                    <p class="text-2xl font-bold text-green-600">${{ number_format($user->total_earnings, 2) }}</p>
                    <p class="text-sm text-gray-500">Total Earned</p>
                </div>
            </div>

            @if($stats['next_stage'])
            <div class="mt-6">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Progress to {{ $stats['next_stage']->name }}</span>
                    <span class="text-sm text-gray-500">{{ $stats['referrals_to_next_stage'] }} more referrals needed</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="bg-indigo-600 h-3 rounded-full transition-all duration-300" 
                         style="width: {{ min(100, ($stats['active_referrals'] / $stats['next_stage']->min_referrals) * 100) }}%"></div>
                </div>
                <p class="mt-2 text-sm text-gray-600">
                    {{ $stats['active_referrals'] }} / {{ $stats['next_stage']->min_referrals }} referrals
                </p>
            </div>
            @else
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">
                            Congratulations! You've reached the highest tier!
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>You're at the Elite level - the highest membership stage with maximum benefits.</p>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Membership Stages Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach($membershipStages as $stage)
        <div class="bg-white shadow rounded-lg overflow-hidden {{ $stage['is_current'] ? 'ring-2 ring-indigo-500' : '' }}">
            <!-- Stage Header -->
            <div class="px-6 py-4 {{ $stage['is_current'] ? 'bg-indigo-50 border-b border-indigo-200' : 'bg-gray-50 border-b border-gray-200' }}">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold {{ $stage['is_current'] ? 'text-indigo-900' : 'text-gray-900' }}">
                        {{ $stage['name'] }}
                    </h3>
                    <div class="flex space-x-2">
                        @if($stage['is_current'])
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                            Current
                        </span>
                        @elseif($stage['is_achieved'])
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Achieved
                        </span>
                        @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Locked
                        </span>
                        @endif
                    </div>
                </div>
                <p class="text-sm {{ $stage['is_current'] ? 'text-indigo-600' : 'text-gray-600' }} mt-1">
                    {{ $stage['requirements_text'] }}
                </p>
            </div>

            <!-- Stage Content -->
            <div class="px-6 py-4">
                <!-- Commission Rate -->
                <div class="text-center mb-4">
                    <p class="text-3xl font-bold {{ $stage['is_current'] ? 'text-indigo-600' : 'text-gray-900' }}">
                        ${{ number_format($stage['commission_rate'], 2) }}
                    </p>
                    <p class="text-sm text-gray-500">per referral</p>
                </div>

                <!-- Activation Bonus -->
                @if($stage['activation_bonus'] > 0)
                <div class="text-center mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p class="text-sm font-medium text-yellow-800">Activation Bonus</p>
                    <p class="text-lg font-bold text-yellow-900">${{ number_format($stage['activation_bonus'], 2) }}</p>
                </div>
                @endif

                <!-- Progress Bar (for non-current stages) -->
                @if(!$stage['is_current'] && !$stage['is_achieved'])
                <div class="mb-4">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-500">Progress</span>
                        <span class="text-xs text-gray-500">{{ number_format($stage['progress_percentage'], 1) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-indigo-600 h-2 rounded-full" style="width: {{ $stage['progress_percentage'] }}%"></div>
                    </div>
                </div>
                @endif

                <!-- Benefits List -->
                <div class="space-y-2">
                    <h4 class="text-sm font-medium text-gray-900">Benefits:</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        @foreach($stage['benefits'] as $benefit)
                        <li class="flex items-start">
                            <svg class="flex-shrink-0 h-4 w-4 text-green-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            {{ $benefit }}
                        </li>
                        @endforeach
                    </ul>
                </div>

                <!-- Description -->
                @if($stage['description'])
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <p class="text-sm text-gray-600">{{ $stage['description'] }}</p>
                </div>
                @endif
            </div>

            <!-- Stage Footer -->
            <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
                @if($stage['is_current'])
                <p class="text-sm text-center text-indigo-600 font-medium">Your current stage</p>
                @elseif($stage['is_achieved'])
                <p class="text-sm text-center text-green-600 font-medium">Stage completed</p>
                @else
                <p class="text-sm text-center text-gray-500">
                    {{ $stage['min_referrals'] - $stats['active_referrals'] }} more referrals to unlock
                </p>
                @endif
            </div>
        </div>
        @endforeach
    </div>

    <!-- Call to Action -->
    <div class="mt-12 bg-indigo-50 border border-indigo-200 rounded-lg p-6">
        <div class="text-center">
            <h3 class="text-lg font-medium text-indigo-900 mb-2">Ready to advance to the next stage?</h3>
            <p class="text-indigo-700 mb-4">
                Share your referral link and start earning higher commissions with each new member you bring in.
            </p>
            <div class="flex justify-center space-x-4">
                <a href="{{ route('referrals.index') }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Share Referral Link
                </a>
                <a href="{{ route('dashboard.referrals') }}" 
                   class="inline-flex items-center px-4 py-2 border border-indigo-300 text-sm font-medium rounded-md text-indigo-700 bg-white hover:bg-indigo-50">
                    View My Referrals
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
