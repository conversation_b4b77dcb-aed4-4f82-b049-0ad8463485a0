<?php $__env->startSection('title', 'Life Journey Activations'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Earth-Friendly Life Journey Activations</h1>
        <p class="mt-2 text-gray-600">
            <?php if($user->isEarthFriendlyMember()): ?>
                Welcome, Earth-Friendly Member! Choose any stage below to become a Light Member and start your sustainable living journey.
            <?php else: ?>
                Welcome, Light Member! You can switch between stages anytime to optimize your commission earnings.
            <?php endif; ?>
        </p>
    </div>

    <!-- Current Status -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Current Status</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <?php if($user->isEarthFriendlyMember()): ?>
                        <h4 class="text-xl font-semibold text-green-600">Earth-Friendly Member</h4>
                        <p class="text-gray-600">Free membership - Ready to activate your first stage!</p>
                    <?php else: ?>
                        <h4 class="text-xl font-semibold text-blue-600">Light Member</h4>
                        <p class="text-gray-600">
                            <?php echo e($user->activeStageActivations->count()); ?> Active Stages • <?php echo e($stats['active_referrals']); ?> active referrals
                        </p>
                        <?php if($user->light_member_activated_at): ?>
                        <p class="text-sm text-gray-500">Light member since <?php echo e($user->light_member_activated_at->format('M j, Y')); ?></p>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="text-right">
                    <p class="text-2xl font-bold text-green-600">$<?php echo e(number_format($user->total_earnings, 2)); ?></p>
                    <p class="text-sm text-gray-500">Total Earned</p>
                    <?php if($user->isLightMember()): ?>
                    <p class="text-sm text-blue-600 mt-1">
                        $<?php echo e(number_format($user->total_bonus_earnings ?? 0, 2)); ?> from bonuses
                    </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Stages -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <?php $__currentLoopData = $membershipStages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php
            $isActivated = $user->hasActivatedStage($stage->slug);
        ?>
        <div class="bg-white shadow rounded-lg overflow-hidden <?php echo e($isActivated ? 'ring-2 ring-green-500' : ''); ?>">
            <!-- Stage Header -->
            <div class="px-6 py-4 <?php echo e($isActivated ? 'bg-green-50 border-b border-green-200' : 'bg-gray-50 border-b border-gray-200'); ?>">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold <?php echo e($isActivated ? 'text-green-900' : 'text-gray-900'); ?>">
                        <?php echo e($stage->name); ?>

                    </h3>
                    <?php if($isActivated): ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Activated
                    </span>
                    <?php else: ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Available
                    </span>
                    <?php endif; ?>
                </div>
                <p class="text-sm <?php echo e($isActivated ? 'text-green-600' : 'text-gray-600'); ?> mt-1">
                    <?php if($stage->min_years && $stage->max_years): ?>
                        <?php echo e($stage->min_years); ?>-<?php echo e($stage->max_years); ?> years experience
                    <?php elseif($stage->min_years): ?>
                        <?php echo e($stage->min_years); ?>+ years experience
                    <?php else: ?>
                        No experience requirement
                    <?php endif; ?>
                </p>
            </div>

            <!-- Stage Content -->
            <div class="px-6 py-4">


                <!-- Activation Fee -->
                <div class="text-center mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p class="text-sm font-medium text-blue-800">Activation Fee</p>
                    <p class="text-lg font-bold text-blue-900">
                        <?php if($stage->activation_price > 0): ?>
                            $<?php echo e(number_format($stage->activation_price, 2)); ?>

                        <?php else: ?>
                            FREE
                        <?php endif; ?>
                    </p>
                    <p class="text-xs text-blue-600">Pledge your commitment</p>
                </div>



                <!-- Stage Information -->
                <div class="pt-4 border-t border-gray-200 space-y-3">
                    <?php if($stage->description): ?>
                    <div>
                        <h5 class="text-sm font-medium text-gray-900 mb-1">Description</h5>
                        <p class="text-sm text-gray-600"><?php echo e($stage->description); ?></p>
                    </div>
                    <?php endif; ?>

                    <!-- Stage Benefits -->
                    <?php if($stage->benefits && count($stage->benefits) > 0): ?>
                    <div>
                        <h5 class="text-sm font-medium text-gray-900 mb-2">Benefits</h5>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <?php $__currentLoopData = $stage->benefits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $benefit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="flex items-start">
                                    <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <?php echo e($benefit); ?>

                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Stage Footer -->
            <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
                <?php if($isActivated): ?>
                <div class="text-center">
                    <p class="text-sm text-green-600 font-medium mb-2">✓ Stage Activated</p>
                    <p class="text-xs text-gray-500 mb-3">Access to <?php echo e($stage->name); ?> member area</p>
                    <a href="<?php echo e(url('/stages/' . $stage->slug)); ?>"
                       class="inline-flex items-center px-4 py-2 border border-green-300 text-sm font-medium rounded text-green-700 bg-green-50 hover:bg-green-100">
                        Enter <?php echo e($stage->name); ?> Area
                    </a>
                </div>
                <?php else: ?>
                <form method="POST" action="<?php echo e(route('activations.activate')); ?>" class="text-center">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="stage" value="<?php echo e($stage->slug); ?>">
                    <?php if($user->isEarthFriendlyMember()): ?>
                    <button type="submit"
                            onclick="return confirm('Ready to become a Light Member? Activating <?php echo e($stage->name); ?> will unlock your member area and referral earning potential!')"
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <?php if($stage->activation_price > 0): ?>
                            Activate for $<?php echo e(number_format($stage->activation_price, 2)); ?>

                        <?php else: ?>
                            Join Free
                        <?php endif; ?>
                    </button>
                    <?php else: ?>
                    <button type="submit"
                            onclick="return confirm('Activate <?php echo e($stage->name); ?> stage? <?php if($stage->activation_price > 0): ?>This will cost $<?php echo e(number_format($stage->activation_price, 2)); ?>.<?php endif; ?>')"
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <?php if($stage->activation_price > 0): ?>
                            Activate for $<?php echo e(number_format($stage->activation_price, 2)); ?>

                        <?php else: ?>
                            Activate <?php echo e($stage->name); ?>

                        <?php endif; ?>
                    </button>
                    <?php endif; ?>
                </form>
                <?php endif; ?>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Important Notice -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">
                    <?php if($user->isEarthFriendlyMember()): ?>
                    Welcome to Earth-Friendly Membership!
                    <?php else: ?>
                    Light Member Benefits
                    <?php endif; ?>
                </h3>
                <div class="mt-2 text-sm text-green-700">
                    <?php if($user->isEarthFriendlyMember()): ?>
                    <p>As an Earth-Friendly member, you have free access to our community. Ready to start earning and access our full sustainable living resources? Activate any or all stages to become a Light Member!</p>
                    <ul class="mt-2 list-disc list-inside space-y-1">
                        <li><strong>Activate multiple stages</strong> - Each stage works independently</li>
                        <li><strong>Instant Light Member status</strong> - Access to all resources</li>
                        <li><strong>Earn from each activation</strong> - $5-$30 per referral activation</li>
                        <li><strong>Bonus opportunities</strong> - Milestone, time-based, and leaderboard bonuses</li>
                        <li><strong>Sustainable living journey</strong> - Resources to help you live better</li>
                    </ul>
                    <?php else: ?>
                    <p>As a Light Member, you can activate multiple stages simultaneously! Each activated stage earns you commissions when your referrals activate that same stage.</p>
                    <ul class="mt-2 list-disc list-inside space-y-1">
                        <li><strong>Multi-stage earnings</strong> - Earn from each activated stage independently</li>
                        <li><strong>Referral activation commissions</strong> - Earn when referrals activate your stages</li>
                        <li><strong>Milestone bonuses</strong> - Extra rewards at 10, 25, 50+ referrals</li>
                        <li><strong>Time-based bonuses</strong> - First 30 days and weekly streak rewards</li>
                        <li><strong>Flexible activation</strong> - Activate/deactivate stages anytime</li>
                    </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 text-center">
        <div class="flex justify-center space-x-4">
            <a href="<?php echo e(route('awareness.index')); ?>"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                Share Awareness Link
            </a>
            <a href="<?php echo e(route('awareness.index')); ?>"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                View My Awareness
            </a>
            <a href="<?php echo e(route('wallet.index')); ?>"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                View Wallet
            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/activations/index.blade.php ENDPATH**/ ?>