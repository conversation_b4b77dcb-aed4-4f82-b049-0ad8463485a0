<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use App\Models\Setting;

class PlatformCustomizationController extends Controller
{
    /**
     * Display the platform customization index page
     */
    public function index()
    {
        return view('admin.platform-customization.index');
    }

    /**
     * Display the platform customization dashboard
     */
    public function dashboard()
    {
        $settings = $this->getPlatformSettings();
        $themes = $this->getAvailableThemes();
        $fonts = $this->getAvailableFonts();

        return view('admin.platform-customization.dashboard', compact('settings', 'themes', 'fonts'));
    }

    /**
     * Display the branding customization page
     */
    public function branding()
    {
        return view('admin.platform-customization.branding');
    }

    /**
     * Display the appearance customization page
     */
    public function appearance()
    {
        return view('admin.platform-customization.appearance');
    }

    /**
     * Display the layout customization page
     */
    public function layout()
    {
        return view('admin.platform-customization.layout');
    }

    /**
     * Get platform settings
     */
    public function getPlatformSettings()
    {
        try {
            $settings = [
                'site_name' => Setting::getValue('site.name', 'Lestel Platform'),
                'site_description' => Setting::getValue('site.description', 'Earth-Friendly Life Journey Platform'),
                'site_logo' => Setting::getValue('site.logo', null),
                'site_favicon' => Setting::getValue('site.favicon', null),
                'primary_color' => Setting::getValue('theme.primary_color', '#3B82F6'),
                'secondary_color' => Setting::getValue('theme.secondary_color', '#10B981'),
                'accent_color' => Setting::getValue('theme.accent_color', '#F59E0B'),
                'font_family' => Setting::getValue('theme.font_family', 'Inter'),
                'enable_dark_mode' => Setting::getValue('theme.enable_dark_mode', false),
                'maintenance_mode' => Setting::getValue('site.maintenance_mode', false),
                'registration_enabled' => Setting::getValue('site.registration_enabled', true),
                'email_verification_required' => Setting::getValue('site.email_verification_required', true),
                'default_language' => Setting::getValue('site.default_language', 'en'),
                'default_currency' => Setting::getValue('site.default_currency', 'USD'),
                'timezone' => Setting::getValue('site.timezone', 'UTC'),
            ];

            return response()->json(['success' => true, 'data' => $settings]);
        } catch (\Exception $e) {
            Log::error('Error fetching platform settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching platform settings']);
        }
    }

    /**
     * Update platform settings
     */
    public function updatePlatformSettings(Request $request)
    {
        try {
            $validated = $request->validate([
                'site_name' => 'required|string|max:255',
                'site_description' => 'required|string|max:500',
                'primary_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'secondary_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'accent_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'font_family' => 'required|string|max:100',
                'enable_dark_mode' => 'boolean',
                'maintenance_mode' => 'boolean',
                'registration_enabled' => 'boolean',
                'email_verification_required' => 'boolean',
                'default_language' => 'required|string|max:5',
                'default_currency' => 'required|string|max:3',
                'timezone' => 'required|string|max:50',
            ]);

            DB::beginTransaction();

            foreach ($validated as $key => $value) {
                $settingKey = str_contains($key, 'color') || str_contains($key, 'font') || str_contains($key, 'dark_mode') 
                    ? "theme.{$key}" 
                    : "site.{$key}";
                
                Setting::setValue($settingKey, $value);
            }

            // Clear cache to apply changes
            Cache::forget('platform_settings');
            
            DB::commit();

            Log::channel('admin')->info('Platform settings updated', [
                'admin_id' => Auth::id(),
                'settings' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Platform settings updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating platform settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating platform settings']);
        }
    }

    /**
     * Upload logo
     */
    public function uploadLogo(Request $request)
    {
        try {
            $request->validate([
                'logo' => 'required|image|mimes:jpeg,png,jpg,svg|max:2048',
            ]);

            $file = $request->file('logo');
            $filename = 'logo_' . time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('public/branding', $filename);

            // Delete old logo if exists
            $oldLogo = Setting::getValue('site.logo');
            if ($oldLogo && Storage::exists($oldLogo)) {
                Storage::delete($oldLogo);
            }

            Setting::setValue('site.logo', $path);

            Log::channel('admin')->info('Logo uploaded', [
                'admin_id' => Auth::id(),
                'filename' => $filename,
            ]);

            return response()->json([
                'success' => true, 
                'message' => 'Logo uploaded successfully',
                'logo_url' => Storage::url($path)
            ]);
        } catch (\Exception $e) {
            Log::error('Error uploading logo: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error uploading logo']);
        }
    }

    /**
     * Get custom pages
     */
    public function getCustomPages()
    {
        try {
            // Get pages from settings for now
            $pages = Setting::getValue('custom_pages', []);
            return response()->json(['success' => true, 'data' => array_values($pages)]);
        } catch (\Exception $e) {
            Log::error('Error fetching custom pages: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching custom pages']);
        }
    }

    /**
     * Create custom page
     */
    public function createCustomPage(Request $request)
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'slug' => 'required|string|max:255|unique:custom_pages,slug',
                'content' => 'required|string',
                'meta_description' => 'nullable|string|max:160',
                'is_published' => 'boolean',
                'show_in_menu' => 'boolean',
                'menu_order' => 'nullable|integer|min:0',
            ]);

            // Store page data in settings for now
            Setting::setValue('custom_pages.' . $validated['slug'], [
                ...$validated,
                'created_by' => Auth::id(),
                'created_at' => now(),
            ]);

            Log::channel('admin')->info('Custom page created', [
                'admin_id' => Auth::id(),
                'slug' => $validated['slug'],
                'title' => $validated['title'],
            ]);

            return response()->json(['success' => true, 'message' => 'Custom page created successfully']);
        } catch (\Exception $e) {
            Log::error('Error creating custom page: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error creating custom page']);
        }
    }

    /**
     * Get widgets
     */
    public function getWidgets()
    {
        try {
            // Get widgets from settings for now
            $widgets = Setting::getValue('widgets', []);
            return response()->json(['success' => true, 'data' => $widgets]);
        } catch (\Exception $e) {
            Log::error('Error fetching widgets: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching widgets']);
        }
    }

    /**
     * Create widget
     */
    public function createWidget(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'type' => 'required|in:text,html,shortcode,banner,stats',
                'content' => 'required|string',
                'position' => 'required|in:header,footer,sidebar,dashboard,homepage',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
                'visibility_rules' => 'nullable|string',
            ]);

            // Store widget data in settings for now
            $widgets = Setting::getValue('widgets', []);
            $widgetId = uniqid('widget_');
            $widgets[$widgetId] = [
                ...$validated,
                'id' => $widgetId,
                'created_by' => Auth::id(),
                'created_at' => now(),
            ];
            Setting::setValue('widgets', $widgets);

            Log::channel('admin')->info('Widget created', [
                'admin_id' => Auth::id(),
                'widget_id' => $widgetId,
                'name' => $validated['name'],
            ]);

            return response()->json(['success' => true, 'message' => 'Widget created successfully']);
        } catch (\Exception $e) {
            Log::error('Error creating widget: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error creating widget']);
        }
    }

    /**
     * Get email templates
     */
    public function getEmailTemplates()
    {
        try {
            $templates = [
                'welcome_email' => Setting::getValue('email.welcome_template', $this->getDefaultWelcomeTemplate()),
                'activation_email' => Setting::getValue('email.activation_template', $this->getDefaultActivationTemplate()),
                'referral_email' => Setting::getValue('email.referral_template', $this->getDefaultReferralTemplate()),
                'withdrawal_email' => Setting::getValue('email.withdrawal_template', $this->getDefaultWithdrawalTemplate()),
                'points_email' => Setting::getValue('email.points_template', $this->getDefaultPointsTemplate()),
            ];

            return response()->json(['success' => true, 'data' => $templates]);
        } catch (\Exception $e) {
            Log::error('Error fetching email templates: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching email templates']);
        }
    }

    /**
     * Update email template
     */
    public function updateEmailTemplate(Request $request)
    {
        try {
            $validated = $request->validate([
                'template_type' => 'required|in:welcome_email,activation_email,referral_email,withdrawal_email,points_email',
                'subject' => 'required|string|max:255',
                'content' => 'required|string',
            ]);

            Setting::setValue("email.{$validated['template_type']}", [
                'subject' => $validated['subject'],
                'content' => $validated['content'],
                'updated_by' => Auth::id(),
                'updated_at' => now(),
            ]);

            Log::channel('admin')->info('Email template updated', [
                'admin_id' => Auth::id(),
                'template_type' => $validated['template_type'],
            ]);

            return response()->json(['success' => true, 'message' => 'Email template updated successfully']);
        } catch (\Exception $e) {
            Log::error('Error updating email template: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating email template']);
        }
    }

    /**
     * Get pricing plans
     */
    public function getPricingPlans()
    {
        try {
            $plans = [
                'stage_1_price' => Setting::getValue('pricing.stage_1', 25),
                'stage_2_price' => Setting::getValue('pricing.stage_2', 50),
                'stage_3_price' => Setting::getValue('pricing.stage_3', 100),
                'stage_4_price' => Setting::getValue('pricing.stage_4', 200),
                'stage_5_price' => Setting::getValue('pricing.stage_5', 500),
                'stage_6_price' => Setting::getValue('pricing.stage_6', 1000),
                'commission_rates' => [
                    'stage_1' => Setting::getValue('commission.stage_1', 15),
                    'stage_2' => Setting::getValue('commission.stage_2', 20),
                    'stage_3' => Setting::getValue('commission.stage_3', 25),
                    'stage_4' => Setting::getValue('commission.stage_4', 30),
                    'stage_5' => Setting::getValue('commission.stage_5', 35),
                    'stage_6' => Setting::getValue('commission.stage_6', 40),
                ],
                'admin_commission' => Setting::getValue('commission.admin_rate', 10),
                'withdrawal_fee' => Setting::getValue('fees.withdrawal_fee', 2.5),
                'minimum_withdrawal' => Setting::getValue('fees.minimum_withdrawal', 10),
            ];

            return response()->json(['success' => true, 'data' => $plans]);
        } catch (\Exception $e) {
            Log::error('Error fetching pricing plans: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching pricing plans']);
        }
    }

    /**
     * Update pricing plans
     */
    public function updatePricingPlans(Request $request)
    {
        try {
            $validated = $request->validate([
                'stage_1_price' => 'required|numeric|min:0',
                'stage_2_price' => 'required|numeric|min:0',
                'stage_3_price' => 'required|numeric|min:0',
                'stage_4_price' => 'required|numeric|min:0',
                'stage_5_price' => 'required|numeric|min:0',
                'stage_6_price' => 'required|numeric|min:0',
                'commission_rates' => 'required|array',
                'admin_commission' => 'required|numeric|min:0|max:100',
                'withdrawal_fee' => 'required|numeric|min:0',
                'minimum_withdrawal' => 'required|numeric|min:0',
            ]);

            DB::beginTransaction();

            // Update stage prices
            for ($i = 1; $i <= 6; $i++) {
                Setting::setValue("pricing.stage_{$i}", $validated["stage_{$i}_price"]);
                Setting::setValue("commission.stage_{$i}", $validated['commission_rates']["stage_{$i}"]);
            }

            // Update other settings
            Setting::setValue('commission.admin_rate', $validated['admin_commission']);
            Setting::setValue('fees.withdrawal_fee', $validated['withdrawal_fee']);
            Setting::setValue('fees.minimum_withdrawal', $validated['minimum_withdrawal']);

            DB::commit();

            Log::channel('admin')->info('Pricing plans updated', [
                'admin_id' => Auth::id(),
                'changes' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Pricing plans updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating pricing plans: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating pricing plans']);
        }
    }

    /**
     * Default email templates
     */
    private function getDefaultWelcomeTemplate()
    {
        return [
            'subject' => 'Welcome to {{site_name}}!',
            'content' => 'Dear {{user_name}}, welcome to our Earth-Friendly Life Journey platform...',
        ];
    }

    private function getDefaultActivationTemplate()
    {
        return [
            'subject' => 'Stage {{stage_number}} Activated Successfully!',
            'content' => 'Congratulations! You have successfully activated Stage {{stage_number}}...',
        ];
    }

    private function getDefaultReferralTemplate()
    {
        return [
            'subject' => 'New Referral Joined!',
            'content' => 'Great news! {{referral_name}} has joined through your referral link...',
        ];
    }

    private function getDefaultWithdrawalTemplate()
    {
        return [
            'subject' => 'Withdrawal Request {{status}}',
            'content' => 'Your withdrawal request for {{amount}} has been {{status}}...',
        ];
    }

    private function getDefaultPointsTemplate()
    {
        return [
            'subject' => 'Points Update - {{points}} Points {{action}}',
            'content' => 'Your points balance has been updated. {{points}} points have been {{action}}...',
        ];
    }

    /**
     * Get available themes
     */
    private function getAvailableThemes()
    {
        return [
            'default' => 'Default Theme',
            'dark' => 'Dark Theme',
            'minimal' => 'Minimal Theme',
            'corporate' => 'Corporate Theme',
            'creative' => 'Creative Theme',
        ];
    }

    /**
     * Get available fonts
     */
    private function getAvailableFonts()
    {
        return [
            'Inter' => 'Inter',
            'Roboto' => 'Roboto',
            'Open Sans' => 'Open Sans',
            'Lato' => 'Lato',
            'Montserrat' => 'Montserrat',
            'Poppins' => 'Poppins',
            'Source Sans Pro' => 'Source Sans Pro',
            'Nunito' => 'Nunito',
        ];
    }

    /**
     * Get branding settings
     */
    public function getBrandingSettings()
    {
        try {
            $settings = [
                'site_name' => Setting::getValue('site.name', 'Lestel Platform'),
                'site_tagline' => Setting::getValue('site.tagline', 'Earth-Friendly Life Journey Platform'),
                'site_logo' => Setting::getValue('site.logo', null),
                'site_favicon' => Setting::getValue('site.favicon', null),
                'company_description' => Setting::getValue('branding.company_description', ''),
                'contact_email' => Setting::getValue('branding.contact_email', ''),
                'support_email' => Setting::getValue('branding.support_email', ''),
                'social_media' => Setting::getValue('branding.social_media', []),
                'seo_title' => Setting::getValue('seo.title', ''),
                'seo_description' => Setting::getValue('seo.description', ''),
                'seo_keywords' => Setting::getValue('seo.keywords', ''),
            ];

            return response()->json(['success' => true, 'data' => $settings]);
        } catch (\Exception $e) {
            Log::error('Error fetching branding settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch branding settings'], 500);
        }
    }

    /**
     * Get appearance settings
     */
    public function getAppearanceSettings()
    {
        try {
            $settings = [
                'primary_color' => Setting::getValue('theme.primary_color', '#4F46E5'),
                'secondary_color' => Setting::getValue('theme.secondary_color', '#10B981'),
                'accent_color' => Setting::getValue('theme.accent_color', '#F59E0B'),
                'background_color' => Setting::getValue('theme.background_color', '#FFFFFF'),
                'text_color' => Setting::getValue('theme.text_color', '#1F2937'),
                'current_theme' => Setting::getValue('theme.current', 'default'),
                'primary_font' => Setting::getValue('typography.primary_font', 'Inter'),
                'heading_font' => Setting::getValue('typography.heading_font', 'Inter'),
                'font_size' => Setting::getValue('typography.font_size', '16'),
                'animations_enabled' => Setting::getValue('appearance.animations_enabled', true),
                'dark_mode_enabled' => Setting::getValue('appearance.dark_mode_enabled', false),
            ];

            return response()->json(['success' => true, 'data' => $settings]);
        } catch (\Exception $e) {
            Log::error('Error fetching appearance settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch appearance settings'], 500);
        }
    }

    /**
     * Update appearance settings
     */
    public function updateAppearanceSettings(Request $request)
    {
        try {
            $validated = $request->validate([
                'primary_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'secondary_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'accent_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'background_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'text_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'primary_font' => 'nullable|string|max:50',
                'heading_font' => 'nullable|string|max:50',
                'font_size' => 'nullable|integer|min:12|max:24',
                'animations_enabled' => 'boolean',
                'dark_mode_enabled' => 'boolean',
            ]);

            DB::beginTransaction();

            foreach ($validated as $key => $value) {
                if (in_array($key, ['primary_color', 'secondary_color', 'accent_color', 'background_color', 'text_color'])) {
                    Setting::setValue("theme.{$key}", $value);
                } elseif (in_array($key, ['primary_font', 'heading_font', 'font_size'])) {
                    Setting::setValue("typography.{$key}", $value);
                } else {
                    Setting::setValue("appearance.{$key}", $value);
                }
            }

            DB::commit();

            Log::channel('admin')->info('Appearance settings updated', [
                'admin_id' => Auth::id(),
                'settings' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Appearance settings updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating appearance settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update appearance settings'], 500);
        }
    }

    /**
     * Update content settings
     */
    public function updateContentSettings(Request $request)
    {
        try {
            $validated = $request->validate([
                'homepage_hero_title' => 'nullable|string|max:255',
                'homepage_hero_subtitle' => 'nullable|string|max:1000',
                'welcome_message' => 'nullable|string|max:1000',
                'footer_description' => 'nullable|string|max:1000',
                'contact_email' => 'nullable|email|max:255',
                'support_email' => 'nullable|email|max:255',
                'privacy_policy_url' => 'nullable|url|max:255',
                'terms_of_service_url' => 'nullable|url|max:255',
            ]);

            DB::beginTransaction();

            foreach ($validated as $key => $value) {
                Setting::setValue("content.{$key}", $value);
            }

            DB::commit();

            Log::channel('admin')->info('Content settings updated', [
                'admin_id' => Auth::id(),
                'settings' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Content settings updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating content settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update content settings'], 500);
        }
    }

    /**
     * Update theme
     */
    public function updateTheme(Request $request)
    {
        try {
            $validated = $request->validate([
                'theme' => 'required|string|in:default,dark,minimal,corporate,creative',
            ]);

            Setting::setValue('theme.current', $validated['theme']);

            Log::channel('admin')->info('Theme updated', [
                'admin_id' => Auth::id(),
                'theme' => $validated['theme'],
            ]);

            return response()->json(['success' => true, 'message' => 'Theme updated successfully']);
        } catch (\Exception $e) {
            Log::error('Error updating theme: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update theme'], 500);
        }
    }

    /**
     * Update branding settings
     */
    public function updateBranding(Request $request)
    {
        try {
            $validated = $request->validate([
                'site_name' => 'nullable|string|max:255',
                'site_tagline' => 'nullable|string|max:255',
                'company_description' => 'nullable|string|max:1000',
                'contact_email' => 'nullable|email|max:255',
                'support_email' => 'nullable|email|max:255',
                'social_media' => 'nullable|array',
                'seo_title' => 'nullable|string|max:255',
                'seo_description' => 'nullable|string|max:255',
                'seo_keywords' => 'nullable|string|max:255',
            ]);

            DB::beginTransaction();

            foreach ($validated as $key => $value) {
                if (in_array($key, ['site_name', 'site_tagline'])) {
                    Setting::setValue("site.{$key}", $value);
                } elseif (in_array($key, ['seo_title', 'seo_description', 'seo_keywords'])) {
                    Setting::setValue("seo.{$key}", $value);
                } else {
                    Setting::setValue("branding.{$key}", $value);
                }
            }

            DB::commit();

            Log::channel('admin')->info('Branding settings updated', [
                'admin_id' => Auth::id(),
                'settings' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Branding settings updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating branding settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update branding settings'], 500);
        }
    }

    /**
     * Update colors
     */
    public function updateColors(Request $request)
    {
        try {
            $validated = $request->validate([
                'primary_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'secondary_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'accent_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'background_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'text_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            ]);

            DB::beginTransaction();

            foreach ($validated as $key => $value) {
                Setting::setValue("theme.{$key}", $value);
            }

            DB::commit();

            Log::channel('admin')->info('Color settings updated', [
                'admin_id' => Auth::id(),
                'settings' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Color settings updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating color settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update color settings'], 500);
        }
    }

    /**
     * Update typography
     */
    public function updateTypography(Request $request)
    {
        try {
            $validated = $request->validate([
                'primary_font' => 'nullable|string|max:50',
                'heading_font' => 'nullable|string|max:50',
                'font_size' => 'nullable|integer|min:12|max:24',
                'line_height' => 'nullable|numeric|min:1|max:3',
                'letter_spacing' => 'nullable|numeric|min:-0.1|max:0.2',
            ]);

            DB::beginTransaction();

            foreach ($validated as $key => $value) {
                Setting::setValue("typography.{$key}", $value);
            }

            DB::commit();

            Log::channel('admin')->info('Typography settings updated', [
                'admin_id' => Auth::id(),
                'settings' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Typography settings updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating typography settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update typography settings'], 500);
        }
    }

    /**
     * Update layout settings
     */
    public function updateLayout(Request $request)
    {
        try {
            $validated = $request->validate([
                'layout_type' => 'nullable|string|in:full-width,boxed,fluid',
                'max_width' => 'nullable|string|in:1200,1400,1600,full',
                'content_padding' => 'nullable|integer|min:0|max:50',
                'sticky_header' => 'boolean',
                'header_height' => 'nullable|integer|min:60|max:120',
                'show_search_bar' => 'boolean',
                'show_user_menu' => 'boolean',
                'show_footer' => 'boolean',
                'sticky_footer' => 'boolean',
                'footer_columns' => 'nullable|integer|min:1|max:4',
            ]);

            DB::beginTransaction();

            foreach ($validated as $key => $value) {
                Setting::setValue("layout.{$key}", $value);
            }

            DB::commit();

            Log::channel('admin')->info('Layout settings updated', [
                'admin_id' => Auth::id(),
                'settings' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Layout settings updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating layout settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update layout settings'], 500);
        }
    }

    /**
     * Reset section to defaults
     */
    public function resetSection(Request $request, $section)
    {
        try {
            $validSections = ['branding', 'appearance', 'layout', 'content', 'features'];

            if (!in_array($section, $validSections)) {
                return response()->json(['success' => false, 'message' => 'Invalid section'], 400);
            }

            DB::beginTransaction();

            // Reset section-specific settings to defaults
            switch ($section) {
                case 'branding':
                    Setting::setValue('site.name', 'Lestel Platform');
                    Setting::setValue('site.tagline', 'Earth-Friendly Life Journey Platform');
                    Setting::setValue('branding.company_description', '');
                    Setting::setValue('branding.contact_email', '');
                    Setting::setValue('branding.support_email', '');
                    break;
                case 'appearance':
                    Setting::setValue('theme.primary_color', '#4F46E5');
                    Setting::setValue('theme.secondary_color', '#10B981');
                    Setting::setValue('theme.accent_color', '#F59E0B');
                    Setting::setValue('theme.background_color', '#FFFFFF');
                    Setting::setValue('theme.text_color', '#1F2937');
                    Setting::setValue('theme.current', 'default');
                    Setting::setValue('typography.primary_font', 'Inter');
                    Setting::setValue('typography.heading_font', 'Inter');
                    break;
                case 'layout':
                    Setting::setValue('layout.sidebar_position', 'left');
                    Setting::setValue('layout.sticky_navigation', true);
                    Setting::setValue('layout.breadcrumbs_enabled', true);
                    break;
                case 'content':
                    Setting::setValue('content.homepage_hero_title', 'Welcome to Earth-Friendly Platform');
                    Setting::setValue('content.homepage_hero_subtitle', 'Join our community of environmentally conscious individuals');
                    Setting::setValue('content.welcome_message', '');
                    break;
                case 'features':
                    Setting::setValue('features.user_registration', true);
                    Setting::setValue('features.email_verification', true);
                    Setting::setValue('features.referral_system', true);
                    Setting::setValue('features.wallet_system', true);
                    break;
            }

            DB::commit();

            Log::channel('admin')->info('Section reset to defaults', [
                'admin_id' => Auth::id(),
                'section' => $section,
            ]);

            return response()->json(['success' => true, 'message' => ucfirst($section) . ' settings reset to defaults']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error resetting section: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to reset section'], 500);
        }
    }
}
