<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use App\Models\Setting;
use App\Models\CustomPage;
use App\Models\Widget;
use App\Models\Theme;

class PlatformCustomizationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Get platform settings
     */
    public function getPlatformSettings()
    {
        try {
            $settings = [
                'site_name' => Setting::getValue('site.name', 'Lestel Platform'),
                'site_description' => Setting::getValue('site.description', 'Earth-Friendly Life Journey Platform'),
                'site_logo' => Setting::getValue('site.logo', null),
                'site_favicon' => Setting::getValue('site.favicon', null),
                'primary_color' => Setting::getValue('theme.primary_color', '#3B82F6'),
                'secondary_color' => Setting::getValue('theme.secondary_color', '#10B981'),
                'accent_color' => Setting::getValue('theme.accent_color', '#F59E0B'),
                'font_family' => Setting::getValue('theme.font_family', 'Inter'),
                'enable_dark_mode' => Setting::getValue('theme.enable_dark_mode', false),
                'maintenance_mode' => Setting::getValue('site.maintenance_mode', false),
                'registration_enabled' => Setting::getValue('site.registration_enabled', true),
                'email_verification_required' => Setting::getValue('site.email_verification_required', true),
                'default_language' => Setting::getValue('site.default_language', 'en'),
                'default_currency' => Setting::getValue('site.default_currency', 'USD'),
                'timezone' => Setting::getValue('site.timezone', 'UTC'),
            ];

            return response()->json(['success' => true, 'data' => $settings]);
        } catch (\Exception $e) {
            Log::error('Error fetching platform settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching platform settings']);
        }
    }

    /**
     * Update platform settings
     */
    public function updatePlatformSettings(Request $request)
    {
        try {
            $validated = $request->validate([
                'site_name' => 'required|string|max:255',
                'site_description' => 'required|string|max:500',
                'primary_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'secondary_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'accent_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'font_family' => 'required|string|max:100',
                'enable_dark_mode' => 'boolean',
                'maintenance_mode' => 'boolean',
                'registration_enabled' => 'boolean',
                'email_verification_required' => 'boolean',
                'default_language' => 'required|string|max:5',
                'default_currency' => 'required|string|max:3',
                'timezone' => 'required|string|max:50',
            ]);

            DB::beginTransaction();

            foreach ($validated as $key => $value) {
                $settingKey = str_contains($key, 'color') || str_contains($key, 'font') || str_contains($key, 'dark_mode') 
                    ? "theme.{$key}" 
                    : "site.{$key}";
                
                Setting::setValue($settingKey, $value);
            }

            // Clear cache to apply changes
            Cache::forget('platform_settings');
            
            DB::commit();

            Log::channel('admin')->info('Platform settings updated', [
                'admin_id' => auth()->id(),
                'settings' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Platform settings updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating platform settings: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating platform settings']);
        }
    }

    /**
     * Upload logo
     */
    public function uploadLogo(Request $request)
    {
        try {
            $request->validate([
                'logo' => 'required|image|mimes:jpeg,png,jpg,svg|max:2048',
            ]);

            $file = $request->file('logo');
            $filename = 'logo_' . time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('public/branding', $filename);

            // Delete old logo if exists
            $oldLogo = Setting::getValue('site.logo');
            if ($oldLogo && Storage::exists($oldLogo)) {
                Storage::delete($oldLogo);
            }

            Setting::setValue('site.logo', $path);

            Log::channel('admin')->info('Logo uploaded', [
                'admin_id' => auth()->id(),
                'filename' => $filename,
            ]);

            return response()->json([
                'success' => true, 
                'message' => 'Logo uploaded successfully',
                'logo_url' => Storage::url($path)
            ]);
        } catch (\Exception $e) {
            Log::error('Error uploading logo: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error uploading logo']);
        }
    }

    /**
     * Get custom pages
     */
    public function getCustomPages()
    {
        try {
            $pages = CustomPage::orderBy('title')->get();
            return response()->json(['success' => true, 'data' => $pages]);
        } catch (\Exception $e) {
            Log::error('Error fetching custom pages: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching custom pages']);
        }
    }

    /**
     * Create custom page
     */
    public function createCustomPage(Request $request)
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'slug' => 'required|string|max:255|unique:custom_pages,slug',
                'content' => 'required|string',
                'meta_description' => 'nullable|string|max:160',
                'is_published' => 'boolean',
                'show_in_menu' => 'boolean',
                'menu_order' => 'nullable|integer|min:0',
            ]);

            $page = CustomPage::create([
                ...$validated,
                'created_by' => auth()->id(),
            ]);

            Log::channel('admin')->info('Custom page created', [
                'admin_id' => auth()->id(),
                'page_id' => $page->id,
                'title' => $page->title,
            ]);

            return response()->json(['success' => true, 'message' => 'Custom page created successfully']);
        } catch (\Exception $e) {
            Log::error('Error creating custom page: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error creating custom page']);
        }
    }

    /**
     * Get widgets
     */
    public function getWidgets()
    {
        try {
            $widgets = Widget::orderBy('position')->get();
            return response()->json(['success' => true, 'data' => $widgets]);
        } catch (\Exception $e) {
            Log::error('Error fetching widgets: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching widgets']);
        }
    }

    /**
     * Create widget
     */
    public function createWidget(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'type' => 'required|in:text,html,shortcode,banner,stats',
                'content' => 'required|string',
                'position' => 'required|in:header,footer,sidebar,dashboard,homepage',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
                'visibility_rules' => 'nullable|string',
            ]);

            $widget = Widget::create([
                ...$validated,
                'created_by' => auth()->id(),
            ]);

            Log::channel('admin')->info('Widget created', [
                'admin_id' => auth()->id(),
                'widget_id' => $widget->id,
                'name' => $widget->name,
            ]);

            return response()->json(['success' => true, 'message' => 'Widget created successfully']);
        } catch (\Exception $e) {
            Log::error('Error creating widget: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error creating widget']);
        }
    }

    /**
     * Get email templates
     */
    public function getEmailTemplates()
    {
        try {
            $templates = [
                'welcome_email' => Setting::getValue('email.welcome_template', $this->getDefaultWelcomeTemplate()),
                'activation_email' => Setting::getValue('email.activation_template', $this->getDefaultActivationTemplate()),
                'referral_email' => Setting::getValue('email.referral_template', $this->getDefaultReferralTemplate()),
                'withdrawal_email' => Setting::getValue('email.withdrawal_template', $this->getDefaultWithdrawalTemplate()),
                'points_email' => Setting::getValue('email.points_template', $this->getDefaultPointsTemplate()),
            ];

            return response()->json(['success' => true, 'data' => $templates]);
        } catch (\Exception $e) {
            Log::error('Error fetching email templates: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching email templates']);
        }
    }

    /**
     * Update email template
     */
    public function updateEmailTemplate(Request $request)
    {
        try {
            $validated = $request->validate([
                'template_type' => 'required|in:welcome_email,activation_email,referral_email,withdrawal_email,points_email',
                'subject' => 'required|string|max:255',
                'content' => 'required|string',
            ]);

            Setting::setValue("email.{$validated['template_type']}", [
                'subject' => $validated['subject'],
                'content' => $validated['content'],
                'updated_by' => auth()->id(),
                'updated_at' => now(),
            ]);

            Log::channel('admin')->info('Email template updated', [
                'admin_id' => auth()->id(),
                'template_type' => $validated['template_type'],
            ]);

            return response()->json(['success' => true, 'message' => 'Email template updated successfully']);
        } catch (\Exception $e) {
            Log::error('Error updating email template: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating email template']);
        }
    }

    /**
     * Get pricing plans
     */
    public function getPricingPlans()
    {
        try {
            $plans = [
                'stage_1_price' => Setting::getValue('pricing.stage_1', 25),
                'stage_2_price' => Setting::getValue('pricing.stage_2', 50),
                'stage_3_price' => Setting::getValue('pricing.stage_3', 100),
                'stage_4_price' => Setting::getValue('pricing.stage_4', 200),
                'stage_5_price' => Setting::getValue('pricing.stage_5', 500),
                'stage_6_price' => Setting::getValue('pricing.stage_6', 1000),
                'commission_rates' => [
                    'stage_1' => Setting::getValue('commission.stage_1', 15),
                    'stage_2' => Setting::getValue('commission.stage_2', 20),
                    'stage_3' => Setting::getValue('commission.stage_3', 25),
                    'stage_4' => Setting::getValue('commission.stage_4', 30),
                    'stage_5' => Setting::getValue('commission.stage_5', 35),
                    'stage_6' => Setting::getValue('commission.stage_6', 40),
                ],
                'admin_commission' => Setting::getValue('commission.admin_rate', 10),
                'withdrawal_fee' => Setting::getValue('fees.withdrawal_fee', 2.5),
                'minimum_withdrawal' => Setting::getValue('fees.minimum_withdrawal', 10),
            ];

            return response()->json(['success' => true, 'data' => $plans]);
        } catch (\Exception $e) {
            Log::error('Error fetching pricing plans: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching pricing plans']);
        }
    }

    /**
     * Update pricing plans
     */
    public function updatePricingPlans(Request $request)
    {
        try {
            $validated = $request->validate([
                'stage_1_price' => 'required|numeric|min:0',
                'stage_2_price' => 'required|numeric|min:0',
                'stage_3_price' => 'required|numeric|min:0',
                'stage_4_price' => 'required|numeric|min:0',
                'stage_5_price' => 'required|numeric|min:0',
                'stage_6_price' => 'required|numeric|min:0',
                'commission_rates' => 'required|array',
                'admin_commission' => 'required|numeric|min:0|max:100',
                'withdrawal_fee' => 'required|numeric|min:0',
                'minimum_withdrawal' => 'required|numeric|min:0',
            ]);

            DB::beginTransaction();

            // Update stage prices
            for ($i = 1; $i <= 6; $i++) {
                Setting::setValue("pricing.stage_{$i}", $validated["stage_{$i}_price"]);
                Setting::setValue("commission.stage_{$i}", $validated['commission_rates']["stage_{$i}"]);
            }

            // Update other settings
            Setting::setValue('commission.admin_rate', $validated['admin_commission']);
            Setting::setValue('fees.withdrawal_fee', $validated['withdrawal_fee']);
            Setting::setValue('fees.minimum_withdrawal', $validated['minimum_withdrawal']);

            DB::commit();

            Log::channel('admin')->info('Pricing plans updated', [
                'admin_id' => auth()->id(),
                'changes' => $validated,
            ]);

            return response()->json(['success' => true, 'message' => 'Pricing plans updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating pricing plans: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating pricing plans']);
        }
    }

    /**
     * Default email templates
     */
    private function getDefaultWelcomeTemplate()
    {
        return [
            'subject' => 'Welcome to {{site_name}}!',
            'content' => 'Dear {{user_name}}, welcome to our Earth-Friendly Life Journey platform...',
        ];
    }

    private function getDefaultActivationTemplate()
    {
        return [
            'subject' => 'Stage {{stage_number}} Activated Successfully!',
            'content' => 'Congratulations! You have successfully activated Stage {{stage_number}}...',
        ];
    }

    private function getDefaultReferralTemplate()
    {
        return [
            'subject' => 'New Referral Joined!',
            'content' => 'Great news! {{referral_name}} has joined through your referral link...',
        ];
    }

    private function getDefaultWithdrawalTemplate()
    {
        return [
            'subject' => 'Withdrawal Request {{status}}',
            'content' => 'Your withdrawal request for {{amount}} has been {{status}}...',
        ];
    }

    private function getDefaultPointsTemplate()
    {
        return [
            'subject' => 'Points Update - {{points}} Points {{action}}',
            'content' => 'Your points balance has been updated. {{points}} points have been {{action}}...',
        ];
    }
}
