<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Achievement extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'badge_color',
        'type',
        'requirements',
        'reward_amount',
        'reward_points',
        'is_active',
        'is_repeatable',
        'sort_order',
    ];

    protected $casts = [
        'requirements' => 'array',
        'reward_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'is_repeatable' => 'boolean',
    ];

    /**
     * Get the user achievements.
     */
    public function userAchievements()
    {
        return $this->hasMany(UserAchievement::class);
    }

    /**
     * Scope for active achievements.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Check if user has achieved this.
     */
    public function hasUserAchieved($userId)
    {
        return $this->userAchievements()->where('user_id', $userId)->exists();
    }

    /**
     * Get default achievements.
     */
    public static function getDefaults()
    {
        return [
            [
                'name' => 'Millionaire',
                'slug' => 'millionaire',
                'description' => 'Earn $1,000,000 in total earnings across all currencies',
                'icon' => '💰',
                'badge_color' => '#FFD700',
                'type' => 'earnings',
                'requirements' => ['amount' => 1000000],
                'reward_amount' => 10000.00,
                'reward_points' => 50000,
                'sort_order' => 1,
            ],
            [
                'name' => 'First Steps',
                'slug' => 'first-steps',
                'description' => 'Complete your first stage activation',
                'icon' => '🚀',
                'badge_color' => '#10B981',
                'type' => 'activities',
                'requirements' => ['stage_activations' => 1],
                'reward_amount' => 50.00,
                'reward_points' => 1000,
                'sort_order' => 2,
            ],
            [
                'name' => 'Referral Master',
                'slug' => 'referral-master',
                'description' => 'Refer 100 new members to the platform',
                'icon' => '👥',
                'badge_color' => '#3B82F6',
                'type' => 'referrals',
                'requirements' => ['referrals' => 100],
                'reward_amount' => 500.00,
                'reward_points' => 10000,
                'sort_order' => 3,
            ],
        ];
    }

    /**
     * Automatically generate slug when creating.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($achievement) {
            if (empty($achievement->slug)) {
                $achievement->slug = Str::slug($achievement->name);
            }
        });
    }
}
