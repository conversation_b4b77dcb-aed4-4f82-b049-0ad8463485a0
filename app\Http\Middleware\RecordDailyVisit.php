<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\PointsService;
use Symfony\Component\HttpFoundation\Response;

class RecordDailyVisit
{
    protected $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Record daily visit for authenticated users
        if (Auth::check()) {
            try {
                $this->pointsService->recordDailyVisit(Auth::user());
            } catch (\Exception $e) {
                // Log error but don't break the request
                \Log::error('Failed to record daily visit: ' . $e->getMessage());
            }
        }

        return $next($request);
    }
}
