@extends('layouts.admin')

@section('title', 'System Maintenance')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">System Maintenance</h1>
                <p class="text-gray-600 mt-1">Complete website maintenance and management tools</p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="flex items-center">
                    <span class="text-sm text-gray-500 mr-2">Maintenance Mode:</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer" id="maintenanceToggle" onchange="toggleMaintenanceMode()">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance Status Alert -->
    <div id="maintenanceAlert" class="hidden mb-6 bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Website Under Maintenance</h3>
                <p class="text-sm text-red-700 mt-1">The website is currently in maintenance mode. Only administrators can access the site.</p>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">System Status</h3>
                    <p class="text-sm text-green-600">Operational</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Last Backup</h3>
                    <p class="text-sm text-gray-600">2 hours ago</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Log Files</h3>
                    <p class="text-sm text-gray-600">2.4 MB</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Database Size</h3>
                    <p class="text-sm text-gray-600">156.7 MB</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'backup' }">
                <button @click="activeTab = 'backup'" :class="activeTab === 'backup' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Backup & Restore
                </button>
                <button @click="activeTab = 'logs'" :class="activeTab === 'logs' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    System Logs
                </button>
                <button @click="activeTab = 'data'" :class="activeTab === 'data' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Data Management
                </button>
                <button @click="activeTab = 'reset'" :class="activeTab === 'reset' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Reset & Delete
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'backup' }">
        <!-- Backup & Restore Tab -->
        <div x-show="activeTab === 'backup'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Backup & Restore Management</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Create Backup -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Create Backup</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Database</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">User Files</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">System Configuration</span>
                                    </label>
                                </div>
                                <button onclick="createBackup()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    Create Full Backup
                                </button>
                            </div>
                        </div>

                        <!-- Backup History -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Recent Backups</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Full Backup</p>
                                        <p class="text-xs text-gray-500">2024-06-23 14:30:00</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button onclick="downloadBackup('backup_20240623_1430')" class="text-blue-600 hover:text-blue-900 text-sm">Download</button>
                                        <button onclick="restoreBackup('backup_20240623_1430')" class="text-green-600 hover:text-green-900 text-sm">Restore</button>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Database Only</p>
                                        <p class="text-xs text-gray-500">2024-06-23 12:00:00</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button onclick="downloadBackup('backup_20240623_1200')" class="text-blue-600 hover:text-blue-900 text-sm">Download</button>
                                        <button onclick="restoreBackup('backup_20240623_1200')" class="text-green-600 hover:text-green-900 text-sm">Restore</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Logs Tab -->
        <div x-show="activeTab === 'logs'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">System Logs</h3>
                        <div class="flex items-center space-x-3">
                            <button onclick="refreshLogs()" class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Refresh
                            </button>
                            <button onclick="clearLogs()" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                Clear Logs
                            </button>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="bg-gray-900 rounded-lg p-4 h-96 overflow-y-auto">
                        <div class="text-green-400 font-mono text-sm space-y-1">
                            <div>[2024-06-23 14:35:22] INFO: User login successful - <EMAIL></div>
                            <div>[2024-06-23 14:34:15] INFO: Backup created successfully</div>
                            <div>[2024-06-23 14:30:45] INFO: System maintenance mode disabled</div>
                            <div>[2024-06-23 14:25:33] WARNING: High memory usage detected - 85%</div>
                            <div>[2024-06-23 14:20:12] INFO: Database optimization completed</div>
                            <div>[2024-06-23 14:15:08] INFO: Cache cleared successfully</div>
                            <div>[2024-06-23 14:10:55] ERROR: Failed login attempt - <EMAIL></div>
                            <div>[2024-06-23 14:05:42] INFO: User registration - <EMAIL></div>
                            <div>[2024-06-23 14:00:30] INFO: System startup completed</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Management Tab -->
        <div x-show="activeTab === 'data'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Data Import/Export</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Export Data -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Export Data</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Users Data</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Referral Data</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300" checked>
                                        <span class="ml-2 text-sm text-gray-700">Financial Data</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300">
                                        <span class="ml-2 text-sm text-gray-700">System Settings</span>
                                    </label>
                                </div>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="csv">CSV Format</option>
                                    <option value="json">JSON Format</option>
                                    <option value="excel">Excel Format</option>
                                </select>
                                <button onclick="exportData()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                    Export Selected Data
                                </button>
                            </div>
                        </div>

                        <!-- Import Data -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Import Data</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Select File</label>
                                    <input type="file" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Data Type</label>
                                    <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <option value="users">Users Data</option>
                                        <option value="referrals">Referral Data</option>
                                        <option value="financial">Financial Data</option>
                                        <option value="settings">System Settings</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300">
                                        <span class="ml-2 text-sm text-gray-700">Overwrite existing data</span>
                                    </label>
                                </div>
                                <button onclick="importData()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    Import Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reset & Delete Tab -->
        <div x-show="activeTab === 'reset'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Reset & Delete Operations</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Reset Website -->
                        <div class="border border-yellow-200 rounded-lg p-6 bg-yellow-50">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Reset Website</h4>
                            <p class="text-sm text-gray-600 mb-4">Reset website to default settings while keeping user data intact.</p>
                            <button onclick="resetWebsite()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700">
                                Reset Website Settings
                            </button>
                        </div>

                        <!-- Delete Website Data -->
                        <div class="border border-red-200 rounded-lg p-6 bg-red-50">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Delete Website Data</h4>
                            <p class="text-sm text-gray-600 mb-4">⚠️ <strong>WARNING:</strong> This will permanently delete all website data including users, transactions, and settings. This action cannot be undone.</p>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Type "DELETE ALL DATA" to confirm:</label>
                                    <input type="text" id="deleteConfirmation" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" placeholder="DELETE ALL DATA">
                                </div>
                                <button onclick="deleteWebsiteData()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                    Delete All Website Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleMaintenanceMode() {
    const toggle = document.getElementById('maintenanceToggle');
    const alert = document.getElementById('maintenanceAlert');
    
    if (toggle.checked) {
        alert.classList.remove('hidden');
        alert('Maintenance mode enabled. Website is now under maintenance.');
    } else {
        alert.classList.add('hidden');
        alert('Maintenance mode disabled. Website is now accessible to users.');
    }
}

function createBackup() {
    if (confirm('Create a full backup of the website?')) {
        alert('Backup creation started. You will be notified when complete.');
    }
}

function downloadBackup(backupId) {
    alert(`Downloading backup: ${backupId}`);
}

function restoreBackup(backupId) {
    if (confirm(`Restore backup: ${backupId}? This will overwrite current data.`)) {
        alert('Backup restoration started. Please wait...');
    }
}

function refreshLogs() {
    alert('Logs refreshed successfully.');
}

function clearLogs() {
    if (confirm('Clear all system logs? This action cannot be undone.')) {
        alert('System logs cleared successfully.');
    }
}

function exportData() {
    alert('Data export started. Download will begin shortly.');
}

function importData() {
    if (confirm('Import selected data? This may overwrite existing data.')) {
        alert('Data import started. Please wait...');
    }
}

function resetWebsite() {
    if (confirm('Reset website to default settings? User data will be preserved.')) {
        alert('Website reset initiated. Please wait...');
    }
}

function deleteWebsiteData() {
    const confirmation = document.getElementById('deleteConfirmation').value;
    if (confirmation !== 'DELETE ALL DATA') {
        alert('Please type "DELETE ALL DATA" to confirm this action.');
        return;
    }
    
    if (confirm('⚠️ FINAL WARNING: This will permanently delete ALL website data. This action CANNOT be undone. Are you absolutely sure?')) {
        alert('Website data deletion initiated. This process cannot be stopped once started.');
    }
}
</script>
@endsection
