<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->json('value')->nullable();
            $table->string('type')->default('string');
            $table->text('description')->nullable();
            $table->string('group')->nullable();
            $table->boolean('is_public')->default(false);
            $table->timestamps();

            $table->index(['key']);
            $table->index(['group']);
        });

        Schema::create('point_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('point_type');
            $table->integer('amount');
            $table->text('description')->nullable();
            $table->string('evidence_url')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('admin_notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status']);
            $table->index(['user_id', 'status']);
        });

        Schema::create('special_events', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('point_type');
            $table->integer('total_points');
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->integer('max_per_user')->nullable();
            $table->text('eligibility_criteria')->nullable();
            $table->enum('status', ['active', 'inactive', 'completed'])->default('active');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            $table->index(['status']);
            $table->index(['start_date', 'end_date']);
        });

        Schema::create('stage_resources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stage_id')->constrained('membership_stages')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['document', 'video', 'link', 'image']);
            $table->string('url')->nullable();
            $table->string('file_path')->nullable();
            $table->boolean('is_downloadable')->default(false);
            $table->integer('order')->default(0);
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            $table->index(['stage_id', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stage_resources');
        Schema::dropIfExists('special_events');
        Schema::dropIfExists('point_requests');
        Schema::dropIfExists('settings');
    }
};
