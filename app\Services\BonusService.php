<?php

namespace App\Services;

use App\Models\User;
use App\Models\BonusPayment;
use App\Models\MilestoneTracking;
use Carbon\Carbon;

class BonusService
{
    /**
     * Process all bonus types for a user.
     */
    public function processAllBonuses(User $user): void
    {
        $this->processMilestoneBonuses($user);
        $this->processTimeBasedBonuses($user);
        $this->processLeaderboardBonuses();
    }

    /**
     * Process milestone bonuses for a user.
     */
    public function processMilestoneBonuses(User $user): void
    {
        if (!$user->isLightMember()) {
            return;
        }

        $currentReferrals = $user->total_active_referrals;
        
        $pendingMilestones = $user->milestoneTracking()
            ->where('achieved', false)
            ->where('milestone_count', '<=', $currentReferrals)
            ->get();

        foreach ($pendingMilestones as $milestone) {
            $milestone->markAsAchieved();
        }
    }

    /**
     * Process time-based bonuses (e.g., first 30 days).
     */
    public function processTimeBasedBonuses(User $user): void
    {
        if (!$user->isLightMember()) {
            return;
        }

        // First 30 days bonus
        $this->processFirst30DaysBonus($user);
        
        // Weekly streak bonuses
        $this->processWeeklyStreakBonus($user);
    }

    /**
     * Process first 30 days bonus.
     */
    private function processFirst30DaysBonus(User $user): void
    {
        $activationDate = $user->light_member_activated_at;
        if (!$activationDate) {
            return;
        }

        $thirtyDaysLater = $activationDate->addDays(30);
        $now = now();

        // Check if we're still within the 30-day window
        if ($now->lessThan($thirtyDaysLater)) {
            $referralsInPeriod = $user->referrals()
                ->where('created_at', '>=', $activationDate)
                ->where('created_at', '<=', $now)
                ->where('is_active', true)
                ->count();

            // Award bonuses for different milestones within 30 days
            $this->awardFirst30DaysMilestones($user, $referralsInPeriod);
        }
    }

    /**
     * Award first 30 days milestone bonuses.
     */
    private function awardFirst30DaysMilestones(User $user, int $referrals): void
    {
        $milestones = [
            5 => 25.00,   // $25 for 5 referrals in first 30 days
            10 => 75.00,  // $75 for 10 referrals in first 30 days
            20 => 200.00, // $200 for 20 referrals in first 30 days
        ];

        foreach ($milestones as $count => $bonus) {
            if ($referrals >= $count) {
                $existingBonus = BonusPayment::where('user_id', $user->id)
                    ->where('bonus_type', 'time_based')
                    ->where('bonus_name', "First 30 Days - {$count} Referrals")
                    ->first();

                if (!$existingBonus) {
                    BonusPayment::create([
                        'user_id' => $user->id,
                        'bonus_type' => 'time_based',
                        'bonus_name' => "First 30 Days - {$count} Referrals",
                        'amount' => $bonus,
                        'description' => "Bonus for achieving {$count} referrals within first 30 days",
                        'criteria' => [
                            'referral_count' => $count,
                            'period' => 'first_30_days',
                            'activation_date' => $user->light_member_activated_at->toDateString(),
                        ],
                        'earned_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Process weekly streak bonuses.
     */
    private function processWeeklyStreakBonus(User $user): void
    {
        // Award bonus for getting referrals in consecutive weeks
        $weeklyStreak = $this->calculateWeeklyStreak($user);
        
        if ($weeklyStreak >= 4) { // 4 consecutive weeks
            $existingBonus = BonusPayment::where('user_id', $user->id)
                ->where('bonus_type', 'time_based')
                ->where('bonus_name', 'Weekly Streak - 4 Weeks')
                ->first();

            if (!$existingBonus) {
                BonusPayment::create([
                    'user_id' => $user->id,
                    'bonus_type' => 'time_based',
                    'bonus_name' => 'Weekly Streak - 4 Weeks',
                    'amount' => 100.00,
                    'description' => 'Bonus for maintaining referral activity for 4 consecutive weeks',
                    'criteria' => [
                        'streak_weeks' => $weeklyStreak,
                        'period' => 'weekly_streak',
                    ],
                    'earned_at' => now(),
                ]);
            }
        }
    }

    /**
     * Calculate weekly streak for a user.
     */
    private function calculateWeeklyStreak(User $user): int
    {
        $streak = 0;
        $currentWeek = now()->startOfWeek();
        
        for ($i = 0; $i < 12; $i++) { // Check last 12 weeks
            $weekStart = $currentWeek->copy()->subWeeks($i);
            $weekEnd = $weekStart->copy()->endOfWeek();
            
            $referralsThisWeek = $user->referrals()
                ->where('created_at', '>=', $weekStart)
                ->where('created_at', '<=', $weekEnd)
                ->where('is_active', true)
                ->count();
            
            if ($referralsThisWeek > 0) {
                $streak++;
            } else {
                break;
            }
        }
        
        return $streak;
    }

    /**
     * Process monthly leaderboard bonuses.
     */
    public function processLeaderboardBonuses(): void
    {
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();
        
        // Get top performers for the current month
        $topPerformers = User::where('membership_tier', 'light')
            ->withCount(['referrals as monthly_referrals' => function ($query) use ($currentMonth, $endOfMonth) {
                $query->where('created_at', '>=', $currentMonth)
                      ->where('created_at', '<=', $endOfMonth)
                      ->where('is_active', true);
            }])
            ->having('monthly_referrals', '>', 0)
            ->orderBy('monthly_referrals', 'desc')
            ->take(3)
            ->get();

        $prizes = [
            1 => 500.00, // 1st place
            2 => 300.00, // 2nd place
            3 => 200.00, // 3rd place
        ];

        foreach ($topPerformers as $index => $user) {
            $position = $index + 1;
            $prize = $prizes[$position] ?? 0;
            
            if ($prize > 0) {
                $existingBonus = BonusPayment::where('user_id', $user->id)
                    ->where('bonus_type', 'leaderboard')
                    ->where('bonus_name', "Monthly Top {$position}")
                    ->whereMonth('earned_at', $currentMonth->month)
                    ->whereYear('earned_at', $currentMonth->year)
                    ->first();

                if (!$existingBonus) {
                    BonusPayment::create([
                        'user_id' => $user->id,
                        'bonus_type' => 'leaderboard',
                        'bonus_name' => "Monthly Top {$position}",
                        'amount' => $prize,
                        'description' => "Monthly leaderboard bonus for {$position} place with {$user->monthly_referrals} referrals",
                        'criteria' => [
                            'position' => $position,
                            'referral_count' => $user->monthly_referrals,
                            'month' => $currentMonth->format('Y-m'),
                        ],
                        'earned_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Get bonus summary for a user.
     */
    public function getBonusSummary(User $user): array
    {
        return [
            'total_bonus_earnings' => $user->bonusPayments()->paid()->sum('amount'),
            'pending_bonus_earnings' => $user->bonusPayments()->pending()->sum('amount'),
            'milestone_bonuses' => $user->bonusPayments()->milestone()->count(),
            'time_based_bonuses' => $user->bonusPayments()->timeBased()->count(),
            'leaderboard_bonuses' => $user->bonusPayments()->leaderboard()->count(),
            'recent_bonuses' => $user->bonusPayments()->latest()->take(5)->get(),
        ];
    }
}
