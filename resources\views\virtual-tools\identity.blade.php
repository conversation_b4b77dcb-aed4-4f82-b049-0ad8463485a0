@extends('layouts.app')

@section('title', 'Virtual Identity')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 mb-8 text-white">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-4">🔐 Virtual Identity</h1>
            <p class="text-xl text-blue-100">Manage your digital identity and privacy with advanced tools</p>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-4 4-4-4 4-4 .257-.257A6 6 0 1118 8zm-6-2a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Secure Authentication</h3>
            <p class="text-gray-600 text-sm">Multi-factor authentication and biometric security</p>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Privacy Protection</h3>
            <p class="text-gray-600 text-sm">Advanced encryption and data protection</p>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Identity Management</h3>
            <p class="text-gray-600 text-sm">Centralized identity and credential management</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Identity Dashboard -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Identity Dashboard</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Primary Identity</h3>
                                <p class="text-sm text-gray-500">Verified and secure</p>
                            </div>
                        </div>
                        <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded">Active</span>
                    </div>

                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-4 4-4-4 4-4 .257-.257A6 6 0 1118 8zm-6-2a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Two-Factor Authentication</h3>
                                <p class="text-sm text-gray-500">Enhanced security enabled</p>
                            </div>
                        </div>
                        <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded">Enabled</span>
                    </div>

                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Backup Identity</h3>
                                <p class="text-sm text-gray-500">Setup recommended</p>
                            </div>
                        </div>
                        <span class="bg-yellow-100 text-yellow-800 text-sm font-medium px-3 py-1 rounded">Pending</span>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Security Settings</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Password Protection</h3>
                            <p class="text-sm text-gray-500">Strong password with regular updates</p>
                        </div>
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Update</button>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Biometric Authentication</h3>
                            <p class="text-sm text-gray-500">Fingerprint and face recognition</p>
                        </div>
                        <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">Enable</button>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900">Device Management</h3>
                            <p class="text-sm text-gray-500">Manage trusted devices</p>
                        </div>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Manage</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Security Score</h3>
                <div class="text-center">
                    <div class="text-4xl font-bold text-green-600 mb-2">85%</div>
                    <p class="text-sm text-gray-600">Your identity is well protected</p>
                </div>
                <div class="mt-4 space-y-2">
                    <div class="flex justify-between text-sm">
                        <span>Authentication</span>
                        <span class="text-green-600">Strong</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>Privacy</span>
                        <span class="text-green-600">High</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>Backup</span>
                        <span class="text-yellow-600">Needs Setup</span>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Recent Activity</h3>
                <div class="space-y-3">
                    <div class="flex items-center text-sm">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-gray-600">Successful login from Chrome</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-gray-600">Password updated</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                        <span class="text-gray-600">New device registered</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <button class="w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700">Generate New Key</button>
                    <button class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Backup Identity</button>
                    <button class="w-full bg-red-600 text-white py-2 rounded hover:bg-red-700">Emergency Reset</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
