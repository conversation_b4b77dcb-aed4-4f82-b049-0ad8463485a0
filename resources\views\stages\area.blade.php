@extends('layouts.app')

@section('title', $stage->name . ' Life Journey Stage')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $stage->name }} Life Journey Stage</h1>
                <p class="mt-2 text-gray-600">{{ $stage->description }}</p>
                @if(isset($analytics['overview']['stage_rank']))
                <p class="text-sm text-indigo-600 mt-1">You're ranked #{{ $analytics['overview']['stage_rank'] }} in this stage</p>
                @endif
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('stages.activities.index', $stage) }}"
                   class="inline-flex items-center px-4 py-2 border border-purple-300 text-sm font-medium rounded-md text-purple-700 bg-purple-50 hover:bg-purple-100">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Activities
                </a>
                <a href="{{ url('/stages/' . $stage->slug . '/resources') }}"
                   class="inline-flex items-center px-4 py-2 border border-indigo-300 text-sm font-medium rounded-md text-indigo-700 bg-indigo-50 hover:bg-indigo-100">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
                    </svg>
                    Resources
                </a>
                <a href="{{ url('/stages/' . $stage->slug . '/rewards') }}"
                   class="inline-flex items-center px-4 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-yellow-50 hover:bg-yellow-100">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    Rewards
                </a>
                <a href="{{ url('/stages/' . $stage->slug . '/community') }}"
                   class="inline-flex items-center px-4 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                    Community
                </a>
                <a href="{{ url('/activations') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Back to Stages
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Earned</dt>
                        <dd class="text-lg font-medium text-gray-900">
                            ${{ number_format($analytics['overview']['total_earned'] ?? 0, 2) }}
                        </dd>
                        @if(isset($analytics['earnings']['month_growth']) && $analytics['earnings']['month_growth'] != 0)
                        <dd class="text-xs {{ $analytics['earnings']['month_growth'] > 0 ? 'text-green-600' : 'text-red-600' }}">
                            {{ $analytics['earnings']['month_growth'] > 0 ? '+' : '' }}{{ number_format($analytics['earnings']['month_growth'], 1) }}% this month
                        </dd>
                        @endif
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Average Daily</dt>
                        <dd class="text-lg font-medium text-gray-900">
                            ${{ number_format($analytics['overview']['average_daily_earnings'] ?? 0, 2) }}
                        </dd>
                        @if(isset($analytics['earnings']['earning_streak']) && $analytics['earnings']['earning_streak'] > 0)
                        <dd class="text-xs text-blue-600">{{ $analytics['earnings']['earning_streak'] }} day streak</dd>
                        @endif
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Life Journey Connections</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $analytics['overview']['referrals_count'] ?? 0 }}</dd>
                        @if(isset($analytics['earnings']['this_month']) && $analytics['earnings']['this_month'] > 0)
                        <dd class="text-xs text-purple-600">${{ number_format($analytics['earnings']['this_month'], 2) }} this month</dd>
                        @endif
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Journey Days</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $analytics['overview']['days_active'] ?? 0 }}</dd>
                        <dd class="text-xs text-yellow-600">{{ $stage->name }} stage</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Goals -->
    @if(isset($analytics['progress']) && count($analytics['progress']) > 0)
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">{{ $stage->name }} Stage Goals</h3>
            <p class="text-sm text-gray-600">Track your progress on your life journey</p>
        </div>
        <div class="p-6">
            <div class="space-y-6">
                @foreach($analytics['progress'] as $goal)
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-900">{{ $goal['name'] }}</span>
                        <span class="text-sm text-gray-500">{{ number_format($goal['current']) }} / {{ number_format($goal['target']) }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="{{ $goal['status'] === 'completed' ? 'bg-green-600' : 'bg-indigo-600' }} h-2 rounded-full transition-all duration-300"
                             style="width: {{ min(100, $goal['percentage']) }}%"></div>
                    </div>
                    <div class="flex items-center justify-between mt-1">
                        <span class="text-xs text-gray-500">{{ number_format($goal['percentage'], 1) }}% complete</span>
                        @if($goal['status'] === 'completed')
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            ✓ Achieved
                        </span>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Insights -->
    @if(isset($analytics['insights']) && count($analytics['insights']) > 0)
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Personal Insights</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                @foreach($analytics['insights'] as $insight)
                <div class="flex items-start space-x-3 p-4 rounded-lg {{ $insight['type'] === 'positive' || $insight['type'] === 'success' ? 'bg-green-50 border border-green-200' : ($insight['type'] === 'suggestion' || $insight['type'] === 'tip' ? 'bg-blue-50 border border-blue-200' : 'bg-yellow-50 border border-yellow-200') }}">
                    <div class="flex-shrink-0">
                        @if($insight['type'] === 'positive' || $insight['type'] === 'success')
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        @elseif($insight['type'] === 'suggestion' || $insight['type'] === 'tip')
                        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                        @else
                        <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        @endif
                    </div>
                    <div>
                        <h4 class="text-sm font-medium {{ $insight['type'] === 'positive' || $insight['type'] === 'success' ? 'text-green-900' : ($insight['type'] === 'suggestion' || $insight['type'] === 'tip' ? 'text-blue-900' : 'text-yellow-900') }}">
                            {{ $insight['title'] }}
                        </h4>
                        <p class="text-sm {{ $insight['type'] === 'positive' || $insight['type'] === 'success' ? 'text-green-700' : ($insight['type'] === 'suggestion' || $insight['type'] === 'tip' ? 'text-blue-700' : 'text-yellow-700') }} mt-1">
                            {{ $insight['message'] }}
                        </p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Analytics Charts -->
        <div class="lg:col-span-2">
            <!-- Earnings Chart -->
            @if(isset($analytics['charts']['earnings_trend']) && count($analytics['charts']['earnings_trend']) > 0)
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Earnings Trend</h3>
                    <p class="text-sm text-gray-600">Your earnings progress over time</p>
                </div>
                <div class="p-6">
                    <div class="h-64 flex items-end justify-center space-x-4">
                        <div class="text-center">
                            <div class="w-16 h-32 bg-indigo-200 rounded-t flex items-end">
                                <div class="w-full bg-indigo-600 rounded-t" style="height: 60%"></div>
                            </div>
                            <span class="text-xs text-gray-500 mt-2">This Week</span>
                            <p class="text-sm font-medium">${{ number_format($analytics['earnings']['this_week'] ?? 0, 2) }}</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-32 bg-green-200 rounded-t flex items-end">
                                <div class="w-full bg-green-600 rounded-t" style="height: 80%"></div>
                            </div>
                            <span class="text-xs text-gray-500 mt-2">This Month</span>
                            <p class="text-sm font-medium">${{ number_format($analytics['earnings']['this_month'] ?? 0, 2) }}</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-32 bg-purple-200 rounded-t flex items-end">
                                <div class="w-full bg-purple-600 rounded-t" style="height: 100%"></div>
                            </div>
                            <span class="text-xs text-gray-500 mt-2">Total</span>
                            <p class="text-sm font-medium">${{ number_format($analytics['overview']['total_earned'] ?? 0, 2) }}</p>
                        </div>
                    </div>
                    @if(isset($analytics['earnings']['best_month']) && $analytics['earnings']['best_month']['amount'] > 0 && $analytics['earnings']['best_month']['month'] && $analytics['earnings']['best_month']['year'])
                    <div class="mt-4 text-center">
                        <p class="text-sm text-gray-600">
                            Best month: ${{ number_format($analytics['earnings']['best_month']['amount'], 2) }}
                            ({{ date('F Y', mktime(0, 0, 0, $analytics['earnings']['best_month']['month'], 1, $analytics['earnings']['best_month']['year'])) }})
                        </p>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Recent Activities -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Life Journey Activities</h3>
                    <p class="text-sm text-gray-600">Your recent progress and milestones</p>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Stage Activation -->
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm font-medium text-gray-900">{{ $stage->name }} Stage Activated</h4>
                                <p class="text-sm text-gray-600">Started your {{ strtolower($stage->name) }} life journey</p>
                                <div class="flex items-center justify-between mt-2">
                                    <span class="text-xs text-gray-500">{{ $activation->activated_at->format('M j, Y') }}</span>
                                    @if($analytics['overview']['activation_bonus'] > 0)
                                    <span class="text-sm font-medium text-green-600">+${{ number_format($analytics['overview']['activation_bonus'], 2) }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Progress Milestones -->
                        @if(isset($analytics['progress']))
                        @foreach($analytics['progress'] as $goal)
                        @if($goal['status'] === 'completed')
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm font-medium text-gray-900">{{ $goal['name'] }} Achieved</h4>
                                <p class="text-sm text-gray-600">Completed {{ strtolower($goal['name']) }} milestone</p>
                                <div class="flex items-center justify-between mt-2">
                                    <span class="text-xs text-gray-500">{{ number_format($goal['current']) }} / {{ number_format($goal['target']) }}</span>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        ✓ Complete
                                    </span>
                                </div>
                            </div>
                        </div>
                        @endif
                        @endforeach
                        @endif

                        <!-- Recent Earnings -->
                        @if(isset($analytics['earnings']['this_week']) && $analytics['earnings']['this_week'] > 0)
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm font-medium text-gray-900">Weekly Progress</h4>
                                <p class="text-sm text-gray-600">Earnings from life journey connections</p>
                                <div class="flex items-center justify-between mt-2">
                                    <span class="text-xs text-gray-500">This week</span>
                                    <span class="text-sm font-medium text-green-600">+${{ number_format($analytics['earnings']['this_week'], 2) }}</span>
                                </div>
                            </div>
                        </div>
                        @endif

                        @if(count($analytics['progress'] ?? []) === 0 && ($analytics['earnings']['this_week'] ?? 0) === 0)
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Start Your Journey</h3>
                            <p class="mt-1 text-sm text-gray-500">Begin connecting with others to track your progress here.</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Stage Members -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">{{ $stage->name }} Members</h3>
                </div>
                <div class="p-6">
                    @if($stageMembers->count() > 0)
                    <div class="space-y-4">
                        @foreach($stageMembers as $member)
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-indigo-600">{{ substr($member->user->name, 0, 1) }}</span>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{ $member->user->name }}</p>
                                <p class="text-xs text-gray-500">Joined {{ $member->activated_at->format('M j') }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    <div class="mt-6">
                        <a href="{{ route('stages.community', $stage->slug) }}" 
                           class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            View All Members
                        </a>
                    </div>
                    @else
                    <div class="text-center py-4">
                        <p class="text-sm text-gray-500">No other members yet</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Stage Info -->
            <div class="bg-white shadow rounded-lg mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Stage Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Commission Rate</span>
                        <span class="text-sm font-medium text-gray-900">${{ $stage->commission_rate }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Activation Bonus</span>
                        <span class="text-sm font-medium text-gray-900">${{ $stage->activation_bonus }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Activated On</span>
                        <span class="text-sm font-medium text-gray-900">{{ $activation->activated_at->format('M j, Y') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
