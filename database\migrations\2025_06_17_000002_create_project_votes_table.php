<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_votes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('community_projects')->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('vote_type', ['support', 'against'])->default('support');
            $table->text('comment')->nullable();
            $table->timestamps();
            
            $table->unique(['project_id', 'user_id']);
            $table->index(['project_id', 'vote_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_votes');
    }
};
