<?php

namespace App\Console\Commands;

use App\Models\Activity;
use App\Models\MembershipStage;
use Illuminate\Console\Command;

class CreateDefaultActivities extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'activities:create-defaults';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create default activities for all membership stages';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $stages = MembershipStage::all();
        
        if ($stages->isEmpty()) {
            $this->error('No membership stages found.');
            return 1;
        }

        $this->info('Creating default activities for all stages...');

        foreach ($stages as $stage) {
            // Check if activities already exist for this stage
            $existingActivities = Activity::where('stage_id', $stage->id)->count();
            
            if ($existingActivities > 0) {
                $this->warn("Stage '{$stage->name}' already has {$existingActivities} activities. Skipping...");
                continue;
            }

            Activity::createDefaultActivitiesForStage($stage);
            $this->info("✓ Created default activities for: {$stage->name}");
        }

        $this->info('Default activities creation completed!');
        return 0;
    }
}
