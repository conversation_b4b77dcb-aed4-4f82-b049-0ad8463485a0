@extends('layouts.app')

@section('title', 'Register')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <a href="{{ route('login') }}" class="font-medium text-indigo-600 hover:text-indigo-500">
                    sign in to your existing account
                </a>
            </p>
        </div>

        @if($referrer)
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">
                        You're being referred by {{ $referrer->name }}
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>Complete your registration to join their team and start earning commissions!</p>
                    </div>
                </div>
            </div>
        </div>
        @endif
        
        <form class="mt-8 space-y-6" method="POST" action="{{ route('register') }}">
            @csrf
            
            @if($referralCode)
                <input type="hidden" name="referral_code" value="{{ $referralCode }}">
            @endif
            
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input id="first_name" name="first_name" type="text" autocomplete="given-name" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('first_name') border-red-300 @enderror"
                               placeholder="Enter your first name" value="{{ old('first_name') }}">
                        @error('first_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input id="last_name" name="last_name" type="text" autocomplete="family-name" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('last_name') border-red-300 @enderror"
                               placeholder="Enter your last name" value="{{ old('last_name') }}">
                        @error('last_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Location Fields -->
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                    <input id="address" name="address" type="text" autocomplete="street-address"
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('address') border-red-300 @enderror"
                           placeholder="Enter your address" value="{{ old('address') }}">
                    @error('address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                        <input id="city" name="city" type="text" autocomplete="address-level2"
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('city') border-red-300 @enderror"
                               placeholder="Enter your city" value="{{ old('city') }}">
                        @error('city')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="state" class="block text-sm font-medium text-gray-700">State/Province</label>
                        <input id="state" name="state" type="text" autocomplete="address-level1"
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('state') border-red-300 @enderror"
                               placeholder="Enter your state/province" value="{{ old('state') }}">
                        @error('state')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="country" class="block text-sm font-medium text-gray-700">Country <span class="text-red-500">*</span></label>
                    <select id="country" name="country" autocomplete="country" required
                            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('country') border-red-300 @enderror">
                        <option value="">Select your country</option>
                        <option value="US" {{ old('country') === 'US' ? 'selected' : '' }}>United States</option>
                        <option value="CA" {{ old('country') === 'CA' ? 'selected' : '' }}>Canada</option>
                        <option value="GB" {{ old('country') === 'GB' ? 'selected' : '' }}>United Kingdom</option>
                        <option value="AU" {{ old('country') === 'AU' ? 'selected' : '' }}>Australia</option>
                        <option value="DE" {{ old('country') === 'DE' ? 'selected' : '' }}>Germany</option>
                        <option value="FR" {{ old('country') === 'FR' ? 'selected' : '' }}>France</option>
                        <option value="IT" {{ old('country') === 'IT' ? 'selected' : '' }}>Italy</option>
                        <option value="ES" {{ old('country') === 'ES' ? 'selected' : '' }}>Spain</option>
                        <option value="NL" {{ old('country') === 'NL' ? 'selected' : '' }}>Netherlands</option>
                        <option value="SE" {{ old('country') === 'SE' ? 'selected' : '' }}>Sweden</option>
                        <option value="NO" {{ old('country') === 'NO' ? 'selected' : '' }}>Norway</option>
                        <option value="DK" {{ old('country') === 'DK' ? 'selected' : '' }}>Denmark</option>
                        <option value="FI" {{ old('country') === 'FI' ? 'selected' : '' }}>Finland</option>
                        <option value="JP" {{ old('country') === 'JP' ? 'selected' : '' }}>Japan</option>
                        <option value="KR" {{ old('country') === 'KR' ? 'selected' : '' }}>South Korea</option>
                        <option value="SG" {{ old('country') === 'SG' ? 'selected' : '' }}>Singapore</option>
                        <option value="NZ" {{ old('country') === 'NZ' ? 'selected' : '' }}>New Zealand</option>
                        <option value="BR" {{ old('country') === 'BR' ? 'selected' : '' }}>Brazil</option>
                        <option value="MX" {{ old('country') === 'MX' ? 'selected' : '' }}>Mexico</option>
                        <option value="IN" {{ old('country') === 'IN' ? 'selected' : '' }}>India</option>
                        <option value="CN" {{ old('country') === 'CN' ? 'selected' : '' }}>China</option>
                        <option value="ZA" {{ old('country') === 'ZA' ? 'selected' : '' }}>South Africa</option>
                        <option value="NG" {{ old('country') === 'NG' ? 'selected' : '' }}>Nigeria</option>
                        <option value="KE" {{ old('country') === 'KE' ? 'selected' : '' }}>Kenya</option>
                        <option value="EG" {{ old('country') === 'EG' ? 'selected' : '' }}>Egypt</option>
                        <option value="AE" {{ old('country') === 'AE' ? 'selected' : '' }}>United Arab Emirates</option>
                        <option value="SA" {{ old('country') === 'SA' ? 'selected' : '' }}>Saudi Arabia</option>
                        <option value="TR" {{ old('country') === 'TR' ? 'selected' : '' }}>Turkey</option>
                        <option value="RU" {{ old('country') === 'RU' ? 'selected' : '' }}>Russia</option>
                        <option value="PL" {{ old('country') === 'PL' ? 'selected' : '' }}>Poland</option>
                        <option value="CZ" {{ old('country') === 'CZ' ? 'selected' : '' }}>Czech Republic</option>
                        <option value="HU" {{ old('country') === 'HU' ? 'selected' : '' }}>Hungary</option>
                        <option value="GR" {{ old('country') === 'GR' ? 'selected' : '' }}>Greece</option>
                        <option value="PT" {{ old('country') === 'PT' ? 'selected' : '' }}>Portugal</option>
                        <option value="IE" {{ old('country') === 'IE' ? 'selected' : '' }}>Ireland</option>
                        <option value="BE" {{ old('country') === 'BE' ? 'selected' : '' }}>Belgium</option>
                        <option value="CH" {{ old('country') === 'CH' ? 'selected' : '' }}>Switzerland</option>
                        <option value="AT" {{ old('country') === 'AT' ? 'selected' : '' }}>Austria</option>
                        <option value="OTHER" {{ old('country') === 'OTHER' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('country')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Date of Birth and Preferred Currency -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700">Date of Birth <span class="text-red-500">*</span></label>
                        <input id="date_of_birth" name="date_of_birth" type="date" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('date_of_birth') border-red-300 @enderror"
                               value="{{ old('date_of_birth') }}" max="{{ date('Y-m-d', strtotime('-13 years')) }}">
                        @error('date_of_birth')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">You must be at least 13 years old to register</p>
                    </div>

                    <div>
                        <label for="preferred_currency" class="block text-sm font-medium text-gray-700">Preferred Currency <span class="text-red-500">*</span></label>
                        <select id="preferred_currency" name="preferred_currency" required
                                class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('preferred_currency') border-red-300 @enderror">
                            <option value="">Select your preferred currency</option>
                            <option value="USD" {{ old('preferred_currency') === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                            <option value="EUR" {{ old('preferred_currency') === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                            <option value="GBP" {{ old('preferred_currency') === 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                            <option value="CAD" {{ old('preferred_currency') === 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                            <option value="AUD" {{ old('preferred_currency') === 'AUD' ? 'selected' : '' }}>AUD - Australian Dollar</option>
                            <option value="JPY" {{ old('preferred_currency') === 'JPY' ? 'selected' : '' }}>JPY - Japanese Yen</option>
                            <option value="CHF" {{ old('preferred_currency') === 'CHF' ? 'selected' : '' }}>CHF - Swiss Franc</option>
                            <option value="CNY" {{ old('preferred_currency') === 'CNY' ? 'selected' : '' }}>CNY - Chinese Yuan</option>
                            <option value="INR" {{ old('preferred_currency') === 'INR' ? 'selected' : '' }}>INR - Indian Rupee</option>
                            <option value="NGN" {{ old('preferred_currency') === 'NGN' ? 'selected' : '' }}>NGN - Nigerian Naira</option>
                            <option value="ZAR" {{ old('preferred_currency') === 'ZAR' ? 'selected' : '' }}>ZAR - South African Rand</option>
                            <option value="KES" {{ old('preferred_currency') === 'KES' ? 'selected' : '' }}>KES - Kenyan Shilling</option>
                            <option value="EGP" {{ old('preferred_currency') === 'EGP' ? 'selected' : '' }}>EGP - Egyptian Pound</option>
                            <option value="AED" {{ old('preferred_currency') === 'AED' ? 'selected' : '' }}>AED - UAE Dirham</option>
                            <option value="SAR" {{ old('preferred_currency') === 'SAR' ? 'selected' : '' }}>SAR - Saudi Riyal</option>
                            <option value="TRY" {{ old('preferred_currency') === 'TRY' ? 'selected' : '' }}>TRY - Turkish Lira</option>
                            <option value="RUB" {{ old('preferred_currency') === 'RUB' ? 'selected' : '' }}>RUB - Russian Ruble</option>
                            <option value="BRL" {{ old('preferred_currency') === 'BRL' ? 'selected' : '' }}>BRL - Brazilian Real</option>
                            <option value="MXN" {{ old('preferred_currency') === 'MXN' ? 'selected' : '' }}>MXN - Mexican Peso</option>
                            <option value="KRW" {{ old('preferred_currency') === 'KRW' ? 'selected' : '' }}>KRW - South Korean Won</option>
                            <option value="SGD" {{ old('preferred_currency') === 'SGD' ? 'selected' : '' }}>SGD - Singapore Dollar</option>
                            <option value="NZD" {{ old('preferred_currency') === 'NZD' ? 'selected' : '' }}>NZD - New Zealand Dollar</option>
                        </select>
                        @error('preferred_currency')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">This will be your default wallet currency</p>
                    </div>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email Address <span class="text-red-500">*</span></label>
                    <input id="email" name="email" type="email" autocomplete="email" required
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('email') border-red-300 @enderror"
                           placeholder="Enter your email address" value="{{ old('email') }}">
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="relative">
                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input id="password" name="password" type="password" autocomplete="new-password" required
                           class="mt-1 appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('password') border-red-300 @enderror"
                           placeholder="Create a password">
                    <button type="button" onclick="togglePassword('password')"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center mt-6">
                        <svg id="password-eye-open" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg id="password-eye-closed" class="h-5 w-5 text-gray-400 hover:text-gray-600 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                    </button>
                    @error('password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="relative">
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password" required
                           class="mt-1 appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           placeholder="Confirm your password">
                    <button type="button" onclick="togglePassword('password_confirmation')"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center mt-6">
                        <svg id="password_confirmation-eye-open" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg id="password_confirmation-eye-closed" class="h-5 w-5 text-gray-400 hover:text-gray-600 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                    </button>
                </div>

                @if(!$referralCode)
                <div>
                    <label for="referral_code_input" class="block text-sm font-medium text-gray-700">Referral Code (Optional)</label>
                    <input id="referral_code_input" name="referral_code" type="text" 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('referral_code') border-red-300 @enderror" 
                           placeholder="Enter referral code if you have one" value="{{ old('referral_code') }}">
                    @error('referral_code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                @endif
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Create Account
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function togglePassword(inputId) {
    const passwordInput = document.getElementById(inputId);
    const eyeOpen = document.getElementById(inputId + '-eye-open');
    const eyeClosed = document.getElementById(inputId + '-eye-closed');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeOpen.classList.add('hidden');
        eyeClosed.classList.remove('hidden');
    } else {
        passwordInput.type = 'password';
        eyeOpen.classList.remove('hidden');
        eyeClosed.classList.add('hidden');
    }
}
</script>
@endsection
