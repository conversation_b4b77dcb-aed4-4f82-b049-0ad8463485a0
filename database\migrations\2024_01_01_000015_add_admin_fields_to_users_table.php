<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_top_admin')->default(false)->after('is_admin');
            $table->enum('admin_status', ['pending', 'approved', 'rejected'])->nullable()->after('is_top_admin');
            $table->string('preferred_language', 5)->default('en')->after('admin_since');
        });

        // Make the first user the top admin
        if ($firstUser = \App\Models\User::first()) {
            $firstUser->update([
                'is_top_admin' => true,
                'admin_status' => 'approved',
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'is_top_admin',
                'admin_status',
                'preferred_language'
            ]);
        });
    }
};
