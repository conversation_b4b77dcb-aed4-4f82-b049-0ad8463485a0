<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MaintenanceController extends Controller
{
    /**
     * Show maintenance dashboard.
     */
    public function index()
    {
        if (!Auth::user() || !Auth::user()->is_top_admin) {
            abort(403, 'Only the Top Administrator can access maintenance features.');
        }

        $maintenanceInfo = $this->getMaintenanceInfo();
        
        return view('admin.maintenance.index', compact('maintenanceInfo'));
    }

    /**
     * Clear application cache.
     */
    public function clearCache(Request $request)
    {
        if (!Auth::user() || !Auth::user()->is_top_admin) {
            abort(403, 'Access denied.');
        }

        try {
            $cacheType = $request->input('type', 'all');
            $results = [];

            switch ($cacheType) {
                case 'application':
                    Artisan::call('cache:clear');
                    $results[] = 'Application cache cleared';
                    break;
                    
                case 'config':
                    Artisan::call('config:clear');
                    $results[] = 'Configuration cache cleared';
                    break;
                    
                case 'route':
                    Artisan::call('route:clear');
                    $results[] = 'Route cache cleared';
                    break;
                    
                case 'view':
                    Artisan::call('view:clear');
                    $results[] = 'View cache cleared';
                    break;
                    
                case 'all':
                default:
                    Artisan::call('cache:clear');
                    Artisan::call('config:clear');
                    Artisan::call('route:clear');
                    Artisan::call('view:clear');
                    $results[] = 'All caches cleared successfully';
                    break;
            }

            Log::info('Cache cleared by admin', [
                'admin_id' => Auth::id(),
                'cache_type' => $cacheType,
                'timestamp' => now()
            ]);

            return back()->with('success', implode(', ', $results));
            
        } catch (\Exception $e) {
            Log::error('Cache clear failed', [
                'error' => $e->getMessage(),
                'admin_id' => Auth::id()
            ]);
            
            return back()->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Clear error logs.
     */
    public function clearLogs(Request $request)
    {
        if (!Auth::user() || !Auth::user()->is_top_admin) {
            abort(403, 'Access denied.');
        }

        try {
            $logPath = storage_path('logs');
            $files = File::files($logPath);
            $clearedFiles = 0;

            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) === 'log') {
                    File::delete($file);
                    $clearedFiles++;
                }
            }

            Log::info('Error logs cleared by admin', [
                'admin_id' => Auth::id(),
                'files_cleared' => $clearedFiles,
                'timestamp' => now()
            ]);

            return back()->with('success', "Cleared {$clearedFiles} log file(s) successfully");
            
        } catch (\Exception $e) {
            Log::error('Log clear failed', [
                'error' => $e->getMessage(),
                'admin_id' => Auth::id()
            ]);
            
            return back()->with('error', 'Failed to clear logs: ' . $e->getMessage());
        }
    }

    /**
     * Optimize application.
     */
    public function optimize(Request $request)
    {
        if (!Auth::user() || !Auth::user()->is_top_admin) {
            abort(403, 'Access denied.');
        }

        try {
            $results = [];

            // Clear all caches first
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');
            $results[] = 'Caches cleared';

            // Optimize for production
            Artisan::call('config:cache');
            $results[] = 'Configuration cached';
            
            Artisan::call('route:cache');
            $results[] = 'Routes cached';
            
            Artisan::call('view:cache');
            $results[] = 'Views cached';

            // Run database optimization
            Artisan::call('optimize');
            $results[] = 'Application optimized';

            Log::info('Application optimized by admin', [
                'admin_id' => Auth::id(),
                'timestamp' => now()
            ]);

            return back()->with('success', 'Application optimized: ' . implode(', ', $results));
            
        } catch (\Exception $e) {
            Log::error('Optimization failed', [
                'error' => $e->getMessage(),
                'admin_id' => Auth::id()
            ]);
            
            return back()->with('error', 'Optimization failed: ' . $e->getMessage());
        }
    }

    /**
     * Run database maintenance.
     */
    public function databaseMaintenance(Request $request)
    {
        if (!Auth::user() || !Auth::user()->is_top_admin) {
            abort(403, 'Access denied.');
        }

        try {
            $action = $request->input('action');
            $results = [];

            switch ($action) {
                case 'migrate':
                    Artisan::call('migrate', ['--force' => true]);
                    $results[] = 'Database migrations executed';
                    break;
                    
                case 'seed':
                    Artisan::call('db:seed', ['--force' => true]);
                    $results[] = 'Database seeded';
                    break;
                    
                case 'optimize':
                    // Run database optimization queries
                    DB::statement('ANALYZE');
                    $results[] = 'Database analyzed and optimized';
                    break;
                    
                default:
                    return back()->with('error', 'Invalid database action');
            }

            Log::info('Database maintenance performed', [
                'admin_id' => Auth::id(),
                'action' => $action,
                'timestamp' => now()
            ]);

            return back()->with('success', implode(', ', $results));
            
        } catch (\Exception $e) {
            Log::error('Database maintenance failed', [
                'error' => $e->getMessage(),
                'admin_id' => Auth::id(),
                'action' => $request->input('action')
            ]);
            
            return back()->with('error', 'Database maintenance failed: ' . $e->getMessage());
        }
    }

    /**
     * Get maintenance information.
     */
    private function getMaintenanceInfo(): array
    {
        $logPath = storage_path('logs');
        $logFiles = File::files($logPath);
        $totalLogSize = 0;
        
        foreach ($logFiles as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'log') {
                $totalLogSize += File::size($file);
            }
        }

        return [
            'cache_size' => $this->getCacheSize(),
            'log_files_count' => count($logFiles),
            'log_files_size' => $this->formatBytes($totalLogSize),
            'storage_usage' => $this->getStorageUsage(),
            'database_size' => $this->getDatabaseSize(),
            'last_optimization' => Cache::get('last_optimization', 'Never'),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
        ];
    }

    /**
     * Get cache size.
     */
    private function getCacheSize(): string
    {
        $cachePath = storage_path('framework/cache');
        if (!File::exists($cachePath)) {
            return '0 B';
        }
        
        $size = 0;
        $files = File::allFiles($cachePath);
        
        foreach ($files as $file) {
            $size += File::size($file);
        }
        
        return $this->formatBytes($size);
    }

    /**
     * Get storage usage.
     */
    private function getStorageUsage(): string
    {
        $storagePath = storage_path();
        $size = 0;
        $files = File::allFiles($storagePath);
        
        foreach ($files as $file) {
            $size += File::size($file);
        }
        
        return $this->formatBytes($size);
    }

    /**
     * Get database size.
     */
    private function getDatabaseSize(): string
    {
        try {
            $databaseName = config('database.connections.sqlite.database');
            if (File::exists($databaseName)) {
                return $this->formatBytes(File::size($databaseName));
            }
            return 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2): string
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
