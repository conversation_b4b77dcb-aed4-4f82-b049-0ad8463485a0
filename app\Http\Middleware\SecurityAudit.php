<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SecurityAudit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log sensitive actions
        if (config('security.data_protection.log_sensitive_actions', true)) {
            $this->logSensitiveAction($request);
        }

        // Check for suspicious activity
        $this->checkSuspiciousActivity($request);

        $response = $next($request);

        // Log response for audit trail
        $this->logResponse($request, $response);

        return $response;
    }

    /**
     * Log sensitive actions
     */
    private function logSensitiveAction(Request $request): void
    {
        $sensitiveRoutes = [
            'admin.*',
            'wallet.*',
            'withdrawal.*',
            'points.*',
            'profile.update',
            'password.*',
            'two-factor.*'
        ];

        $currentRoute = $request->route() ? $request->route()->getName() : '';

        foreach ($sensitiveRoutes as $pattern) {
            if (fnmatch($pattern, $currentRoute)) {
                Log::channel('security')->info('Sensitive action accessed', [
                    'user_id' => Auth::id(),
                    'ip_address' => $this->getHashedIp($request),
                    'user_agent' => $request->userAgent(),
                    'route' => $currentRoute,
                    'method' => $request->method(),
                    'timestamp' => now(),
                    'session_id' => $request->session()->getId(),
                ]);
                break;
            }
        }
    }

    /**
     * Check for suspicious activity
     */
    private function checkSuspiciousActivity(Request $request): void
    {
        $suspiciousPatterns = [
            'sql injection' => ['union', 'select', 'drop', 'delete', 'insert', 'update', '--', ';'],
            'xss attempt' => ['<script', 'javascript:', 'onload=', 'onerror=', 'onclick='],
            'path traversal' => ['../', '..\\', '/etc/passwd', '/etc/shadow'],
            'command injection' => ['|', '&&', '||', ';', '`', '$(',],
        ];

        $requestData = array_merge(
            $request->all(),
            [$request->getRequestUri(), $request->userAgent()]
        );

        foreach ($requestData as $value) {
            if (is_string($value)) {
                $value = strtolower($value);
                
                foreach ($suspiciousPatterns as $type => $patterns) {
                    foreach ($patterns as $pattern) {
                        if (strpos($value, $pattern) !== false) {
                            Log::channel('security')->warning('Suspicious activity detected', [
                                'type' => $type,
                                'pattern' => $pattern,
                                'value' => $value,
                                'user_id' => Auth::id(),
                                'ip_address' => $this->getHashedIp($request),
                                'user_agent' => $request->userAgent(),
                                'timestamp' => now(),
                            ]);
                            
                            // Optionally block the request
                            if (config('security.monitoring.block_suspicious_requests', false)) {
                                abort(403, 'Suspicious activity detected');
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Log response for audit trail
     */
    private function logResponse(Request $request, Response $response): void
    {
        // Log failed authentication attempts
        if ($response->getStatusCode() === 401 || $response->getStatusCode() === 403) {
            Log::channel('security')->warning('Authentication/Authorization failure', [
                'status_code' => $response->getStatusCode(),
                'ip_address' => $this->getHashedIp($request),
                'user_agent' => $request->userAgent(),
                'route' => $request->route() ? $request->route()->getName() : '',
                'timestamp' => now(),
            ]);
        }

        // Log admin actions
        if (Auth::check() && Auth::user()->hasRole('admin')) {
            $adminRoutes = ['admin.*'];
            $currentRoute = $request->route() ? $request->route()->getName() : '';

            foreach ($adminRoutes as $pattern) {
                if (fnmatch($pattern, $currentRoute)) {
                    Log::channel('admin')->info('Admin action performed', [
                        'admin_id' => Auth::id(),
                        'admin_email' => Auth::user()->email,
                        'action' => $currentRoute,
                        'method' => $request->method(),
                        'ip_address' => $this->getHashedIp($request),
                        'timestamp' => now(),
                        'status_code' => $response->getStatusCode(),
                    ]);
                    break;
                }
            }
        }
    }

    /**
     * Get hashed IP address for privacy
     */
    private function getHashedIp(Request $request): string
    {
        if (config('security.data_protection.hash_user_ips', true)) {
            return hash('sha256', $request->ip() . config('app.key'));
        }
        
        return $request->ip();
    }
}
