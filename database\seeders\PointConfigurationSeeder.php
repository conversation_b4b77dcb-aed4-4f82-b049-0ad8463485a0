<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PointConfiguration;

class PointConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configurations = [
            [
                'source' => 'daily_visit',
                'name' => 'Daily Visit',
                'description' => 'Visit the platform daily to earn points',
                'points_awarded' => 10,
                'is_active' => true,
                'cash_conversion_rate' => 0.01, // 100 points = $1
            ],
            [
                'source' => 'project_completion',
                'name' => 'Project Completion',
                'description' => 'Complete a featured project successfully',
                'points_awarded' => 100,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ],
            [
                'source' => 'referral',
                'name' => 'Successful Referral',
                'description' => 'Refer a new user who activates a stage',
                'points_awarded' => 50,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ],
            [
                'source' => 'achievement',
                'name' => 'Achievement Unlocked',
                'description' => 'Unlock various platform achievements',
                'points_awarded' => 25,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ],
            [
                'source' => 'marketplace_purchase',
                'name' => 'Marketplace Purchase',
                'description' => 'Make a purchase in the marketplace',
                'points_awarded' => 20,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ],
            [
                'source' => 'affiliate_purchase',
                'name' => 'Affiliate Purchase',
                'description' => 'Generate an affiliate sale',
                'points_awarded' => 30,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ],
            [
                'source' => 'event_participation',
                'name' => 'Event Participation',
                'description' => 'Participate in community events',
                'points_awarded' => 15,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ],
            [
                'source' => 'resource_sharing',
                'name' => 'Resource Sharing',
                'description' => 'Share valuable resources with the community',
                'points_awarded' => 20,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ],
            [
                'source' => 'escrow_completion',
                'name' => 'Escrow Transaction',
                'description' => 'Complete an escrow transaction successfully',
                'points_awarded' => 40,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ],
            [
                'source' => 'stage_activation',
                'name' => 'Stage Activation',
                'description' => 'Activate a new membership stage',
                'points_awarded' => 75,
                'is_active' => true,
                'cash_conversion_rate' => 0.01,
            ]
        ];

        foreach ($configurations as $config) {
            PointConfiguration::updateOrCreate(
                ['source' => $config['source']],
                $config
            );
        }
    }
}
