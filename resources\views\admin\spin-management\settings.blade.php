@extends('layouts.admin')

@section('title', 'Spin System Settings')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ route('admin.spin-management.index') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="sr-only">Spin Management</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="text-3xl font-bold text-gray-900 mt-2">🎰 Spin System Settings</h1>
                <p class="text-gray-600 mt-1">Configure the lucky spin system parameters and prize delivery methods</p>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="bg-white shadow rounded-lg">
        <form action="{{ route('admin.spin-management.update-settings') }}" method="POST">
            @csrf
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Basic Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-6">⚙️ Basic Settings</h3>
                        
                        <div class="space-y-6">
                            <!-- System Status -->
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_active" value="1" {{ $settings->is_active ? 'checked' : '' }} class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                    <span class="ml-2 text-sm font-medium text-gray-700">Enable Spin System</span>
                                </label>
                                <p class="mt-1 text-sm text-gray-500">When disabled, users cannot access the spin feature</p>
                            </div>

                            <!-- Spins Required to Win -->
                            <div>
                                <label for="spins_required_to_win" class="block text-sm font-medium text-gray-700 mb-2">
                                    Spins Required for Guaranteed Win
                                </label>
                                <input type="number" 
                                       name="spins_required_to_win" 
                                       id="spins_required_to_win" 
                                       value="{{ $settings->spins_required_to_win }}" 
                                       min="1" 
                                       max="100" 
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <p class="mt-1 text-sm text-gray-500">Number of spins after which user is guaranteed to win a prize</p>
                            </div>

                            <!-- Spin Cost -->
                            <div>
                                <label for="spin_cost_points" class="block text-sm font-medium text-gray-700 mb-2">
                                    Extra Spin Cost (Points)
                                </label>
                                <input type="number" 
                                       name="spin_cost_points" 
                                       id="spin_cost_points" 
                                       value="{{ $settings->spin_cost_points }}" 
                                       min="1" 
                                       max="10000" 
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <p class="mt-1 text-sm text-gray-500">Points required for extra spins after the free daily spin</p>
                            </div>
                        </div>
                    </div>

                    <!-- Prize Delivery Methods -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-6">🚚 Prize Delivery Methods</h3>
                        
                        <div class="space-y-4">
                            <p class="text-sm text-gray-600 mb-4">Select the available methods for prize delivery:</p>
                            
                            @foreach($deliveryMethods as $method => $label)
                                <label class="flex items-center">
                                    <input type="checkbox" 
                                           name="prize_delivery_methods[]" 
                                           value="{{ $method }}" 
                                           {{ in_array($method, $settings->prize_delivery_methods ?? []) ? 'checked' : '' }}
                                           class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                    <span class="ml-3">
                                        <span class="text-sm font-medium text-gray-700">{{ $label }}</span>
                                        <span class="block text-xs text-gray-500">
                                            @switch($method)
                                                @case('events')
                                                    Prizes distributed at community events
                                                    @break
                                                @case('cash')
                                                    Direct cash payments to winners
                                                    @break
                                                @case('pickup_station')
                                                    Physical pickup at designated stations
                                                    @break
                                                @case('digital')
                                                    Digital delivery (vouchers, codes)
                                                    @break
                                                @case('instant')
                                                    Instant points added to user account
                                                    @break
                                            @endswitch
                                        </span>
                                    </span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                <a href="{{ route('admin.spin-management.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Dashboard
                </a>
                
                <div class="flex items-center space-x-3">
                    <button type="button" onclick="resetToDefaults()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Reset to Defaults
                    </button>
                    
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Save Settings
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-medium text-blue-900 mb-4">💡 Configuration Tips</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
            <div>
                <h4 class="font-medium mb-2">Spins to Win</h4>
                <ul class="space-y-1 text-blue-700">
                    <li>• Lower numbers = more frequent wins</li>
                    <li>• Higher numbers = more suspense</li>
                    <li>• Recommended: 8-15 spins</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">Spin Cost</h4>
                <ul class="space-y-1 text-blue-700">
                    <li>• Balance engagement vs. accessibility</li>
                    <li>• Consider your points economy</li>
                    <li>• Recommended: 50-200 points</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
        document.getElementById('spins_required_to_win').value = 10;
        document.getElementById('spin_cost_points').value = 100;
        
        // Reset checkboxes
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (checkbox.name === 'is_active') {
                checkbox.checked = true;
            } else if (checkbox.name === 'prize_delivery_methods[]') {
                checkbox.checked = true; // Enable all delivery methods by default
            }
        });
    }
}
</script>
@endsection
