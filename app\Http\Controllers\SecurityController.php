<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Setting;
use App\Models\SecurityLog;

class SecurityController extends Controller
{
    public function getSettings()
    {
        $keys = [
            'max_login_attempts', 'lockout_duration', 'session_timeout', 'require_2fa', 'password_min_length'
        ];
        $settings = [];
        foreach ($keys as $key) {
            $row = Setting::where('key', $key)->first();
            $settings[$key] = $row ? ($key === 'require_2fa' ? (bool)$row->value : (is_numeric($row->value) ? (int)$row->value : $row->value)) : ($key === 'require_2fa' ? false : null);
        }
        // Provide defaults if not set
        $settings = array_merge([
            'max_login_attempts' => 5,
            'lockout_duration' => 600,
            'session_timeout' => 1800,
            'require_2fa' => false,
            'password_min_length' => 8,
        ], array_filter($settings, fn($v) => $v !== null));
        return response()->json(['success' => true, 'data' => $settings]);
    }

    public function saveSettings(Request $request)
    {
        $data = $request->validate([
            'max_login_attempts' => 'required|integer|min:1|max:10',
            'lockout_duration' => 'required|integer|min:300|max:3600',
            'session_timeout' => 'required|integer|min:900|max:7200',
            'require_2fa' => 'required|boolean',
            'password_min_length' => 'required|integer|min:6|max:20',
        ]);
        foreach ($data as $key => $value) {
            Setting::updateOrCreate(['key' => $key], ['value' => $value]);
        }
        SecurityLog::create([
            'event' => 'Security settings updated',
            'type' => 'settings',
            'user_id' => auth()->id() ?? null,
            'meta' => json_encode($data),
        ]);
        return response()->json(['success' => true, 'message' => 'Settings saved']);
    }

    public function getLogs(Request $request)
    {
        $query = SecurityLog::query();
        if ($type = $request->input('type')) {
            $query->where('type', $type);
        }
        if ($date = $request->input('date')) {
            $query->whereDate('created_at', $date);
        }
        $logs = $query->orderByDesc('created_at')->paginate(2);
        return response()->json([
            'success' => true,
            'data' => $logs->items(),
            'current_page' => $logs->currentPage(),
            'last_page' => $logs->lastPage(),
        ]);
    }

    public function clearCache()
    {
        Artisan::call('cache:clear');
        return response()->json(['success' => true, 'message' => 'Cache cleared']);
    }

    public function optimizeSystem()
    {
        Artisan::call('optimize');
        return response()->json(['success' => true, 'message' => 'System optimized']);
    }

    public function getSystemInfo()
    {
        $info = [
            'php_version' => phpversion(),
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'N/A',
            'cache_driver' => config('cache.default'),
            'timezone' => config('app.timezone'),
        ];
        return response()->json(['success' => true, 'data' => $info]);
    }

    public function restartQueue()
    {
        // For demo, just return success. In real app, use Artisan::call('queue:restart')
        // Artisan::call('queue:restart');
        return response()->json(['success' => true, 'message' => 'Queue worker restarted']);
    }

    public function exportLogs(Request $request)
    {
        $query = SecurityLog::query();
        if ($type = $request->input('type')) {
            $query->where('type', $type);
        }
        if ($date = $request->input('date')) {
            $query->whereDate('created_at', $date);
        }
        $logs = $query->orderByDesc('created_at')->get();
        $csv = "Date,Event,Type,User,Meta\n";
        foreach ($logs as $log) {
            $csv .= sprintf('"%s","%s","%s","%s","%s"\n',
                $log->created_at,
                $log->event,
                $log->type,
                $log->user ? $log->user->name : '',
                $log->meta
            );
        }
        return response($csv)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="security_logs.csv"');
    }

    public function analytics()
    {
        $counts = [
            'login' => SecurityLog::where('type', 'login')->count(),
            'password' => SecurityLog::where('type', 'password')->count(),
            '2fa' => SecurityLog::where('type', '2fa')->count(),
        ];
        return response()->json(['success' => true, 'data' => $counts]);
    }
}
