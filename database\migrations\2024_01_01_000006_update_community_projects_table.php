<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('community_projects', function (Blueprint $table) {
            // Add new columns for enhanced project system (only if they don't exist)
            if (!Schema::hasColumn('community_projects', 'project_type_id')) {
                $table->foreignId('project_type_id')->nullable()->constrained('project_types');
            }
            if (!Schema::hasColumn('community_projects', 'funding_goal')) {
                $table->decimal('funding_goal', 15, 2)->nullable();
            }
            if (!Schema::hasColumn('community_projects', 'duration_days')) {
                $table->integer('duration_days')->nullable();
            }
            if (!Schema::hasColumn('community_projects', 'start_date')) {
                $table->date('start_date')->nullable();
            }
            if (!Schema::hasColumn('community_projects', 'end_date')) {
                $table->date('end_date')->nullable();
            }
            if (!Schema::hasColumn('community_projects', 'volunteer_slots')) {
                $table->integer('volunteer_slots')->nullable();
            }
            if (!Schema::hasColumn('community_projects', 'team_credits')) {
                $table->json('team_credits')->nullable(); // Store team member credits
            }
            if (!Schema::hasColumn('community_projects', 'completion_status')) {
                $table->enum('completion_status', ['not_started', 'in_progress', 'completed', 'overdue'])->default('not_started');
            }
            if (!Schema::hasColumn('community_projects', 'admin_review_notes')) {
                $table->text('admin_review_notes')->nullable();
            }
            if (!Schema::hasColumn('community_projects', 'completed_at')) {
                $table->timestamp('completed_at')->nullable();
            }
            if (!Schema::hasColumn('community_projects', 'auto_moved_to_closed')) {
                $table->boolean('auto_moved_to_closed')->default(false);
            }
        });
    }

    public function down()
    {
        Schema::table('community_projects', function (Blueprint $table) {
            $table->dropForeign(['project_type_id']);
            $table->dropColumn([
                'project_type_id', 'votes_count', 'votes_required', 'funding_goal',
                'duration_days', 'start_date', 'end_date', 'volunteer_slots',
                'volunteer_count', 'team_credits', 'credibility_score',
                'completion_status', 'admin_review_notes', 'featured_at',
                'completed_at', 'auto_moved_to_closed'
            ]);
        });
    }
};
