@extends('layouts.admin')

@section('title', 'Language Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Language Management System</h1>
                <p class="text-gray-600 mt-1">Comprehensive multilanguage support and translation management</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportTranslations()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Translations
                </button>
                <button onclick="addLanguage()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Add Language
                </button>
            </div>
        </div>
    </div>

    <!-- Language Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 12.236 11.618 14z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">12</h3>
                    <p class="text-sm text-gray-600">Active Languages</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">2,847</h3>
                    <p class="text-sm text-gray-600">Translation Keys</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">89%</h3>
                    <p class="text-sm text-gray-600">Completion Rate</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">247</h3>
                    <p class="text-sm text-gray-600">Pending Translations</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">12</h3>
                    <p class="text-sm text-gray-600">Missing Translations</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'languages' }">
                <button @click="activeTab = 'languages'" :class="activeTab === 'languages' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Language Management
                </button>
                <button @click="activeTab = 'translations'" :class="activeTab === 'translations' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Translation Management
                </button>
                <button @click="activeTab = 'import'" :class="activeTab === 'import' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Import/Export
                </button>
                <button @click="activeTab = 'settings'" :class="activeTab === 'settings' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Language Settings
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'languages' }">
        <!-- Language Management Tab -->
        <div x-show="activeTab === 'languages'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Supported Languages</h3>
                        <div class="flex items-center space-x-3">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">All Languages</option>
                                <option value="active">Active Languages</option>
                                <option value="inactive">Inactive Languages</option>
                                <option value="rtl">RTL Languages</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Language</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Direction</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-medium text-blue-600">🇺🇸</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">English</div>
                                            <div class="text-sm text-gray-500">English (US)</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">en</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">LTR</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">100%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="editLanguage('en')" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                        <button onclick="viewTranslations('en')" class="text-green-600 hover:text-green-900">Translations</button>
                                        <button onclick="setDefault('en')" class="text-purple-600 hover:text-purple-900">Set Default</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-medium text-blue-600">🇪🇸</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Spanish</div>
                                            <div class="text-sm text-gray-500">Español</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">es</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">LTR</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 78%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">78%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="editLanguage('es')" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                        <button onclick="viewTranslations('es')" class="text-green-600 hover:text-green-900">Translations</button>
                                        <button onclick="deactivateLanguage('es')" class="text-red-600 hover:text-red-900">Deactivate</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Translation Management Tab -->
        <div x-show="activeTab === 'translations'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Translation Keys</h3>
                        <div class="flex items-center space-x-3">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">All Keys</option>
                                <option value="missing">Missing Translations</option>
                                <option value="pending">Pending Review</option>
                                <option value="completed">Completed</option>
                            </select>
                            <button onclick="addTranslationKey()" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                Add Key
                            </button>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Translation Key Item -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-medium text-gray-900">auth.login.title</h4>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Complete
                                    </span>
                                    <button onclick="editTranslationKey('auth.login.title')" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">English (Default)</label>
                                    <input type="text" value="Login to Your Account" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" readonly>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Spanish</label>
                                    <input type="text" value="Iniciar Sesión en Tu Cuenta" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import/Export Tab -->
        <div x-show="activeTab === 'import'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Import/Export Translations</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Import Section -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900">Import Translations</h4>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3-3m-3 3l3 3m-3-3h-12" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                <div class="mt-4">
                                    <label for="file-upload" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">Upload translation file</span>
                                        <span class="mt-1 block text-sm text-gray-500">JSON, CSV, or Excel format</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Export Section -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900">Export Translations</h4>
                            <div class="space-y-3">
                                <button onclick="exportJSON()" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Export as JSON
                                </button>
                                <button onclick="exportCSV()" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Export as CSV
                                </button>
                                <button onclick="exportExcel()" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Export as Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportTranslations() {
    alert('Export translations functionality will be implemented');
}

function addLanguage() {
    alert('Add language functionality will be implemented');
}

function editLanguage(code) {
    alert(`Edit language ${code} functionality will be implemented`);
}

function viewTranslations(code) {
    alert(`View translations for ${code} functionality will be implemented`);
}

function setDefault(code) {
    if (confirm(`Set ${code} as default language?`)) {
        alert(`Set default language ${code} functionality will be implemented`);
    }
}

function deactivateLanguage(code) {
    if (confirm(`Deactivate ${code} language?`)) {
        alert(`Deactivate language ${code} functionality will be implemented`);
    }
}

function addTranslationKey() {
    alert('Add translation key functionality will be implemented');
}

function editTranslationKey(key) {
    alert(`Edit translation key ${key} functionality will be implemented`);
}

function exportJSON() {
    alert('Export JSON functionality will be implemented');
}

function exportCSV() {
    alert('Export CSV functionality will be implemented');
}

function exportExcel() {
    alert('Export Excel functionality will be implemented');
}
</script>
@endsection
