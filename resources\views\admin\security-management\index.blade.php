@extends('layouts.admin')
@section('title', 'Security Management')
@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold mb-6">Security Management System</h1>

    <!-- Security Settings Form -->
    <div class="bg-white shadow rounded-lg p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Security Settings</h2>
        <form id="securitySettingsForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700">Max Login Attempts</label>
                <input type="number" name="max_login_attempts" min="1" max="10" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Lockout Duration (seconds)</label>
                <input type="number" name="lockout_duration" min="300" max="3600" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Session Timeout (seconds)</label>
                <input type="number" name="session_timeout" min="900" max="7200" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Require 2FA</label>
                <input type="checkbox" name="require_2fa" class="rounded border-gray-300">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Password Min Length</label>
                <input type="number" name="password_min_length" min="6" max="20" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
            </div>
            <button type="submit" class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">Save Settings</button>
        </form>
    </div>

    <!-- Security Logs -->
    <div class="bg-white shadow rounded-lg p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Security Logs</h2>
        <div class="flex items-center mb-2 space-x-2">
            <input type="date" id="logDate" class="border rounded px-2 py-1 text-sm">
            <select id="logType" class="border rounded px-2 py-1 text-sm">
                <option value="">All Events</option>
                <option value="login">Login</option>
                <option value="password">Password</option>
                <option value="2fa">2FA</option>
            </select>
            <button onclick="fetchSecurityLogs(1)" class="px-2 py-1 bg-indigo-500 text-white rounded text-xs">Filter</button>
            <button onclick="exportLogs()" class="px-2 py-1 bg-gray-700 text-white rounded text-xs">Export CSV</button>
            <button onclick="showAnalytics()" class="px-2 py-1 bg-green-700 text-white rounded text-xs">Show Analytics</button>
        </div>
        <div id="securityLogs" class="space-y-2 text-sm text-gray-700">
            <div>Loading logs...</div>
        </div>
        <div id="logsPagination" class="mt-2 flex space-x-2"></div>
        <div id="analyticsBox" class="mt-4 text-sm text-gray-800"></div>
    </div>

    <!-- System Maintenance Tools -->
    <div class="bg-white shadow rounded-lg p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">System Maintenance</h2>
        <button onclick="clearCache()" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 mr-2">Clear Cache</button>
        <button onclick="optimizeSystem()" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mr-2">Optimize System</button>
        <button onclick="restartQueue()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Restart Queue Worker</button>
        <span id="queueStatus" class="ml-4 text-xs text-gray-600"></span>
    </div>

    <!-- System Info -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">System Information</h2>
        <div id="systemInfo" class="text-sm text-gray-700">
            <div>Loading system info...</div>
        </div>
        <button onclick="copySystemInfo()" class="mt-2 px-3 py-1 bg-gray-700 text-white rounded text-xs">Copy Info</button>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    fetchSecuritySettings();
    fetchSecurityLogs();
    fetchSystemInfo();
    document.getElementById('securitySettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSecuritySettings();
    });
});

function fetchSecuritySettings() {
    axios.get('/api/security/settings')
        .then(function(response) {
            if (response.data.success) {
                const form = document.getElementById('securitySettingsForm');
                const s = response.data.data;
                form.max_login_attempts.value = s.max_login_attempts;
                form.lockout_duration.value = s.lockout_duration;
                form.session_timeout.value = s.session_timeout;
                form.require_2fa.checked = s.require_2fa;
                form.password_min_length.value = s.password_min_length;
            }
        });
}

function saveSecuritySettings() {
    const form = document.getElementById('securitySettingsForm');
    const data = {
        max_login_attempts: form.max_login_attempts.value,
        lockout_duration: form.lockout_duration.value,
        session_timeout: form.session_timeout.value,
        require_2fa: form.require_2fa.checked,
        password_min_length: form.password_min_length.value
    };
    axios.post('/api/security/settings', data)
        .then(function(response) {
            showToast(response.data.message || 'Settings saved', response.data.success);
        });
}

function showToast(message, success = true) {
    let toast = document.createElement('div');
    toast.innerText = message;
    toast.className = 'fixed top-4 right-4 px-4 py-2 rounded shadow-lg z-50 ' + (success ? 'bg-green-600 text-white' : 'bg-red-600 text-white');
    document.body.appendChild(toast);
    setTimeout(() => { toast.remove(); }, 3000);
}

function fetchSecurityLogs(page = 1) {
    const date = document.getElementById('logDate').value;
    const type = document.getElementById('logType').value;
    document.getElementById('securityLogs').innerHTML = '<div>Loading...</div>';
    axios.get('/api/security/logs', { params: { page, date, type } })
        .then(function(response) {
            const logsDiv = document.getElementById('securityLogs');
            logsDiv.innerHTML = '';
            if (response.data.success && response.data.data.length) {
                response.data.data.forEach(function(log) {
                    logsDiv.innerHTML += `<div><span class="font-semibold">[${log.created_at}]</span> ${log.event || log.message || JSON.stringify(log)}</div>`;
                });
            } else {
                logsDiv.innerHTML = '<div>No recent logs.</div>';
            }
            // Pagination
            const pagDiv = document.getElementById('logsPagination');
            pagDiv.innerHTML = '';
            if (response.data.last_page && response.data.last_page > 1) {
                for (let i = 1; i <= response.data.last_page; i++) {
                    pagDiv.innerHTML += `<button onclick="fetchSecurityLogs(${i})" class="px-2 py-1 rounded ${i===response.data.current_page?'bg-indigo-600 text-white':'bg-gray-200'}">${i}</button>`;
                }
            }
        })
        .catch(function() {
            document.getElementById('securityLogs').innerHTML = '<div class="text-red-600">Failed to load logs.</div>';
        });
}

function exportLogs() {
    const date = document.getElementById('logDate').value;
    const type = document.getElementById('logType').value;
    let url = '/api/security/logs/export?';
    if (date) url += 'date=' + encodeURIComponent(date) + '&';
    if (type) url += 'type=' + encodeURIComponent(type);
    window.open(url, '_blank');
}

function showAnalytics() {
    document.getElementById('analyticsBox').innerHTML = 'Loading analytics...';
    axios.get('/api/security/logs/analytics')
        .then(function(response) {
            if (response.data.success) {
                const d = response.data.data;
                document.getElementById('analyticsBox').innerHTML =
                    `<div class='font-semibold mb-1'>Event Analytics:</div>` +
                    `<div>Login events: <span class='font-mono'>${d.login}</span></div>` +
                    `<div>Password changes: <span class='font-mono'>${d.password}</span></div>` +
                    `<div>2FA events: <span class='font-mono'>${d['2fa']}</span></div>`;
            } else {
                document.getElementById('analyticsBox').innerHTML = 'No analytics data.';
            }
        })
        .catch(function() {
            document.getElementById('analyticsBox').innerHTML = '<span class="text-red-600">Failed to load analytics.</span>';
        });
}

function clearCache() {
    if (confirm('Clear all system caches?')) {
        axios.post('/api/system/clear-cache')
            .then(function(response) {
                alert(response.data.message || 'Cache cleared');
            });
    }
}

function optimizeSystem() {
    if (confirm('Optimize system?')) {
        axios.post('/api/system/optimize')
            .then(function(response) {
                alert(response.data.message || 'System optimized');
            });
    }
}

function restartQueue() {
    document.getElementById('queueStatus').innerText = 'Restarting...';
    axios.post('/api/system/restart-queue')
        .then(function(response) {
            showToast(response.data.message || 'Queue restarted', response.data.success);
            document.getElementById('queueStatus').innerText = response.data.success ? 'Queue running' : 'Error';
        })
        .catch(function() {
            document.getElementById('queueStatus').innerText = 'Error';
        });
}

function fetchSystemInfo() {
    axios.get('/api/system/info')
        .then(function(response) {
            const infoDiv = document.getElementById('systemInfo');
            if (response.data.success) {
                let html = '<ul>';
                Object.entries(response.data.data).forEach(function([key, value]) {
                    html += `<li><span class="font-semibold">${key.replace(/_/g, ' ')}:</span> ${value}</li>`;
                });
                html += '</ul>';
                infoDiv.innerHTML = html;
            } else {
                infoDiv.innerHTML = '<div>Unable to load system info.</div>';
            }
        });
}

function copySystemInfo() {
    const infoDiv = document.getElementById('systemInfo');
    const text = infoDiv.innerText;
    navigator.clipboard.writeText(text).then(function() {
        showToast('System info copied!', true);
    });
}
</script>
@endsection
