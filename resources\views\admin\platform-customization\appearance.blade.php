@extends('layouts.admin')

@section('title', 'Appearance Settings')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ route('admin.platform-customization.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                                </svg>
                                <span class="sr-only">Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Appearance</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="text-3xl font-bold text-gray-900 mt-2">Appearance Settings</h1>
                <p class="text-gray-600 mt-1">Customize colors, themes, typography, and visual styling</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="resetToDefaults('appearance')" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset to Default
                </button>
                <button onclick="saveAppearanceSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Sub-navigation -->
    <div class="mb-8" x-data="{ activeSubTab: 'colors' }">
        <div class="flex space-x-1 bg-gray-50 p-1 rounded-lg">
            <button @click="activeSubTab = 'colors'" :class="activeSubTab === 'colors' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Colors & Palette</button>
            <button @click="activeSubTab = 'typography'" :class="activeSubTab === 'typography' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Typography</button>
            <button @click="activeSubTab = 'themes'" :class="activeSubTab === 'themes' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Themes</button>
            <button @click="activeSubTab = 'animations'" :class="activeSubTab === 'animations' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Animations</button>
        </div>

        <!-- Colors & Palette Tab -->
        <div x-show="activeSubTab === 'colors'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🎨 Colors & Palette</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Color Scheme -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Primary Color Scheme</h4>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4">
                                <input type="color" id="primary_color" value="#4F46E5" class="h-12 w-20 rounded border border-gray-300">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700">Primary Color</label>
                                    <p class="text-xs text-gray-500">Used for buttons, links, and highlights</p>
                                    <input type="text" value="#4F46E5" class="mt-1 text-xs border border-gray-300 rounded px-2 py-1 w-20">
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <input type="color" id="secondary_color" value="#10B981" class="h-12 w-20 rounded border border-gray-300">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700">Secondary Color</label>
                                    <p class="text-xs text-gray-500">Used for secondary actions and accents</p>
                                    <input type="text" value="#10B981" class="mt-1 text-xs border border-gray-300 rounded px-2 py-1 w-20">
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <input type="color" id="accent_color" value="#F59E0B" class="h-12 w-20 rounded border border-gray-300">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700">Accent Color</label>
                                    <p class="text-xs text-gray-500">Used for warnings and special highlights</p>
                                    <input type="text" value="#F59E0B" class="mt-1 text-xs border border-gray-300 rounded px-2 py-1 w-20">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Background Colors -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Background Colors</h4>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4">
                                <input type="color" id="background_color" value="#FFFFFF" class="h-12 w-20 rounded border border-gray-300">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700">Main Background</label>
                                    <p class="text-xs text-gray-500">Primary background color</p>
                                    <input type="text" value="#FFFFFF" class="mt-1 text-xs border border-gray-300 rounded px-2 py-1 w-20">
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <input type="color" id="surface_color" value="#F9FAFB" class="h-12 w-20 rounded border border-gray-300">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700">Surface Color</label>
                                    <p class="text-xs text-gray-500">Cards and elevated surfaces</p>
                                    <input type="text" value="#F9FAFB" class="mt-1 text-xs border border-gray-300 rounded px-2 py-1 w-20">
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <input type="color" id="text_color" value="#1F2937" class="h-12 w-20 rounded border border-gray-300">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700">Text Color</label>
                                    <p class="text-xs text-gray-500">Primary text color</p>
                                    <input type="text" value="#1F2937" class="mt-1 text-xs border border-gray-300 rounded px-2 py-1 w-20">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Color Preview -->
                <div class="mt-8">
                    <h4 class="text-base font-medium text-gray-900 mb-4">Color Preview</h4>
                    <div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button class="bg-indigo-600 text-white px-4 py-2 rounded-md">Primary Button</button>
                            <button class="bg-green-600 text-white px-4 py-2 rounded-md">Secondary Button</button>
                            <button class="bg-yellow-500 text-white px-4 py-2 rounded-md">Accent Button</button>
                        </div>
                        <div class="mt-4 p-4 bg-white rounded-lg">
                            <h5 class="text-lg font-medium text-gray-900">Sample Card</h5>
                            <p class="text-gray-600 mt-2">This is how your content will look with the selected colors.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Typography Tab -->
        <div x-show="activeSubTab === 'typography'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📝 Typography</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Font Selection -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Font Family</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Primary Font</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="inter">Inter (Recommended)</option>
                                    <option value="roboto">Roboto</option>
                                    <option value="open-sans">Open Sans</option>
                                    <option value="lato">Lato</option>
                                    <option value="montserrat">Montserrat</option>
                                    <option value="poppins">Poppins</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Heading Font</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="same">Same as Primary</option>
                                    <option value="playfair">Playfair Display</option>
                                    <option value="merriweather">Merriweather</option>
                                    <option value="source-serif">Source Serif Pro</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Font Sizes -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Font Sizes</h4>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4">
                                <label class="w-24 text-sm font-medium text-gray-700">Base Size</label>
                                <input type="range" min="14" max="18" value="16" class="flex-1">
                                <span class="w-12 text-sm text-gray-600">16px</span>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <label class="w-24 text-sm font-medium text-gray-700">Line Height</label>
                                <input type="range" min="1.2" max="2.0" step="0.1" value="1.5" class="flex-1">
                                <span class="w-12 text-sm text-gray-600">1.5</span>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <label class="w-24 text-sm font-medium text-gray-700">Letter Spacing</label>
                                <input type="range" min="-0.05" max="0.1" step="0.01" value="0" class="flex-1">
                                <span class="w-12 text-sm text-gray-600">0em</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Typography Preview -->
                <div class="mt-8">
                    <h4 class="text-base font-medium text-gray-900 mb-4">Typography Preview</h4>
                    <div class="border border-gray-200 rounded-lg p-6 bg-white">
                        <h1 class="text-3xl font-bold text-gray-900 mb-4">Heading 1 - Main Title</h1>
                        <h2 class="text-2xl font-semibold text-gray-800 mb-3">Heading 2 - Section Title</h2>
                        <h3 class="text-xl font-medium text-gray-700 mb-3">Heading 3 - Subsection</h3>
                        <p class="text-base text-gray-600 mb-4">
                            This is a paragraph of body text. It shows how your content will look with the selected typography settings. 
                            The font family, size, and spacing all contribute to the overall readability and aesthetic of your platform.
                        </p>
                        <p class="text-sm text-gray-500">Small text for captions and secondary information.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Themes Tab -->
        <div x-show="activeSubTab === 'themes'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🎭 Themes</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Light Theme -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-white p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Light Theme</h4>
                                <button onclick="previewTheme('light')" class="text-sm text-indigo-600 hover:text-indigo-500">Preview</button>
                            </div>
                            <div class="bg-gray-50 rounded p-3 text-xs">
                                <div class="bg-white rounded p-2 mb-2">
                                    <div class="h-2 bg-indigo-500 rounded mb-1"></div>
                                    <div class="h-1 bg-gray-300 rounded"></div>
                                </div>
                                <div class="h-1 bg-gray-200 rounded"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Dark Theme -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gray-900 p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-white">Dark Theme</h4>
                                <button onclick="previewTheme('dark')" class="text-sm text-indigo-400 hover:text-indigo-300">Preview</button>
                            </div>
                            <div class="bg-gray-800 rounded p-3 text-xs">
                                <div class="bg-gray-700 rounded p-2 mb-2">
                                    <div class="h-2 bg-indigo-400 rounded mb-1"></div>
                                    <div class="h-1 bg-gray-500 rounded"></div>
                                </div>
                                <div class="h-1 bg-gray-600 rounded"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Auto Theme -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gradient-to-r from-white to-gray-900 p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">Auto Theme</h4>
                                <button onclick="previewTheme('auto')" class="text-sm text-indigo-600 hover:text-indigo-500">Preview</button>
                            </div>
                            <div class="bg-gray-100 rounded p-3 text-xs">
                                <div class="bg-white rounded p-2 mb-2">
                                    <div class="h-2 bg-indigo-500 rounded mb-1"></div>
                                    <div class="h-1 bg-gray-300 rounded"></div>
                                </div>
                                <div class="h-1 bg-gray-200 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Theme Settings -->
                <div class="mt-8">
                    <h4 class="text-base font-medium text-gray-900 mb-4">Theme Settings</h4>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Allow User Theme Selection</h5>
                                <p class="text-sm text-gray-500">Let users choose their preferred theme</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Respect System Preference</h5>
                                <p class="text-sm text-gray-500">Automatically switch based on user's system settings</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Animations Tab -->
        <div x-show="activeSubTab === 'animations'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">✨ Animations</h3>
                
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Enable Animations</h4>
                            <p class="text-sm text-gray-500">Turn on/off all animations and transitions</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Animation Speed</label>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500">Slow</span>
                            <input type="range" min="0.5" max="2" step="0.1" value="1" class="flex-1">
                            <span class="text-sm text-gray-500">Fast</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Reduce Motion</h4>
                            <p class="text-sm text-gray-500">Respect user's motion preferences for accessibility</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include external JavaScript -->
<script src="{{ asset('js/admin/platform-customization.js') }}"></script>
@endsection
