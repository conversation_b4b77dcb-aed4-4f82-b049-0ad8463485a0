<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('membership_stages', function (Blueprint $table) {
            $table->decimal('admin_commission', 8, 2)->default(0.00)->after('activation_bonus');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('membership_stages', function (Blueprint $table) {
            $table->dropColumn('admin_commission');
        });
    }
};
