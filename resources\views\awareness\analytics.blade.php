@extends('layouts.app')

@section('title', 'Awareness Analytics')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header with Navigation -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Awareness Center</h1>
                <p class="mt-2 text-gray-600">Track your awareness campaign performance</p>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <a href="{{ route('awareness.index') }}"
                   class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm">
                    Dashboard
                </a>
                <a href="{{ route('awareness.referrals') }}"
                   class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm">
                    Referrals
                </a>
                <a href="{{ route('awareness.tools') }}"
                   class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm">
                    Tools
                </a>
                <a href="{{ route('awareness.analytics') }}"
                   class="py-2 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium text-sm">
                    Analytics
                </a>
            </nav>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Clicks</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $analytics['performance_over_time']['last_30_days']['clicks'] }}</dd>
                        <dd class="text-xs text-green-600">+{{ $analytics['performance_over_time']['last_7_days']['clicks'] }} this week</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Conversions</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $analytics['conversion_funnel']['registrations'] }}</dd>
                        <dd class="text-xs text-blue-600">{{ number_format($analytics['conversion_funnel']['click_to_registration'], 1) }}% conversion rate</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Earnings</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ number_format($analytics['performance_over_time']['last_30_days']['earnings'], 2) }}</dd>
                        <dd class="text-xs text-green-600">${{ number_format($analytics['performance_over_time']['last_7_days']['earnings'], 2) }} this week</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Activations</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $analytics['conversion_funnel']['activations'] }}</dd>
                        <dd class="text-xs text-purple-600">{{ number_format($analytics['conversion_funnel']['registration_to_activation'], 1) }}% activation rate</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Conversion Funnel -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Conversion Funnel</h3>
                <p class="text-sm text-gray-600">Track your awareness campaign effectiveness</p>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <!-- Clicks -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">Link Clicks</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ $analytics['conversion_funnel']['clicks'] }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: 100%"></div>
                    </div>

                    <!-- Registrations -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">Registrations</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ $analytics['conversion_funnel']['registrations'] }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: {{ $analytics['conversion_funnel']['click_to_registration'] }}%"></div>
                    </div>

                    <!-- Activations -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-purple-500 rounded mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">Stage Activations</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ $analytics['conversion_funnel']['activations'] }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-500 h-2 rounded-full" style="width: {{ $analytics['conversion_funnel']['registration_to_activation'] }}%"></div>
                    </div>
                </div>

                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="text-sm text-gray-600">
                        <p><strong>Overall Conversion Rate:</strong> {{ number_format(($analytics['conversion_funnel']['activations'] / $analytics['conversion_funnel']['clicks']) * 100, 2) }}%</p>
                        <p class="mt-1"><strong>Revenue per Click:</strong> ${{ number_format($analytics['performance_over_time']['last_30_days']['earnings'] / $analytics['conversion_funnel']['clicks'], 2) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Traffic Sources -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Traffic Sources</h3>
                <p class="text-sm text-gray-600">Where your clicks are coming from</p>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @php
                        $totalClicks = array_sum($analytics['clicks_by_source']);
                    @endphp
                    @foreach($analytics['clicks_by_source'] as $source => $clicks)
                    @php
                        $percentage = $totalClicks > 0 ? ($clicks / $totalClicks) * 100 : 0;
                        $colors = ['email' => 'blue', 'social' => 'green', 'direct' => 'purple', 'blog' => 'yellow'];
                        $color = $colors[$source] ?? 'gray';
                    @endphp
                    <div>
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium text-gray-900 capitalize">{{ $source }}</span>
                            <span class="text-sm text-gray-600">{{ $clicks }} clicks ({{ number_format($percentage, 1) }}%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-{{ $color }}-500 h-2 rounded-full transition-all duration-300" style="width: {{ $percentage }}%"></div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="mt-6 pt-4 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Best Performing Sources</h4>
                    <div class="space-y-2">
                        @php
                            $sortedSources = collect($analytics['clicks_by_source'])->sortDesc();
                        @endphp
                        @foreach($sortedSources->take(3) as $source => $clicks)
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 capitalize">{{ $source }}</span>
                            <span class="font-medium text-gray-900">{{ $clicks }} clicks</span>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Links -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Top Performing Links</h3>
                <p class="text-sm text-gray-600">Your most effective awareness links</p>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @foreach($analytics['top_performing_links'] as $link)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-900">{{ parse_url($link['url'], PHP_URL_QUERY) ? 'Tracked Link' : 'Primary Link' }}</span>
                            <span class="text-sm text-green-600 font-medium">{{ number_format($link['conversion_rate'], 1) }}%</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-2 truncate">{{ $link['url'] }}</p>
                        <div class="grid grid-cols-2 gap-4 text-xs text-gray-600">
                            <div>
                                <span class="font-medium">Clicks:</span> {{ $link['clicks'] }}
                            </div>
                            <div>
                                <span class="font-medium">Conversions:</span> {{ $link['conversions'] }}
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Performance Timeline -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Performance Timeline</h3>
                <p class="text-sm text-gray-600">Your awareness campaign over time</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <!-- Last 30 Days -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Last 30 Days</h4>
                        <div class="grid grid-cols-3 gap-4 text-sm">
                            <div class="text-center">
                                <div class="text-lg font-medium text-blue-600">{{ $analytics['performance_over_time']['last_30_days']['clicks'] }}</div>
                                <div class="text-gray-600">Clicks</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-green-600">{{ $analytics['performance_over_time']['last_30_days']['referrals'] }}</div>
                                <div class="text-gray-600">Referrals</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600">${{ number_format($analytics['performance_over_time']['last_30_days']['earnings'], 2) }}</div>
                                <div class="text-gray-600">Earnings</div>
                            </div>
                        </div>
                    </div>

                    <!-- Last 7 Days -->
                    <div class="pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Last 7 Days</h4>
                        <div class="grid grid-cols-3 gap-4 text-sm">
                            <div class="text-center">
                                <div class="text-lg font-medium text-blue-600">{{ $analytics['performance_over_time']['last_7_days']['clicks'] }}</div>
                                <div class="text-gray-600">Clicks</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-green-600">{{ $analytics['performance_over_time']['last_7_days']['referrals'] }}</div>
                                <div class="text-gray-600">Referrals</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600">${{ number_format($analytics['performance_over_time']['last_7_days']['earnings'], 2) }}</div>
                                <div class="text-gray-600">Earnings</div>
                            </div>
                        </div>
                    </div>

                    <!-- Yesterday -->
                    <div class="pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Yesterday</h4>
                        <div class="grid grid-cols-3 gap-4 text-sm">
                            <div class="text-center">
                                <div class="text-lg font-medium text-blue-600">{{ $analytics['performance_over_time']['yesterday']['clicks'] }}</div>
                                <div class="text-gray-600">Clicks</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-green-600">{{ $analytics['performance_over_time']['yesterday']['referrals'] }}</div>
                                <div class="text-gray-600">Referrals</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600">${{ number_format($analytics['performance_over_time']['yesterday']['earnings'], 2) }}</div>
                                <div class="text-gray-600">Earnings</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
