<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_credibility_scores', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('total_score')->default(0);
            $table->integer('projects_created')->default(0);
            $table->integer('projects_completed')->default(0);
            $table->integer('projects_approved')->default(0);
            $table->integer('volunteer_hours')->default(0);
            $table->decimal('donations_made', 15, 2)->default(0);
            $table->integer('votes_cast')->default(0);
            $table->json('achievements')->nullable();
            $table->enum('credibility_level', [
                'newcomer',
                'contributor',
                'advocate',
                'champion',
                'leader'
            ])->default('newcomer');
            $table->timestamps();
            
            $table->unique(['user_id']);
            $table->index(['total_score']);
            $table->index(['credibility_level']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_credibility_scores');
    }
};
