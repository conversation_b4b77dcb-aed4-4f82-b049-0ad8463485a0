@extends('layouts.app')

@section('title', 'Create New Project')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Create New Project</h1>
        <p class="text-gray-600 mt-1">Share your environmental project with the community</p>
    </div>

    <form action="{{ route('community.projects.store') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
        @csrf
        
        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Project Title <span class="text-red-500">*</span></label>
                    <input type="text" id="title" name="title" required
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Enter project title" value="{{ old('title') }}">
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category <span class="text-red-500">*</span></label>
                    <select id="category" name="category" required
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Select category</option>
                        <option value="renewable_energy" {{ old('category') === 'renewable_energy' ? 'selected' : '' }}>Renewable Energy</option>
                        <option value="waste_management" {{ old('category') === 'waste_management' ? 'selected' : '' }}>Waste Management</option>
                        <option value="water_conservation" {{ old('category') === 'water_conservation' ? 'selected' : '' }}>Water Conservation</option>
                        <option value="reforestation" {{ old('category') === 'reforestation' ? 'selected' : '' }}>Reforestation</option>
                        <option value="sustainable_agriculture" {{ old('category') === 'sustainable_agriculture' ? 'selected' : '' }}>Sustainable Agriculture</option>
                        <option value="climate_action" {{ old('category') === 'climate_action' ? 'selected' : '' }}>Climate Action</option>
                        <option value="biodiversity" {{ old('category') === 'biodiversity' ? 'selected' : '' }}>Biodiversity Conservation</option>
                        <option value="green_technology" {{ old('category') === 'green_technology' ? 'selected' : '' }}>Green Technology</option>
                        <option value="education" {{ old('category') === 'education' ? 'selected' : '' }}>Environmental Education</option>
                        <option value="other" {{ old('category') === 'other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('category')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location <span class="text-red-500">*</span></label>
                    <input type="text" id="location" name="location" required
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="City, State, Country" value="{{ old('location') }}">
                    @error('location')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="project_type" class="block text-sm font-medium text-gray-700 mb-1">Project Type <span class="text-red-500">*</span></label>
                    <select id="project_type" name="project_type" required
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Select type</option>
                        <option value="petition" {{ old('project_type') === 'petition' ? 'selected' : '' }}>Petition</option>
                        <option value="volunteer" {{ old('project_type') === 'volunteer' ? 'selected' : '' }}>Volunteer Project</option>
                        <option value="crowdfund" {{ old('project_type') === 'crowdfund' ? 'selected' : '' }}>Crowdfunding</option>
                        <option value="awareness" {{ old('project_type') === 'awareness' ? 'selected' : '' }}>Awareness Campaign</option>
                        <option value="research" {{ old('project_type') === 'research' ? 'selected' : '' }}>Research Project</option>
                    </select>
                    @error('project_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-6">
                <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-1">Featured Image</label>
                <input type="file" id="featured_image" name="featured_image" accept="image/*"
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <p class="mt-1 text-sm text-gray-500">Upload a compelling image that represents your project</p>
                @error('featured_image')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Problem Statement -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
                <svg class="w-5 h-5 inline mr-2 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                Problem Statement
            </h3>
            
            <div>
                <label for="problem" class="block text-sm font-medium text-gray-700 mb-1">What environmental problem does this project address? <span class="text-red-500">*</span></label>
                <textarea id="problem" name="problem" rows="4" required
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="Describe the environmental issue, its impact, and why it needs urgent attention...">{{ old('problem') }}</textarea>
                <p class="mt-1 text-sm text-gray-500">Clearly explain the environmental challenge you're addressing</p>
                @error('problem')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Solution Approach -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
                <svg class="w-5 h-5 inline mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Solution Approach
            </h3>
            
            <div>
                <label for="solution" class="block text-sm font-medium text-gray-700 mb-1">How will this project solve the problem? <span class="text-red-500">*</span></label>
                <textarea id="solution" name="solution" rows="4" required
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="Describe your approach, methodology, and specific actions that will address the problem...">{{ old('solution') }}</textarea>
                <p class="mt-1 text-sm text-gray-500">Explain your strategy and how it will create positive environmental impact</p>
                @error('solution')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Team Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
                <svg class="w-5 h-5 inline mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                </svg>
                Team Information
            </h3>
            
            <div class="space-y-4">
                <div>
                    <label for="team_description" class="block text-sm font-medium text-gray-700 mb-1">Team Description <span class="text-red-500">*</span></label>
                    <textarea id="team_description" name="team_description" rows="3" required
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Describe your team, expertise, and why you're qualified to execute this project...">{{ old('team_description') }}</textarea>
                    @error('team_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="team_size" class="block text-sm font-medium text-gray-700 mb-1">Team Size</label>
                        <select id="team_size" name="team_size"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select team size</option>
                            <option value="1" {{ old('team_size') === '1' ? 'selected' : '' }}>Individual (1 person)</option>
                            <option value="2-5" {{ old('team_size') === '2-5' ? 'selected' : '' }}>Small team (2-5 people)</option>
                            <option value="6-15" {{ old('team_size') === '6-15' ? 'selected' : '' }}>Medium team (6-15 people)</option>
                            <option value="16-50" {{ old('team_size') === '16-50' ? 'selected' : '' }}>Large team (16-50 people)</option>
                            <option value="50+" {{ old('team_size') === '50+' ? 'selected' : '' }}>Organization (50+ people)</option>
                        </select>
                    </div>

                    <div>
                        <label for="looking_for_volunteers" class="block text-sm font-medium text-gray-700 mb-1">Looking for Volunteers?</label>
                        <select id="looking_for_volunteers" name="looking_for_volunteers"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="no" {{ old('looking_for_volunteers') === 'no' ? 'selected' : '' }}>No</option>
                            <option value="yes" {{ old('looking_for_volunteers') === 'yes' ? 'selected' : '' }}>Yes</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requirements & Timeline -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
                <svg class="w-5 h-5 inline mr-2 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                Requirements & Timeline
            </h3>
            
            <div class="space-y-4">
                <div>
                    <label for="requirements" class="block text-sm font-medium text-gray-700 mb-1">Project Requirements <span class="text-red-500">*</span></label>
                    <textarea id="requirements" name="requirements" rows="3" required
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="List resources, funding, skills, or support needed to complete this project...">{{ old('requirements') }}</textarea>
                    @error('requirements')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="timeline" class="block text-sm font-medium text-gray-700 mb-1">Expected Timeline</label>
                        <select id="timeline" name="timeline"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select timeline</option>
                            <option value="1-3_months" {{ old('timeline') === '1-3_months' ? 'selected' : '' }}>1-3 months</option>
                            <option value="3-6_months" {{ old('timeline') === '3-6_months' ? 'selected' : '' }}>3-6 months</option>
                            <option value="6-12_months" {{ old('timeline') === '6-12_months' ? 'selected' : '' }}>6-12 months</option>
                            <option value="1-2_years" {{ old('timeline') === '1-2_years' ? 'selected' : '' }}>1-2 years</option>
                            <option value="2+_years" {{ old('timeline') === '2+_years' ? 'selected' : '' }}>2+ years</option>
                            <option value="ongoing" {{ old('timeline') === 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                        </select>
                    </div>

                    <div>
                        <label for="funding_needed" class="block text-sm font-medium text-gray-700 mb-1">Funding Needed (USD)</label>
                        <input type="number" id="funding_needed" name="funding_needed" min="0" step="0.01"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="0.00" value="{{ old('funding_needed') }}">
                    </div>
                </div>
            </div>
        </div>

        <!-- Benefits Section -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
                <svg class="w-5 h-5 inline mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                </svg>
                Project Benefits
            </h3>

            <div class="space-y-4">
                <div>
                    <label for="environmental_benefits" class="block text-sm font-medium text-gray-700 mb-1">Benefits to Environment <span class="text-red-500">*</span></label>
                    <textarea id="environmental_benefits" name="environmental_benefits" rows="3" required
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Describe the positive environmental impact this project will create...">{{ old('environmental_benefits') }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Explain how this project will help the environment</p>
                    @error('environmental_benefits')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="life_benefits" class="block text-sm font-medium text-gray-700 mb-1">Benefits to Life & Community <span class="text-red-500">*</span></label>
                    <textarea id="life_benefits" name="life_benefits" rows="3" required
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Describe how this project will improve quality of life for people and communities...">{{ old('life_benefits') }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Explain the social and health benefits</p>
                    @error('life_benefits')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="participant_benefits" class="block text-sm font-medium text-gray-700 mb-1">Benefits to Participants</label>
                    <textarea id="participant_benefits" name="participant_benefits" rows="3"
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="What will volunteers, supporters, or participants gain from joining this project?">{{ old('participant_benefits') }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Describe learning opportunities, skills development, networking, etc.</p>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>

            <div class="space-y-4">
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Detailed Description</label>
                    <textarea id="description" name="description" rows="4"
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Provide any additional details about your project...">{{ old('description') }}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="website" class="block text-sm font-medium text-gray-700 mb-1">Project Website</label>
                        <input type="url" id="website" name="website"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="https://example.com" value="{{ old('website') }}">
                    </div>

                    <div>
                        <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                        <input type="email" id="contact_email" name="contact_email"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="<EMAIL>" value="{{ old('contact_email', auth()->user()->email) }}">
                    </div>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="featured" name="featured" value="1" {{ old('featured') ? 'checked' : '' }}
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="featured" class="ml-2 block text-sm text-gray-900">
                        Request to feature this project
                        <span class="text-gray-500">(Subject to admin approval)</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex items-center justify-between">
            <a href="{{ route('community.index', ['tab' => 'projects']) }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Cancel
            </a>

            <div class="flex items-center space-x-3">
                <button type="submit" name="action" value="draft"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Save as Draft
                </button>
                <button type="submit" name="action" value="publish"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    Publish Project
                </button>
            </div>
        </div>
    </form>
</div>
@endsection
