<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Widget and shortcode management
        Schema::create('widgets', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // community_projects, stage_pricing, leaderboard, etc.
            $table->text('description')->nullable();
            $table->json('settings'); // widget configuration
            $table->text('shortcode');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Stage pricing plans
        Schema::create('stage_pricing_plans', function (Blueprint $table) {
            $table->id();
            $table->string('stage_name');
            $table->decimal('price', 10, 2);
            $table->json('benefits'); // stage benefits array
            $table->string('referral_requirement')->default('0-10 years'); // changed from referral count
            $table->string('button_text')->default('Activate Stage');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Company branding settings
        Schema::create('company_branding', function (Blueprint $table) {
            $table->id();
            $table->string('company_name')->default('Environmental Community Platform');
            $table->string('company_logo')->nullable();
            $table->text('company_description')->nullable();
            $table->string('favicon')->nullable();
            $table->json('contact_info')->nullable(); // email, phone, address
            $table->timestamps();
        });

        // Appearance settings
        Schema::create('appearance_settings', function (Blueprint $table) {
            $table->id();
            $table->string('primary_color')->default('#4F46E5');
            $table->string('secondary_color')->default('#10B981');
            $table->string('accent_color')->default('#F59E0B');
            $table->string('background_color')->default('#FFFFFF');
            $table->string('text_color')->default('#1F2937');
            $table->json('custom_css')->nullable();
            $table->timestamps();
        });

        // Footer management
        Schema::create('footer_settings', function (Blueprint $table) {
            $table->id();
            $table->json('footer_links'); // organization links, etc.
            $table->text('footer_text')->nullable();
            $table->string('copyright_text')->default('All rights reserved.');
            $table->json('social_links')->nullable();
            $table->boolean('show_organization_links')->default(true);
            $table->timestamps();
        });

        // Footer pages
        Schema::create('footer_pages', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('content');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // System settings
        Schema::create('system_settings', function (Blueprint $table) {
            $table->id();
            $table->string('setting_key')->unique();
            $table->text('setting_value');
            $table->string('setting_type')->default('text'); // text, number, boolean, json
            $table->string('category')->default('general');
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_settings');
        Schema::dropIfExists('footer_pages');
        Schema::dropIfExists('footer_settings');
        Schema::dropIfExists('appearance_settings');
        Schema::dropIfExists('company_branding');
        Schema::dropIfExists('stage_pricing_plans');
        Schema::dropIfExists('widgets');
    }
};
