<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserStageActivation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'membership_stage_id',
        'is_active',
        'activation_bonus_paid',
        'activated_at',
        'deactivated_at',
        'approval_status',
        'approved_at',
        'approved_by',
        'approval_notes',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'activation_bonus_paid' => 'decimal:2',
        'activated_at' => 'datetime',
        'deactivated_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the user that owns this activation.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the membership stage for this activation.
     */
    public function membershipStage()
    {
        return $this->belongsTo(MembershipStage::class);
    }

    /**
     * Get commissions earned from this stage activation.
     */
    public function commissions()
    {
        return $this->hasMany(Commission::class, 'stage_activation_id');
    }

    /**
     * Scope for active stage activations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for inactive stage activations.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Deactivate this stage.
     */
    public function deactivate()
    {
        $this->update([
            'is_active' => false,
            'deactivated_at' => now(),
        ]);
    }

    /**
     * Reactivate this stage.
     */
    public function reactivate()
    {
        $this->update([
            'is_active' => true,
            'deactivated_at' => null,
        ]);
    }

    /**
     * Get total earnings from this stage activation.
     */
    public function getTotalEarningsAttribute()
    {
        return $this->commissions()->sum('amount') + $this->activation_bonus_paid;
    }

    /**
     * Check if activation bonus was paid.
     */
    public function wasBonusPaid(): bool
    {
        return $this->activation_bonus_paid > 0;
    }

    /**
     * User who approved this activation.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Check if activation is approved.
     */
    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    /**
     * Check if activation is pending approval.
     */
    public function isPendingApproval(): bool
    {
        return $this->approval_status === 'pending';
    }

    /**
     * Scope for approved activations.
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope for pending approval activations.
     */
    public function scopePendingApproval($query)
    {
        return $query->where('approval_status', 'pending');
    }
}
