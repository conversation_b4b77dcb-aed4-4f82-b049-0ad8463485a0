<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_admin')->default(false)->after('is_active');
            $table->timestamp('admin_since')->nullable()->after('is_admin');
        });

        // Make the first user an admin
        if ($firstUser = \App\Models\User::first()) {
            $firstUser->update([
                'is_admin' => true,
                'admin_since' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['is_admin', 'admin_since']);
        });
    }
};
