<?php

namespace App\Http\Controllers;

use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Switch language.
     */
    public function switch(Request $request, $languageCode)
    {
        $language = Language::getByCode($languageCode);
        
        if (!$language) {
            return back()->with('error', 'Language not supported');
        }

        // Update user preference if logged in
        if (Auth::check()) {
            Auth::user()->update(['language' => $languageCode]);
        }

        // Store in session
        Session::put('language', $languageCode);

        // Redirect back with success message
        return back()->with('success', __('Language changed to :language', ['language' => $language->native_name]));
    }

    /**
     * Get available languages for API.
     */
    public function getAvailable()
    {
        $languages = Language::getActive();
        
        return response()->json([
            'languages' => $languages->map(function ($language) {
                return [
                    'code' => $language->code,
                    'name' => $language->name,
                    'native_name' => $language->native_name,
                    'flag_emoji' => $language->flag_emoji,
                    'display_name' => $language->display_name,
                    'is_rtl' => $language->is_rtl,
                ];
            }),
            'current' => app()->getLocale(),
        ]);
    }
}
