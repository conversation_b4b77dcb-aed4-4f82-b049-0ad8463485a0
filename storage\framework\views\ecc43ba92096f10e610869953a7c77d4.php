

<?php $__env->startSection('title', 'Helpers Management System'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Clean Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Helpers Management</h1>
                <p class="text-gray-600 mt-1">Manage support staff, help desk features, and customer service tools</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportHelperData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export Data
                </button>
                <button onclick="addNewHelper()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add New Helper
                </button>
            </div>
        </div>
    </div>

    <!-- Helper Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">24</h3>
                    <p class="text-sm text-gray-600">Active Helpers</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">156</h3>
                    <p class="text-sm text-gray-600">Open Tickets</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">2.3h</h3>
                    <p class="text-sm text-gray-600">Avg Response Time</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">94.5%</h3>
                    <p class="text-sm text-gray-600">Satisfaction Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Clean Navigation -->
    <div class="mb-8" x-data="{ activeTab: 'helpers' }">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <nav class="flex space-x-0">
                <button @click="activeTab = 'helpers'" :class="activeTab === 'helpers' ? 'bg-blue-50 text-blue-700 border-blue-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 first:rounded-l-lg transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                    Staff
                </button>
                <button @click="activeTab = 'tickets'" :class="activeTab === 'tickets' ? 'bg-blue-50 text-blue-700 border-blue-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    Tickets
                </button>
                <button @click="activeTab = 'knowledge'" :class="activeTab === 'knowledge' ? 'bg-blue-50 text-blue-700 border-blue-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                    Knowledge Base
                </button>
                <button @click="activeTab = 'settings'" :class="activeTab === 'settings' ? 'bg-blue-50 text-blue-700 border-blue-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium border-r border-gray-200 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                    Settings
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'bg-blue-50 text-blue-700 border-blue-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'" class="flex-1 py-4 px-6 text-sm font-medium last:rounded-r-lg transition-all duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                    </svg>
                    Analytics
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <!-- Helper Management Tab -->
        <div x-show="activeTab === 'helpers'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Support Staff Management</h3>
                    <div class="flex items-center space-x-3">
                        <input type="text" placeholder="Search helpers..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>All Roles</option>
                            <option>Support Agent</option>
                            <option>Senior Agent</option>
                            <option>Team Lead</option>
                            <option>Manager</option>
                        </select>
                    </div>
                </div>
                    </div>
                    <button type="submit" class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg">
                        Search
                    </button>
                </form>
            </div>

            <!-- Helpers Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Active</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=John+Doe&background=4F46E5&color=fff" alt="">
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">John Doe</div>
                                        <div class="text-sm text-gray-500"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Support Agent</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 hours ago</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="editHelper('<EMAIL>')" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                                <button onclick="deactivateHelper('<EMAIL>')" class="text-red-600 hover:text-red-900">Deactivate</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Jane+Smith&background=10B981&color=fff" alt="">
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">Jane Smith</div>
                                        <div class="text-sm text-gray-500"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Team Lead</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">89</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">30 minutes ago</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="editHelper('<EMAIL>')" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                                <button onclick="deactivateHelper('<EMAIL>')" class="text-red-600 hover:text-red-900">Deactivate</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Mike+Johnson&background=EF4444&color=fff" alt="">
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">Mike Johnson</div>
                                        <div class="text-sm text-gray-500"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Senior Agent</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">67</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1 hour ago</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="editHelper('<EMAIL>')" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                                <button onclick="deactivateHelper('<EMAIL>')" class="text-red-600 hover:text-red-900">Deactivate</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            </div>
        </div>

        <!-- Support Tickets Tab -->
        <div x-show="activeTab === 'tickets'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Support Ticket Management</h3>
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>All Priorities</option>
                            <option>High</option>
                            <option>Medium</option>
                            <option>Low</option>
                        </select>
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>All Status</option>
                            <option>Open</option>
                            <option>In Progress</option>
                            <option>Resolved</option>
                            <option>Closed</option>
                        </select>
                        <input type="text" placeholder="Search tickets..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                    <button onclick="createTicket()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Create Ticket
                    </button>
                </div>

                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <span class="text-sm font-medium text-gray-900">#TICKET-001</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">High Priority</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">In Progress</span>
                            </div>
                            <span class="text-sm text-gray-500">2 hours ago</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-900 mb-1">Login Issues with Two-Factor Authentication</h4>
                        <p class="text-sm text-gray-600 mb-2">User unable to complete 2FA verification process...</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">Assigned to:</span>
                                <span class="text-xs font-medium text-gray-900">John Doe</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="viewTicket('TICKET-001')" class="text-xs text-blue-600 hover:text-blue-900">View</button>
                                <button onclick="assignTicket('TICKET-001')" class="text-xs text-green-600 hover:text-green-900">Reassign</button>
                                <button onclick="closeTicket('TICKET-001')" class="text-xs text-red-600 hover:text-red-900">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Knowledge Base Tab -->
        <div x-show="activeTab === 'knowledge'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Knowledge Base Management</h3>
                    <button onclick="createArticle()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Create Article
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">Getting Started</h4>
                        <p class="text-sm text-gray-600 mb-3">Basic guides for new users</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">12 articles</span>
                            <button onclick="manageCategory('getting-started')" class="text-xs text-blue-600 hover:text-blue-900">Manage</button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">Account Management</h4>
                        <p class="text-sm text-gray-600 mb-3">User account and profile help</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">8 articles</span>
                            <button onclick="manageCategory('account-management')" class="text-xs text-blue-600 hover:text-blue-900">Manage</button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">Payment & Billing</h4>
                        <p class="text-sm text-gray-600 mb-3">Payment processing and billing</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">15 articles</span>
                            <button onclick="manageCategory('payment-billing')" class="text-xs text-blue-600 hover:text-blue-900">Manage</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Desk Settings Tab -->
        <div x-show="activeTab === 'settings'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Help Desk Configuration</h3>
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Default Response Time (hours)</label>
                            <input type="number" value="24" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Auto-Assignment</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option>Round Robin</option>
                                <option>Least Busy</option>
                                <option>Manual Only</option>
                            </select>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Enable Email Notifications</h4>
                                <p class="text-sm text-gray-500">Send email alerts for new tickets</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Customer Satisfaction Surveys</h4>
                                <p class="text-sm text-gray-500">Send surveys after ticket resolution</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics & Reports Tab -->
        <div x-show="activeTab === 'analytics'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Performance Metrics</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Average Response Time</span>
                            <span class="text-sm font-medium text-gray-900">2.3 hours</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Resolution Rate</span>
                            <span class="text-sm font-medium text-green-600">87.5%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Customer Satisfaction</span>
                            <span class="text-sm font-medium text-green-600">94.5%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">First Contact Resolution</span>
                            <span class="text-sm font-medium text-gray-900">72.1%</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Ticket Statistics</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Open Tickets</span>
                            <span class="text-sm font-medium text-yellow-600">156</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">In Progress</span>
                            <span class="text-sm font-medium text-blue-600">89</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Resolved Today</span>
                            <span class="text-sm font-medium text-green-600">34</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Overdue Tickets</span>
                            <span class="text-sm font-medium text-red-600">12</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportHelperData() {
    alert('Export helper data functionality will be implemented');
}

function addNewHelper() {
    alert('Add new helper functionality will be implemented');
}

function editHelper(email) {
    alert(`Edit helper ${email} functionality will be implemented`);
}

function deactivateHelper(email) {
    if (confirm(`Deactivate helper ${email}?`)) {
        alert(`Deactivate helper ${email} functionality will be implemented`);
    }
}

function createTicket() {
    alert('Create ticket functionality will be implemented');
}

function viewTicket(ticketId) {
    alert(`View ticket ${ticketId} functionality will be implemented`);
}

function assignTicket(ticketId) {
    alert(`Assign ticket ${ticketId} functionality will be implemented`);
}

function closeTicket(ticketId) {
    if (confirm(`Close ticket ${ticketId}?`)) {
        alert(`Close ticket ${ticketId} functionality will be implemented`);
    }
}

function createArticle() {
    alert('Create knowledge base article functionality will be implemented');
}

function manageCategory(category) {
    showCategoryManagementModal(category);
}

// Helpers Management System
const helpersManagement = {
    data: {
        helpers: [],
        tickets: [],
        knowledgeBase: [],
        settings: {},
        performance: {}
    },

    charts: {
        ticketTrends: null,
        helperPerformance: null,
        satisfactionRating: null
    },

    init() {
        this.loadHelpersData();
        this.setupEventListeners();
        this.initializeCharts();
        this.startRealTimeUpdates();
    },

    loadHelpersData() {
        Promise.all([
            this.loadHelpers(),
            this.loadTickets(),
            this.loadKnowledgeBase(),
            this.loadSettings()
        ]).then(() => {
            this.updateDashboard();
        });
    },

    loadHelpers() {
        return fetch('/admin/helpers-management/helpers')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.helpers = data.data;
                    this.updateHelpersDisplay();
                }
            });
    },

    loadTickets(page = 1, filters = {}) {
        const params = new URLSearchParams({ page, ...filters });

        return fetch(`/admin/helpers-management/tickets?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.tickets = data.data;
                    this.updateTicketsDisplay();
                    this.updateTicketsPagination(data.pagination);
                }
            });
    },

    loadKnowledgeBase(category = null, search = null) {
        const params = new URLSearchParams();
        if (category) params.append('category', category);
        if (search) params.append('search', search);

        return fetch(`/admin/helpers-management/knowledge-base?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.knowledgeBase = data.data;
                    this.updateKnowledgeBaseDisplay();
                }
            });
    },

    loadSettings() {
        return fetch('/admin/helpers-management/settings')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.data.settings = data.data;
                    this.populateSettingsForm();
                }
            });
    },

    setupEventListeners() {
        // Auto-save settings
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-helpdesk-setting]')) {
                this.debounce(() => this.saveSettings(), 1000);
            }
        });

        // Real-time ticket search
        document.addEventListener('input', (e) => {
            if (e.target.matches('#ticket-search')) {
                this.debounce(() => this.searchTickets(e.target.value), 300);
            }
        });

        // Knowledge base search
        document.addEventListener('input', (e) => {
            if (e.target.matches('#kb-search')) {
                this.debounce(() => this.searchKnowledgeBase(e.target.value), 300);
            }
        });
    },

    initializeCharts() {
        this.initTicketTrendsChart();
        this.initHelperPerformanceChart();
        this.initSatisfactionChart();
    },

    startRealTimeUpdates() {
        // Update ticket counts every 30 seconds
        setInterval(() => {
            this.updateTicketCounts();
        }, 30000);

        // Update helper status every 2 minutes
        setInterval(() => {
            this.updateHelperStatus();
        }, 120000);
    },

    updateDashboard() {
        this.updateHelpersDisplay();
        this.updateTicketsDisplay();
        this.updateKnowledgeBaseDisplay();
        this.updateCharts();
    },

    debounce(func, wait) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(func, wait);
    }
};

// Helper Management Functions
function addNewHelper() {
    const formData = {
        name: document.querySelector('#helper-name').value,
        email: document.querySelector('#helper-email').value,
        role: document.querySelector('#helper-role').value,
        password: document.querySelector('#helper-password').value,
        departments: getSelectedDepartments(),
        permissions: getSelectedPermissions()
    };

    if (!formData.name || !formData.email || !formData.role || !formData.password) {
        showNotification('Please fill in all required fields', 'warning');
        return;
    }

    fetch('/admin/helpers-management/helpers', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Helper created successfully!', 'success');
            helpersManagement.loadHelpers();
            clearHelperForm();
        } else {
            showNotification(data.message || 'Creation failed', 'error');
        }
    });
}

function editHelper(email) {
    const helper = helpersManagement.data.helpers.find(h => h.email === email);
    if (helper) {
        showHelperEditModal(helper);
    }
}

function deactivateHelper(email) {
    if (confirm(`Are you sure you want to deactivate helper ${email}?`)) {
        fetch(`/admin/helpers-management/helpers/${email}/deactivate`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Helper deactivated successfully!', 'success');
                helpersManagement.loadHelpers();
            }
        });
    }
}

// Ticket Management Functions
function createTicket() {
    const formData = {
        title: document.querySelector('#ticket-title').value,
        description: document.querySelector('#ticket-description').value,
        priority: document.querySelector('#ticket-priority').value,
        category: document.querySelector('#ticket-category').value,
        user_email: document.querySelector('#ticket-user-email').value
    };

    if (!formData.title || !formData.description || !formData.priority) {
        showNotification('Please fill in all required fields', 'warning');
        return;
    }

    fetch('/admin/helpers-management/tickets', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Ticket created successfully!', 'success');
            helpersManagement.loadTickets();
            clearTicketForm();
        } else {
            showNotification(data.message || 'Creation failed', 'error');
        }
    });
}

function viewTicket(ticketId) {
    fetch(`/admin/helpers-management/tickets/${ticketId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showTicketDetailsModal(data.data);
            }
        });
}

function assignTicket(ticketId) {
    const helperId = prompt('Enter helper ID to assign:');
    const notes = prompt('Assignment notes (optional):');

    if (helperId) {
        fetch('/admin/helpers-management/assign-ticket', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ticket_id: ticketId,
                helper_id: parseInt(helperId),
                notes: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Ticket assigned successfully!', 'success');
                helpersManagement.loadTickets();
            } else {
                showNotification(data.message || 'Assignment failed', 'error');
            }
        });
    }
}

function closeTicket(ticketId) {
    const resolutionNotes = prompt('Enter resolution notes:');
    const rating = prompt('Customer satisfaction rating (1-5, optional):');

    if (resolutionNotes) {
        fetch(`/admin/helpers-management/tickets/${ticketId}/close`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                resolution_notes: resolutionNotes,
                satisfaction_rating: rating ? parseInt(rating) : null,
                send_notification: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Ticket closed successfully!', 'success');
                helpersManagement.loadTickets();
            } else {
                showNotification(data.message || 'Close failed', 'error');
            }
        });
    }
}

// Knowledge Base Functions
function createArticle() {
    const formData = {
        title: document.querySelector('#article-title').value,
        content: document.querySelector('#article-content').value,
        category: document.querySelector('#article-category').value,
        tags: getSelectedTags(),
        is_public: document.querySelector('#article-public').checked,
        featured: document.querySelector('#article-featured').checked
    };

    if (!formData.title || !formData.content || !formData.category) {
        showNotification('Please fill in all required fields', 'warning');
        return;
    }

    fetch('/admin/helpers-management/knowledge-base/articles', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Article created successfully!', 'success');
            helpersManagement.loadKnowledgeBase();
            clearArticleForm();
        } else {
            showNotification(data.message || 'Creation failed', 'error');
        }
    });
}

function showCategoryManagementModal(category) {
    // Implementation for category management modal
    alert(`Manage ${category} category - Modal implementation needed`);
}

// Export Functions
function exportHelperData() {
    const type = document.querySelector('#export-type')?.value || 'helpers';
    const format = document.querySelector('#export-format')?.value || 'csv';
    const dateFrom = document.querySelector('#export-date-from')?.value;
    const dateTo = document.querySelector('#export-date-to')?.value;

    const params = new URLSearchParams({ type, format });
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    window.open(`/admin/helpers-management/export?${params}`, '_blank');
    showNotification('Export started. Download will begin shortly.', 'info');
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function showHelperEditModal(helper) {
    // Implementation for helper edit modal
    alert(`Edit helper ${helper.name} - Modal implementation needed`);
}

function showTicketDetailsModal(ticket) {
    // Implementation for ticket details modal
    alert(`Ticket details for ${ticket.id} - Modal implementation needed`);
}

function getSelectedDepartments() {
    const checkboxes = document.querySelectorAll('input[name="departments"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function getSelectedPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function getSelectedTags() {
    const input = document.querySelector('#article-tags');
    return input ? input.value.split(',').map(tag => tag.trim()).filter(tag => tag) : [];
}

function clearHelperForm() {
    document.querySelector('#helper-name').value = '';
    document.querySelector('#helper-email').value = '';
    document.querySelector('#helper-role').value = '';
    document.querySelector('#helper-password').value = '';
}

function clearTicketForm() {
    document.querySelector('#ticket-title').value = '';
    document.querySelector('#ticket-description').value = '';
    document.querySelector('#ticket-priority').value = '';
    document.querySelector('#ticket-category').value = '';
    document.querySelector('#ticket-user-email').value = '';
}

function clearArticleForm() {
    document.querySelector('#article-title').value = '';
    document.querySelector('#article-content').value = '';
    document.querySelector('#article-category').value = '';
    document.querySelector('#article-tags').value = '';
    document.querySelector('#article-public').checked = false;
    document.querySelector('#article-featured').checked = false;
}

// Helpers Tab Management System
const helpersTabManager = {
    currentTab: 'staff',

    init() {
        this.setupTabListeners();
        this.loadTabContent(this.currentTab);
    },

    setupTabListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-helpers-tab]')) {
                e.preventDefault();
                const tabId = e.target.dataset.helpersTab;
                this.switchTab(tabId);
            }
        });
    },

    switchTab(tabId) {
        // Update active tab
        document.querySelectorAll('[data-helpers-tab]').forEach(tab => {
            tab.classList.remove('border-blue-500', 'text-blue-600');
            tab.classList.add('border-transparent', 'text-gray-500');
        });

        const activeTab = document.querySelector(`[data-helpers-tab="${tabId}"]`);
        if (activeTab) {
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-blue-500', 'text-blue-600');
        }

        // Hide all tab content
        document.querySelectorAll('[data-helpers-content]').forEach(content => {
            content.classList.add('hidden');
        });

        // Show selected tab content
        const activeContent = document.querySelector(`[data-helpers-content="${tabId}"]`);
        if (activeContent) {
            activeContent.classList.remove('hidden');
        }

        this.currentTab = tabId;
        this.loadTabContent(tabId);
    },

    loadTabContent(tabId) {
        switch(tabId) {
            case 'staff':
                this.loadStaffContent();
                break;
            case 'tickets':
                this.loadTicketsContent();
                break;
            case 'knowledge-base':
                this.loadKnowledgeBaseContent();
                break;
            case 'performance':
                this.loadPerformanceContent();
                break;
            case 'settings':
                this.loadSettingsContent();
                break;
        }
    },

    loadStaffContent() {
        fetch('/admin/helpers-management/staff-overview')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateStaffDisplay(data.data);
                }
            });
    },

    loadTicketsContent() {
        fetch('/admin/helpers-management/tickets-overview')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateTicketsDisplay(data.data);
                }
            });
    },

    loadKnowledgeBaseContent() {
        fetch('/admin/helpers-management/knowledge-base-overview')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateKnowledgeBaseDisplay(data.data);
                }
            });
    },

    loadPerformanceContent() {
        fetch('/admin/helpers-management/performance-overview')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updatePerformanceDisplay(data.data);
                }
            });
    },

    loadSettingsContent() {
        fetch('/admin/helpers-management/helpdesk-settings')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateSettingsDisplay(data.data);
                }
            });
    },

    updateStaffDisplay(data) {
        // Update staff overview
        const container = document.querySelector('#staff-overview-container');
        if (container && data.staff) {
            container.innerHTML = this.generateStaffHTML(data.staff);
        }
    },

    updateTicketsDisplay(data) {
        // Update tickets overview
        const container = document.querySelector('#tickets-overview-container');
        if (container && data.tickets) {
            container.innerHTML = this.generateTicketsHTML(data.tickets);
        }
    },

    updateKnowledgeBaseDisplay(data) {
        // Update knowledge base overview
        const container = document.querySelector('#kb-overview-container');
        if (container && data.articles) {
            container.innerHTML = this.generateKnowledgeBaseHTML(data.articles);
        }
    },

    updatePerformanceDisplay(data) {
        // Update performance charts
        if (data.performance) {
            this.updatePerformanceCharts(data.performance);
        }
    },

    updateSettingsDisplay(data) {
        // Update settings form
        Object.keys(data).forEach(key => {
            const element = document.querySelector(`[name="helpdesk_${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = data[key];
                } else {
                    element.value = data[key];
                }
            }
        });
    },

    generateStaffHTML(staff) {
        return staff.map(helper => `
            <div class="border border-gray-200 rounded-lg p-4 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium">${helper.name}</span>
                    <span class="text-sm px-2 py-1 rounded ${this.getStatusClass(helper.status)}">
                        ${helper.status}
                    </span>
                </div>
                <div class="text-sm text-gray-600">
                    Email: ${helper.email} |
                    Role: ${helper.role} |
                    Tickets: ${helper.tickets_handled} |
                    Rating: ${helper.performance_rating}/5
                </div>
                <div class="mt-2 flex space-x-2">
                    <button onclick="editHelper('${helper.email}')" class="text-xs text-blue-600 hover:text-blue-900">Edit</button>
                    <button onclick="viewHelperPerformance('${helper.id}')" class="text-xs text-green-600 hover:text-green-900">Performance</button>
                    <button onclick="deactivateHelper('${helper.email}')" class="text-xs text-red-600 hover:text-red-900">Deactivate</button>
                </div>
            </div>
        `).join('');
    },

    generateTicketsHTML(tickets) {
        return tickets.map(ticket => `
            <div class="border border-gray-200 rounded-lg p-4 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium">${ticket.title}</span>
                    <span class="text-sm px-2 py-1 rounded ${this.getPriorityClass(ticket.priority)}">
                        ${ticket.priority}
                    </span>
                </div>
                <div class="text-sm text-gray-600">
                    ID: ${ticket.id} |
                    Status: ${ticket.status} |
                    Assigned: ${ticket.assigned_to || 'Unassigned'} |
                    Created: ${formatDate(ticket.created_at)}
                </div>
                <div class="mt-2 flex space-x-2">
                    <button onclick="viewTicket('${ticket.id}')" class="text-xs text-blue-600 hover:text-blue-900">View</button>
                    <button onclick="assignTicket('${ticket.id}')" class="text-xs text-green-600 hover:text-green-900">Assign</button>
                    <button onclick="closeTicket('${ticket.id}')" class="text-xs text-red-600 hover:text-red-900">Close</button>
                </div>
            </div>
        `).join('');
    },

    generateKnowledgeBaseHTML(articles) {
        return articles.map(article => `
            <div class="border border-gray-200 rounded-lg p-4 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium">${article.title}</span>
                    <span class="text-sm text-gray-500">${article.category}</span>
                </div>
                <div class="text-sm text-gray-600">
                    Views: ${article.views} |
                    Author: ${article.author} |
                    Updated: ${formatDate(article.updated_at)}
                </div>
                <div class="mt-2 flex space-x-2">
                    <button onclick="editArticle('${article.id}')" class="text-xs text-blue-600 hover:text-blue-900">Edit</button>
                    <button onclick="viewArticleStats('${article.id}')" class="text-xs text-green-600 hover:text-green-900">Stats</button>
                    <button onclick="deleteArticle('${article.id}')" class="text-xs text-red-600 hover:text-red-900">Delete</button>
                </div>
            </div>
        `).join('');
    },

    getStatusClass(status) {
        switch(status.toLowerCase()) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'inactive': return 'bg-red-100 text-red-800';
            case 'busy': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    },

    getPriorityClass(priority) {
        switch(priority.toLowerCase()) {
            case 'urgent': return 'bg-red-100 text-red-800';
            case 'high': return 'bg-orange-100 text-orange-800';
            case 'medium': return 'bg-yellow-100 text-yellow-800';
            case 'low': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    },

    updatePerformanceCharts(performance) {
        // Update various performance charts
        if (window.helperPerformanceChart && performance.helper_stats) {
            window.helperPerformanceChart.data.datasets[0].data = performance.helper_stats;
            window.helperPerformanceChart.update();
        }

        if (window.ticketTrendsChart && performance.ticket_trends) {
            window.ticketTrendsChart.data.datasets[0].data = performance.ticket_trends;
            window.ticketTrendsChart.update();
        }
    }
};

// Helpers sub-menu specific functions
function saveStaffSettings() {
    const formData = new FormData(document.querySelector('#staff-settings-form'));

    fetch('/admin/helpers-management/staff-settings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Staff settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function saveHelpdeskSettings() {
    const formData = new FormData(document.querySelector('#helpdesk-settings-form'));

    fetch('/admin/helpers-management/settings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Helpdesk settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function viewHelperPerformance(helperId) {
    fetch(`/admin/helpers-management/performance?helper_id=${helperId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayHelperPerformanceModal(data.data);
            }
        });
}

function displayHelperPerformanceModal(performance) {
    // Implementation for showing helper performance modal
    alert(`Helper Performance Data - Modal implementation needed`);
}

function editArticle(articleId) {
    // Implementation for editing knowledge base article
    showNotification(`Editing article ${articleId}`, 'info');
}

function viewArticleStats(articleId) {
    fetch(`/admin/helpers-management/knowledge-base/articles/${articleId}/stats`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayArticleStatsModal(data.data);
            }
        });
}

function displayArticleStatsModal(stats) {
    // Implementation for showing article statistics modal
    alert(`Article Statistics - Modal implementation needed`);
}

function deleteArticle(articleId) {
    if (confirm('Are you sure you want to delete this article?')) {
        fetch(`/admin/helpers-management/knowledge-base/articles/${articleId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Article deleted successfully!', 'success');
                helpersTabManager.loadKnowledgeBaseContent();
            } else {
                showNotification(data.message || 'Delete failed', 'error');
            }
        });
    }
}

function runPerformanceAnalysis() {
    showNotification('Running performance analysis...', 'info');

    fetch('/admin/helpers-management/run-performance-analysis', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Performance analysis completed!', 'success');
            displayPerformanceAnalysisResults(data.results);
        } else {
            showNotification(data.message || 'Analysis failed', 'error');
        }
    });
}

function displayPerformanceAnalysisResults(results) {
    const resultsContainer = document.querySelector('#performance-analysis-results');
    if (!resultsContainer) return;

    resultsContainer.innerHTML = `
        <div class="bg-white rounded-lg p-6 mt-4">
            <h3 class="text-lg font-medium mb-4">Performance Analysis Results</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">${results.total_tickets || 0}</div>
                    <div class="text-sm text-blue-700">Total Tickets</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">${results.avg_response_time || 0}h</div>
                    <div class="text-sm text-green-700">Avg Response Time</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">${results.satisfaction_rate || 0}%</div>
                    <div class="text-sm text-yellow-700">Satisfaction Rate</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">${results.resolution_rate || 0}%</div>
                    <div class="text-sm text-purple-700">Resolution Rate</div>
                </div>
            </div>
            <div class="space-y-3">
                ${(results.insights || []).map(insight => `
                    <div class="flex items-center p-3 rounded-lg bg-gray-50">
                        <div class="flex-1">
                            <div class="font-medium">${insight.title}</div>
                            <div class="text-sm text-gray-600">${insight.description}</div>
                        </div>
                        <div class="ml-4 text-2xl">
                            ${insight.type === 'positive' ? '👍' : insight.type === 'negative' ? '👎' : 'ℹ️'}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function exportHelpdeskData() {
    const type = document.querySelector('#export-type')?.value || 'tickets';
    const format = document.querySelector('#export-format')?.value || 'csv';
    const dateFrom = document.querySelector('#export-date-from')?.value;
    const dateTo = document.querySelector('#export-date-to')?.value;

    const params = new URLSearchParams({ type, format });
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    window.open(`/admin/helpers-management/export?${params}`, '_blank');
    showNotification('Export started. Download will begin shortly.', 'info');
}

// Missing functions for tab functionality
function loadHelperData() {
    showNotification('Loading helper data...', 'info');
    fetch('/admin/helpers-management/staff-overview')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Helper data loaded successfully!', 'success');
            }
        });
}

function loadTicketData() {
    showNotification('Loading ticket data...', 'info');
    fetch('/admin/helpers-management/tickets-overview')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Ticket data loaded successfully!', 'success');
            }
        });
}

function loadKnowledgeBaseData() {
    showNotification('Loading knowledge base data...', 'info');
    fetch('/admin/helpers-management/knowledge-base-overview')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Knowledge base data loaded successfully!', 'success');
            }
        });
}

// Add missing showNotification function
window.showNotification = function(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    helpersManagement.init();
    helpersTabManager.init();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/admin/helpers-management/index.blade.php ENDPATH**/ ?>