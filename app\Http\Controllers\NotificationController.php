<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display user notifications.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $notifications = Notification::forUser($user->id)
            ->active()
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $unreadCount = Notification::getUnreadCountForUser($user);

        return view('notifications.index', compact('notifications', 'unreadCount'));
    }

    /**
     * Display admin notifications.
     */
    public function adminIndex(Request $request)
    {
        if (!Auth::user() || !Auth::user()->is_admin) {
            abort(403, 'Access denied.');
        }

        $notifications = Notification::forAdmins()
            ->active()
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $unreadCount = Notification::getUnreadCountForAdmins();

        return view('admin.notifications.index', compact('notifications', 'unreadCount'));
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Notification $notification)
    {
        $user = Auth::user();

        // Check if user can access this notification
        if ($notification->type === 'user' && $notification->user_id !== $user->id) {
            abort(403, 'Access denied.');
        }

        if ($notification->type === 'admin' && !$user->is_admin) {
            abort(403, 'Access denied.');
        }

        $notification->markAsRead();

        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(Request $request)
    {
        $user = Auth::user();

        if ($request->input('type') === 'admin' && $user->is_admin) {
            Notification::forAdmins()
                ->unread()
                ->update([
                    'is_read' => true,
                    'read_at' => now(),
                ]);
        } else {
            Notification::markAllAsReadForUser($user);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Get unread notification count.
     */
    public function getUnreadCount(Request $request)
    {
        $user = Auth::user();
        $type = $request->input('type', 'user');

        if ($type === 'admin' && $user->is_admin) {
            $count = Notification::getUnreadCountForAdmins();
        } else {
            $count = Notification::getUnreadCountForUser($user);
        }

        return response()->json(['count' => $count]);
    }

    /**
     * Get recent notifications for dropdown.
     */
    public function getRecent(Request $request)
    {
        $user = Auth::user();
        $type = $request->input('type', 'user');
        $limit = $request->input('limit', 5);

        if ($type === 'admin' && $user->is_admin) {
            $notifications = Notification::forAdmins()
                ->active()
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
        } else {
            $notifications = Notification::forUser($user->id)
                ->active()
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
        }

        return response()->json([
            'notifications' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'priority' => $notification->priority,
                    'category' => $notification->category,
                    'is_read' => $notification->is_read,
                    'created_at' => $notification->created_at->diffForHumans(),
                    'priority_color' => $notification->priority_color,
                    'category_icon' => $notification->category_icon,
                ];
            })
        ]);
    }

    /**
     * Delete notification.
     */
    public function destroy(Notification $notification)
    {
        $user = Auth::user();

        // Check if user can delete this notification
        if ($notification->type === 'user' && $notification->user_id !== $user->id) {
            abort(403, 'Access denied.');
        }

        if ($notification->type === 'admin' && !$user->is_admin) {
            abort(403, 'Access denied.');
        }

        $notification->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Subscribe to push notifications.
     */
    public function subscribeToPush(Request $request)
    {
        $request->validate([
            'endpoint' => 'required|string',
            'keys.p256dh' => 'required|string',
            'keys.auth' => 'required|string',
        ]);

        $user = Auth::user();

        // Create or update push subscription
        $user->pushSubscriptions()->updateOrCreate([
            'endpoint' => $request->input('endpoint'),
        ], [
            'public_key' => $request->input('keys.p256dh'),
            'auth_token' => $request->input('keys.auth'),
            'is_active' => true,
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Unsubscribe from push notifications.
     */
    public function unsubscribeFromPush(Request $request)
    {
        $request->validate([
            'endpoint' => 'required|string',
        ]);

        $user = Auth::user();

        $user->pushSubscriptions()
            ->where('endpoint', $request->input('endpoint'))
            ->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Test push notification.
     */
    public function testPush(Request $request)
    {
        if (!Auth::user() || !Auth::user()->is_admin) {
            abort(403, 'Access denied.');
        }

        $user = Auth::user();

        $notification = Notification::createForUser(
            $user,
            'Test Push Notification',
            'This is a test push notification to verify the system is working correctly.',
            'system',
            'normal'
        );

        // Here you would trigger the actual push notification
        // This will be implemented with the push notification service

        return response()->json([
            'success' => true,
            'message' => 'Test notification created and push attempted.'
        ]);
    }
}
