<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // User notifications
        Schema::create('user_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type'); // email_alert, system_notification, message
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // additional data
            $table->boolean('read')->default(false);
            $table->boolean('email_sent')->default(false);
            $table->string('priority')->default('normal'); // low, normal, high, urgent
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
        });

        // Email alerts configuration
        Schema::create('email_alerts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('alert_type'); // stage_activation, referral_bonus, withdrawal_approved, etc.
            $table->boolean('enabled')->default(true);
            $table->string('frequency')->default('immediate'); // immediate, daily, weekly
            $table->timestamps();
        });

        // Messages between users
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('recipient_id')->constrained('users')->onDelete('cascade');
            $table->string('subject');
            $table->text('message');
            $table->boolean('read')->default(false);
            $table->boolean('sender_deleted')->default(false);
            $table->boolean('recipient_deleted')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
        });

        // Custom content management
        Schema::create('custom_content', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // page_title, meta_description, welcome_message, etc.
            $table->string('title');
            $table->text('content');
            $table->string('type')->default('text'); // text, html, json
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Notification templates
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('subject');
            $table->text('email_template');
            $table->text('notification_template');
            $table->json('variables')->nullable(); // available template variables
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_templates');
        Schema::dropIfExists('custom_content');
        Schema::dropIfExists('messages');
        Schema::dropIfExists('email_alerts');
        Schema::dropIfExists('user_notifications');
    }
};
