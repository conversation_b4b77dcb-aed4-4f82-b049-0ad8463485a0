<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MilestoneTracking extends Model
{
    use HasFactory;

    protected $table = 'milestone_tracking';

    protected $fillable = [
        'user_id',
        'milestone_count',
        'bonus_amount',
        'achieved',
        'achieved_at',
    ];

    protected $casts = [
        'achieved' => 'boolean',
        'achieved_at' => 'datetime',
        'bonus_amount' => 'decimal:2',
    ];

    /**
     * Get the user that owns the milestone.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for achieved milestones.
     */
    public function scopeAchieved($query)
    {
        return $query->where('achieved', true);
    }

    /**
     * Scope for pending milestones.
     */
    public function scopePending($query)
    {
        return $query->where('achieved', false);
    }

    /**
     * Mark milestone as achieved and create bonus payment.
     */
    public function markAsAchieved()
    {
        $this->update([
            'achieved' => true,
            'achieved_at' => now(),
        ]);

        // Create bonus payment
        BonusPayment::create([
            'user_id' => $this->user_id,
            'bonus_type' => 'milestone',
            'bonus_name' => "{$this->milestone_count} Referrals Milestone",
            'amount' => $this->bonus_amount,
            'description' => "Milestone bonus for reaching {$this->milestone_count} referrals",
            'criteria' => [
                'milestone_count' => $this->milestone_count,
                'milestone_id' => $this->id,
            ],
            'earned_at' => now(),
        ]);
    }

    /**
     * Get default milestone configurations.
     */
    public static function getDefaultMilestones()
    {
        return [
            10 => 50.00,   // $50 for 10 referrals
            25 => 150.00,  // $150 for 25 referrals
            50 => 350.00,  // $350 for 50 referrals
            100 => 750.00, // $750 for 100 referrals
            250 => 2000.00, // $2000 for 250 referrals
            500 => 5000.00, // $5000 for 500 referrals
        ];
    }
}
