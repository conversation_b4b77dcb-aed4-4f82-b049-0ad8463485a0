@extends('layouts.admin')

@section('title', 'Stage Management')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Stage Management</h1>
                <p class="mt-2 text-gray-600">Manage life journey stages and commission rates</p>
            </div>
        </div>
    </div>

    <!-- Stages Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Life Journey Stages</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stage</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pricing</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commission</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bonus</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activations</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($stages as $stage)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $stage->name }}</div>
                            <div class="text-sm text-gray-500">{{ $stage->description }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <form method="POST" action="{{ route('admin.stages.update', $stage) }}" class="space-y-2">
                                @csrf
                                @method('PUT')
                                <div class="flex items-center space-x-2">
                                    <label class="text-xs text-gray-500">Activation:</label>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-sm text-gray-500">$</span>
                                        <input type="number" name="activation_price" step="0.01" min="0"
                                               value="{{ $stage->activation_price ?? 0 }}"
                                               class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-green-500 focus:border-green-500">
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <label class="text-xs text-gray-500">Monthly:</label>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-sm text-gray-500">$</span>
                                        <input type="number" name="monthly_fee" step="0.01" min="0"
                                               value="{{ $stage->monthly_fee ?? 0 }}"
                                               class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-green-500 focus:border-green-500">
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <label class="flex items-center text-xs text-gray-500">
                                        <input type="checkbox" name="requires_approval" value="1"
                                               {{ $stage->requires_approval ? 'checked' : '' }}
                                               class="mr-1 rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                        Requires Approval
                                    </label>
                                </div>
                                <input type="hidden" name="commission_amount" value="{{ $stage->commission_amount ?? $stage->commission_rate }}">
                                <input type="hidden" name="activation_bonus" value="{{ $stage->activation_bonus ?? 0 }}">
                                <input type="hidden" name="is_active" value="{{ $stage->is_active ? '1' : '0' }}">
                                <button type="submit" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                                    Update Pricing
                                </button>
                            </form>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <form method="POST" action="{{ route('admin.stages.update', $stage) }}" class="flex items-center space-x-2">
                                @csrf
                                @method('PUT')
                                <div class="flex items-center space-x-1">
                                    <span class="text-sm text-gray-500">$</span>
                                    <input type="number" name="commission_amount" step="0.01" min="0"
                                           value="{{ $stage->commission_amount ?? $stage->commission_rate }}"
                                           class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-green-500 focus:border-green-500">
                                </div>
                                <input type="hidden" name="activation_bonus" value="{{ $stage->activation_bonus ?? 0 }}">
                                <input type="hidden" name="is_active" value="{{ $stage->is_active ? '1' : '0' }}">
                                <button type="submit" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                                    Update
                                </button>
                            </form>
                            <div class="text-sm text-gray-500 mt-1">per activation</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <form method="POST" action="{{ route('admin.stages.update', $stage) }}" class="flex items-center space-x-2">
                                @csrf
                                @method('PUT')
                                <div class="flex items-center space-x-1">
                                    <span class="text-sm text-gray-500">$</span>
                                    <input type="number" name="activation_bonus" step="0.01" min="0"
                                           value="{{ $stage->activation_bonus ?? 0 }}"
                                           class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-green-500 focus:border-green-500">
                                </div>
                                <input type="hidden" name="commission_amount" value="{{ $stage->commission_amount ?? $stage->commission_rate }}">
                                <input type="hidden" name="is_active" value="{{ $stage->is_active ? '1' : '0' }}">
                                <button type="submit" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                                    Update
                                </button>
                            </form>
                            <div class="text-sm text-gray-500 mt-1">activation bonus</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $stageStats[$stage->id]['total_activations'] ?? 0 }}</div>
                            <div class="text-sm text-gray-500">{{ $stageStats[$stage->id]['active_activations'] ?? 0 }} active</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${{ number_format($stageStats[$stage->id]['total_commissions'] ?? 0, 2) }}</div>
                            <div class="text-sm text-gray-500">total revenue</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <form method="POST" action="{{ route('admin.stages.update', $stage) }}" class="inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="commission_amount" value="{{ $stage->commission_amount ?? $stage->commission_rate }}">
                                <input type="hidden" name="activation_bonus" value="{{ $stage->activation_bonus ?? 0 }}">
                                <input type="hidden" name="is_active" value="{{ $stage->is_active ? '0' : '1' }}">
                                @if($stage->is_active)
                                <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                        onclick="return confirm('Deactivate this stage?')">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                    Deactivate
                                </button>
                                @else
                                <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    Activate
                                </button>
                                @endif
                            </form>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Stage Performance Summary -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Activations</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $stats['total_activations'] }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ number_format($stats['total_revenue'] ?? 0, 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Stages</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $stages->where('is_active', true)->count() }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Box -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Stage Information</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>Life journey stages represent different levels of sustainable living commitment. Users can activate multiple stages simultaneously to maximize their earning potential.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
