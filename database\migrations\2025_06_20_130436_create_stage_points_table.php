<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stage_points', function (Blueprint $table) {
            $table->id();
            $table->string('stage_name');
            $table->string('point_type'); // daily_reward, referral_bonus, challenge_completion, etc.
            $table->integer('points_amount');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Add stage activation requirements
        Schema::create('stage_activations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('stage_name');
            $table->decimal('amount_paid', 10, 2);
            $table->string('payment_method'); // stripe, paypal, bank_transfer
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->timestamp('activated_at')->nullable();
            $table->text('admin_notes')->nullable();
            $table->timestamps();
        });

        // Withheld earnings for stage requirements
        Schema::create('withheld_earnings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('referral_id')->constrained('users')->onDelete('cascade');
            $table->string('required_stage');
            $table->decimal('amount', 10, 2);
            $table->string('earning_type'); // referral_commission, bonus, etc.
            $table->boolean('released')->default(false);
            $table->timestamp('released_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('withheld_earnings');
        Schema::dropIfExists('stage_activations');
        Schema::dropIfExists('stage_points');
    }
};
