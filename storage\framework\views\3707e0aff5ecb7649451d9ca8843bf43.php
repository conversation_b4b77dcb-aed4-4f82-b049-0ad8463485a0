<?php $__env->startSection('title', 'Layout Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="<?php echo e(route('admin.platform-customization.dashboard')); ?>" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                                </svg>
                                <span class="sr-only">Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Layout</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="text-3xl font-bold text-gray-900 mt-2">Layout Settings</h1>
                <p class="text-gray-600 mt-1">Configure page structure, navigation, and responsive design</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="resetToDefaults('layout')" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset to Default
                </button>
                <button onclick="saveLayoutSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Sub-navigation -->
    <div class="mb-8" x-data="{ activeSubTab: 'structure' }">
        <div class="flex space-x-1 bg-gray-50 p-1 rounded-lg">
            <button @click="activeSubTab = 'structure'" :class="activeSubTab === 'structure' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Page Structure</button>
            <button @click="activeSubTab = 'navigation'" :class="activeSubTab === 'navigation' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Navigation</button>
            <button @click="activeSubTab = 'responsive'" :class="activeSubTab === 'responsive' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Responsive</button>
            <button @click="activeSubTab = 'widgets'" :class="activeSubTab === 'widgets' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Widgets</button>
        </div>

        <!-- Page Structure Tab -->
        <div x-show="activeSubTab === 'structure'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🏗️ Page Structure</h3>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Layout Options -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Layout Type</h4>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="radio" name="layout_type" value="full-width" checked class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                <span class="ml-3 text-sm text-gray-700">Full Width</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="layout_type" value="boxed" class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                <span class="ml-3 text-sm text-gray-700">Boxed Layout</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="layout_type" value="fluid" class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                <span class="ml-3 text-sm text-gray-700">Fluid Layout</span>
                            </label>
                        </div>
                    </div>

                    <!-- Container Settings -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Container Settings</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Max Width</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="1200">1200px</option>
                                    <option value="1400">1400px</option>
                                    <option value="1600">1600px</option>
                                    <option value="full">Full Width</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Content Padding</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" min="0" max="50" value="20" class="flex-1">
                                    <span class="w-12 text-sm text-gray-600">20px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Header Settings -->
                <div class="mt-8">
                    <h4 class="text-base font-medium text-gray-900 mb-4">Header Configuration</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Sticky Header</h5>
                                    <p class="text-sm text-gray-500">Header stays at top when scrolling</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Header Height</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" min="60" max="120" value="80" class="flex-1">
                                    <span class="w-12 text-sm text-gray-600">80px</span>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Show Search Bar</h5>
                                    <p class="text-sm text-gray-500">Display search in header</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Show User Menu</h5>
                                    <p class="text-sm text-gray-500">Display user dropdown menu</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Settings -->
                <div class="mt-8">
                    <h4 class="text-base font-medium text-gray-900 mb-4">Footer Configuration</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Show Footer</h5>
                                    <p class="text-sm text-gray-500">Display footer on all pages</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Sticky Footer</h5>
                                    <p class="text-sm text-gray-500">Footer sticks to bottom of page</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Footer Columns</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="1">1 Column</option>
                                    <option value="2">2 Columns</option>
                                    <option value="3" selected>3 Columns</option>
                                    <option value="4">4 Columns</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include external JavaScript -->
<script src="<?php echo e(asset('js/admin/platform-customization.js')); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/admin/platform-customization/layout.blade.php ENDPATH**/ ?>