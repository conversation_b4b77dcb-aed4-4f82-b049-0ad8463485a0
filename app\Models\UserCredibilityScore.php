<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserCredibilityScore extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'total_score',
        'projects_created',
        'projects_completed',
        'projects_approved',
        'volunteer_hours',
        'donations_made',
        'votes_cast',
        'achievements',
        'credibility_level'
    ];

    protected $casts = [
        'achievements' => 'array',
        'donations_made' => 'decimal:2'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getCredibilityLevelDisplayAttribute(): string
    {
        return ucfirst($this->credibility_level);
    }

    public function getCredibilityLevelColorAttribute(): string
    {
        return match($this->credibility_level) {
            'leader' => 'bg-purple-100 text-purple-800',
            'champion' => 'bg-blue-100 text-blue-800',
            'advocate' => 'bg-green-100 text-green-800',
            'contributor' => 'bg-yellow-100 text-yellow-800',
            'newcomer' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    public function getAchievementsListAttribute(): array
    {
        return $this->achievements ?: [];
    }

    // Scopes
    public function scopeByLevel($query, $level)
    {
        return $query->where('credibility_level', $level);
    }

    public function scopeTopScorers($query)
    {
        return $query->orderBy('total_score', 'desc');
    }

    // Methods
    public function updateScore(): void
    {
        $this->total_score = $this->calculateTotalScore();
        $this->credibility_level = $this->calculateCredibilityLevel();
        $this->save();
    }

    private function calculateTotalScore(): int
    {
        $score = 0;
        
        // Points for projects
        $score += $this->projects_created * 10;
        $score += $this->projects_completed * 50;
        $score += $this->projects_approved * 25;
        
        // Points for volunteering
        $score += $this->volunteer_hours * 2;
        
        // Points for donations (1 point per $10 donated)
        $score += floor($this->donations_made / 10);
        
        // Points for voting participation
        $score += $this->votes_cast * 1;
        
        return $score;
    }

    private function calculateCredibilityLevel(): string
    {
        $score = $this->total_score;
        
        if ($score >= 1000) {
            return 'leader';
        } elseif ($score >= 500) {
            return 'champion';
        } elseif ($score >= 200) {
            return 'advocate';
        } elseif ($score >= 50) {
            return 'contributor';
        } else {
            return 'newcomer';
        }
    }

    public function addAchievement(string $achievement): void
    {
        $achievements = $this->achievements_list;
        
        if (!in_array($achievement, $achievements)) {
            $achievements[] = $achievement;
            $this->achievements = $achievements;
            $this->save();
        }
    }

    public function incrementProjectsCreated(): void
    {
        $this->projects_created++;
        $this->updateScore();
    }

    public function incrementProjectsCompleted(): void
    {
        $this->projects_completed++;
        $this->updateScore();
    }

    public function incrementProjectsApproved(): void
    {
        $this->projects_approved++;
        $this->updateScore();
    }

    public function addVolunteerHours(int $hours): void
    {
        $this->volunteer_hours += $hours;
        $this->updateScore();
    }

    public function addDonation(float $amount): void
    {
        $this->donations_made += $amount;
        $this->updateScore();
    }

    public function incrementVotesCast(): void
    {
        $this->votes_cast++;
        $this->updateScore();
    }
}
