<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('community_projects', function (Blueprint $table) {
            $table->id();
            $table->string('project_id')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->text('requirements');
            $table->decimal('amount_needed', 15, 2)->default(0);
            $table->enum('project_type', [
                'environmentalist',
                'conservationist',
                'stewards',
                'volunteers',
                'philanthropy',
                'education',
                'health',
                'community_development',
                'technology',
                'arts_culture'
            ]);
            $table->enum('status', [
                'petition', 
                'featured', 
                'closed', 
                'denied', 
                'cancelled'
            ])->default('petition');
            $table->integer('votes_count')->default(0);
            $table->integer('votes_required')->default(10);
            $table->datetime('voting_deadline');
            $table->datetime('project_deadline')->nullable();
            $table->integer('project_duration_days')->nullable();
            $table->decimal('amount_raised', 15, 2)->default(0);
            $table->integer('volunteer_count')->default(0);
            $table->json('images')->nullable();
            $table->json('videos')->nullable();
            $table->json('documents')->nullable();
            $table->text('admin_notes')->nullable();
            $table->datetime('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->datetime('featured_at')->nullable();
            $table->datetime('closed_at')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->integer('credibility_score')->default(0);
            $table->timestamps();
            
            $table->index(['status', 'created_at']);
            $table->index(['user_id', 'status']);
            $table->index(['voting_deadline']);
            $table->index(['project_deadline']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('community_projects');
    }
};
