<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MarketplaceCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the products for this category.
     */
    public function products()
    {
        return $this->hasMany(MarketplaceProduct::class, 'category_id');
    }

    /**
     * Get active products for this category.
     */
    public function activeProducts()
    {
        return $this->hasMany(MarketplaceProduct::class, 'category_id')->where('status', 'active');
    }
}
