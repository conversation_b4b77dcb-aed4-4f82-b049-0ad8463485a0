<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    protected $fillable = [
        'user_id',
        'transaction_id',
        'type',
        'amount',
        'currency',
        'category',
        'description',
        'payment_method',
        'reference_id',
        'status',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function generateTransactionId()
    {
        return 'TXN-' . strtoupper(uniqid());
    }
}
