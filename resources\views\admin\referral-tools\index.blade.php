@extends('layouts.admin')

@section('title', 'Referral Tools Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Referral Tools Management System</h1>
                <p class="text-gray-600 mt-1">Manage referral tools, bonuses, and awareness campaigns</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportReferralData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Data
                </button>
                <button onclick="createReferralTool()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Create Tool
                </button>
            </div>
        </div>
    </div>

    <!-- Referral Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">12,847</h3>
                    <p class="text-sm text-gray-600">Total Referrals</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">$89,420</h3>
                    <p class="text-sm text-gray-600">Total Commissions</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">2,456</h3>
                    <p class="text-sm text-gray-600">Active Campaigns</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">847</h3>
                    <p class="text-sm text-gray-600">Bonus Rewards</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'tools' }">
                <button @click="activeTab = 'tools'" :class="activeTab === 'tools' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Referral Tools
                </button>
                <button @click="activeTab = 'bonuses'" :class="activeTab === 'bonuses' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Bonus Management
                </button>
                <button @click="activeTab = 'campaigns'" :class="activeTab === 'campaigns' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Awareness Campaigns
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Analytics
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'tools' }">
        <!-- Referral Tools Tab -->
        <div x-show="activeTab === 'tools'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Referral Tools Management</h3>
                        <button onclick="bulkToolActions()" class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Bulk Actions
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Referral Link Generator -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Referral Link Generator</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Usage:</span>
                                    <span class="font-medium">12,847 links generated</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Conversion:</span>
                                    <span class="font-medium text-green-600">23.4%</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Template:</span>
                                    <span class="font-medium">Customizable</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <button onclick="editTool('link_generator')" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                                <button onclick="viewToolStats('link_generator')" class="text-green-600 hover:text-green-900 text-sm">Stats</button>
                            </div>
                        </div>

                        <!-- Social Media Templates -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Social Media Templates</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Templates:</span>
                                    <span class="font-medium">47 available</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Downloads:</span>
                                    <span class="font-medium">8,923</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Platforms:</span>
                                    <span class="font-medium">FB, IG, TW, LI</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <button onclick="editTool('social_templates')" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                                <button onclick="viewToolStats('social_templates')" class="text-green-600 hover:text-green-900 text-sm">Stats</button>
                            </div>
                        </div>

                        <!-- Email Templates -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Email Templates</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Templates:</span>
                                    <span class="font-medium">23 available</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Sent:</span>
                                    <span class="font-medium">15,642 emails</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Open Rate:</span>
                                    <span class="font-medium text-green-600">34.7%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <button onclick="editTool('email_templates')" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                                <button onclick="viewToolStats('email_templates')" class="text-green-600 hover:text-green-900 text-sm">Stats</button>
                            </div>
                        </div>

                        <!-- Banner Generator -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Banner Generator</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Designs:</span>
                                    <span class="font-medium">156 templates</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Generated:</span>
                                    <span class="font-medium">4,567 banners</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Formats:</span>
                                    <span class="font-medium">PNG, JPG, SVG</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <button onclick="editTool('banner_generator')" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                                <button onclick="viewToolStats('banner_generator')" class="text-green-600 hover:text-green-900 text-sm">Stats</button>
                            </div>
                        </div>

                        <!-- QR Code Generator -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">QR Code Generator</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Generated:</span>
                                    <span class="font-medium">2,847 QR codes</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Scans:</span>
                                    <span class="font-medium">18,923</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Conversion:</span>
                                    <span class="font-medium text-green-600">12.8%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <button onclick="editTool('qr_generator')" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                                <button onclick="viewToolStats('qr_generator')" class="text-green-600 hover:text-green-900 text-sm">Stats</button>
                            </div>
                        </div>

                        <!-- Video Templates -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Video Templates</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Beta
                                </span>
                            </div>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Templates:</span>
                                    <span class="font-medium">12 available</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Generated:</span>
                                    <span class="font-medium">234 videos</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Duration:</span>
                                    <span class="font-medium">15-60 seconds</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <button onclick="editTool('video_templates')" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                                <button onclick="viewToolStats('video_templates')" class="text-green-600 hover:text-green-900 text-sm">Stats</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bonus Management Tab -->
        <div x-show="activeTab === 'bonuses'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Bonus Management</h3>
                        <button onclick="createBonus()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Create Bonus
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bonus Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Criteria</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reward</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Awarded</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Milestone Bonus</div>
                                    <div class="text-sm text-gray-500">50 Referrals Achievement</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">50+ active referrals</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$500</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">247 times</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="editBonus(1)" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                        <button onclick="viewBonusStats(1)" class="text-green-600 hover:text-green-900">Stats</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReferralData() {
    alert('Export referral data functionality will be implemented');
}

function createReferralTool() {
    alert('Create referral tool functionality will be implemented');
}

function bulkToolActions() {
    alert('Bulk tool actions functionality will be implemented');
}

function editTool(toolId) {
    alert(`Edit tool ${toolId} functionality will be implemented`);
}

function viewToolStats(toolId) {
    alert(`View tool stats ${toolId} functionality will be implemented`);
}

function createBonus() {
    alert('Create bonus functionality will be implemented');
}

function editBonus(id) {
    alert(`Edit bonus ${id} functionality will be implemented`);
}

function viewBonusStats(id) {
    alert(`View bonus stats ${id} functionality will be implemented`);
}
</script>
@endsection
