<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Activities table
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stage_id')->constrained('membership_stages')->onDelete('cascade');
            $table->string('type'); // 'leadership', 'knowledge', 'action', 'awareness'
            $table->string('title');
            $table->text('description');
            $table->text('content')->nullable(); // Rich content/instructions
            $table->json('requirements')->nullable(); // Activity requirements
            $table->integer('points')->default(0); // Points awarded for completion
            $table->integer('duration_minutes')->nullable(); // Estimated duration
            $table->string('difficulty_level')->default('beginner'); // beginner, intermediate, advanced
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['stage_id', 'type', 'is_active']);
            $table->index(['type', 'is_active']);
        });

        // User activity completions
        Schema::create('user_activity_completions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('activity_id')->constrained()->onDelete('cascade');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->string('status')->default('not_started'); // not_started, in_progress, completed, verified
            $table->text('notes')->nullable(); // User notes or submission
            $table->json('submission_data')->nullable(); // Activity submission data
            $table->integer('points_earned')->default(0);
            $table->timestamp('verified_at')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['user_id', 'activity_id']);
            $table->index(['user_id', 'status']);
            $table->index(['activity_id', 'status']);
        });

        // Activity resources (files, links, etc.)
        Schema::create('activity_resources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('activity_id')->constrained()->onDelete('cascade');
            $table->string('type'); // 'file', 'link', 'video', 'document'
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('url')->nullable(); // For links/videos
            $table->string('file_path')->nullable(); // For uploaded files
            $table->string('file_type')->nullable(); // MIME type
            $table->integer('file_size')->nullable(); // File size in bytes
            $table->integer('sort_order')->default(0);
            $table->boolean('is_required')->default(false);
            $table->timestamps();

            $table->index(['activity_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_resources');
        Schema::dropIfExists('user_activity_completions');
        Schema::dropIfExists('activities');
    }
};
