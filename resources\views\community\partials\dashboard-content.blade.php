<!-- Community Dashboard Content -->
<div class="space-y-8">
    <!-- Overview Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
        <!-- Projects Overview -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Projects</h3>
                <a href="{{ route('community.index', ['tab' => 'projects']) }}" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">View All</a>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Total Projects</span>
                    <span class="text-sm font-medium">{{ $stats['total_projects'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Active</span>
                    <span class="text-sm font-medium text-green-600">{{ $stats['active_projects'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Completed</span>
                    <span class="text-sm font-medium text-blue-600">{{ $stats['completed_projects'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Success Rate</span>
                    <span class="text-sm font-medium text-purple-600">{{ $stats['success_rate'] }}%</span>
                </div>
            </div>
        </div>

        <!-- Crowdfunding Overview -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Crowdfunding</h3>
                <a href="{{ route('community.index', ['tab' => 'crowdfund']) }}" class="text-green-600 hover:text-green-500 text-sm font-medium">View All</a>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Total Campaigns</span>
                    <span class="text-sm font-medium">{{ $stats['total_campaigns'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Active</span>
                    <span class="text-sm font-medium text-green-600">{{ $stats['active_campaigns'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Total Raised</span>
                    <span class="text-sm font-medium text-green-600">${{ number_format($stats['total_campaign_raised']) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Contributors</span>
                    <span class="text-sm font-medium text-blue-600">{{ $stats['total_contributors'] }}</span>
                </div>
            </div>
        </div>

        <!-- Discussions Overview -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Discussions</h3>
                <a href="{{ route('community.index', ['tab' => 'discussions']) }}" class="text-blue-600 hover:text-blue-500 text-sm font-medium">View All</a>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Total Topics</span>
                    <span class="text-sm font-medium">{{ $stats['total_discussions'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Active</span>
                    <span class="text-sm font-medium text-green-600">{{ $stats['active_discussions'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Participants</span>
                    <span class="text-sm font-medium text-blue-600">{{ $stats['total_participants'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">New This Week</span>
                    <span class="text-sm font-medium text-purple-600">{{ $stats['new_discussions_week'] }}</span>
                </div>
            </div>
        </div>

        <!-- Events Overview -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Events</h3>
                <a href="{{ route('community.index', ['tab' => 'events']) }}" class="text-purple-600 hover:text-purple-500 text-sm font-medium">View All</a>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Upcoming</span>
                    <span class="text-sm font-medium text-green-600">{{ $stats['upcoming_events'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Total Attendees</span>
                    <span class="text-sm font-medium">{{ $stats['total_attendees'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Completed</span>
                    <span class="text-sm font-medium text-blue-600">{{ $stats['completed_events'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">This Month</span>
                    <span class="text-sm font-medium text-purple-600">{{ $stats['events_this_month'] }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Community Activity -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Community Activity</h3>
        </div>
        <div class="p-6">
            @if(isset($recentActivity) && $recentActivity->count() > 0)
                <div class="space-y-4">
                    @foreach($recentActivity as $activity)
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-{{ $activity->color }}-100 rounded-full flex items-center justify-center">
                                @if($activity->icon === 'project')
                                    <svg class="w-5 h-5 text-{{ $activity->color }}-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                    </svg>
                                @elseif($activity->icon === 'campaign')
                                    <svg class="w-5 h-5 text-{{ $activity->color }}-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                    </svg>
                                @elseif($activity->icon === 'discussion')
                                    <svg class="w-5 h-5 text-{{ $activity->color }}-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                                    </svg>
                                @elseif($activity->icon === 'event')
                                    <svg class="w-5 h-5 text-{{ $activity->color }}-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 text-{{ $activity->color }}-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                @endif
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900">{{ $activity->title }}</p>
                            <p class="text-sm text-gray-500">{{ $activity->description }}</p>
                            <div class="mt-1 flex items-center space-x-2 text-xs text-gray-400">
                                <span>by {{ $activity->user }}</span>
                                <span>•</span>
                                <span>{{ $activity->created_at->diffForHumans() }}</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM9 6v10h6V6H9z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
                    <p class="mt-1 text-sm text-gray-500">Community activity will appear here as it happens.</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Community Leaderboard Preview -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Top Contributors</h3>
            <a href="{{ route('community.index', ['tab' => 'leaderboard']) }}" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">View Full Leaderboard</a>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                @for($i = 1; $i <= 5; $i++)
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-gray-600">#{{ $i }}</span>
                            </div>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Member {{ $i }}</p>
                            <p class="text-xs text-gray-500">{{ rand(5, 25) }} contributions</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">{{ number_format(rand(1000, 5000)) }} points</p>
                    </div>
                </div>
                @endfor
            </div>
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    // Add refresh functionality
    location.reload();
}
</script>
