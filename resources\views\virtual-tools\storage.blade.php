@extends('layouts.app')

@section('title', 'Virtual Private Storage')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg p-8 mb-8 text-white">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-4">☁️ Virtual Private Storage</h1>
            <p class="text-xl text-purple-100">Secure cloud storage with multiple plan options</p>
        </div>
    </div>

    <!-- Storage Plans -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Free Plan -->
        <div class="bg-white shadow rounded-lg p-6 border-2 border-green-200">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Free Plan</h3>
                <div class="text-3xl font-bold text-green-600 mb-4">$0<span class="text-lg text-gray-500">/month</span></div>
                <ul class="text-sm text-gray-600 space-y-2 mb-6">
                    <li>✓ 5GB Storage</li>
                    <li>✓ Basic Encryption</li>
                    <li>✓ File Sharing</li>
                    <li>✓ Mobile Access</li>
                    <li>✓ Email Support</li>
                </ul>
                <button class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Current Plan</button>
            </div>
        </div>

        <!-- Pro Plan -->
        <div class="bg-white shadow rounded-lg p-6 border-2 border-blue-500 relative">
            <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Pro Plan</h3>
                <div class="text-3xl font-bold text-blue-600 mb-4">$9.99<span class="text-lg text-gray-500">/month</span></div>
                <ul class="text-sm text-gray-600 space-y-2 mb-6">
                    <li>✓ 100GB Storage</li>
                    <li>✓ Advanced Encryption</li>
                    <li>✓ Unlimited File Sharing</li>
                    <li>✓ Mobile & Desktop Apps</li>
                    <li>✓ Priority Support</li>
                    <li>✓ Version History</li>
                    <li>✓ Team Collaboration</li>
                </ul>
                <button class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">Upgrade Now</button>
            </div>
        </div>

        <!-- Enterprise Plan -->
        <div class="bg-white shadow rounded-lg p-6 border-2 border-purple-200">
            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
                <div class="text-3xl font-bold text-purple-600 mb-4">$29.99<span class="text-lg text-gray-500">/month</span></div>
                <ul class="text-sm text-gray-600 space-y-2 mb-6">
                    <li>✓ 1TB Storage</li>
                    <li>✓ Military-Grade Encryption</li>
                    <li>✓ Advanced Admin Controls</li>
                    <li>✓ API Access</li>
                    <li>✓ 24/7 Phone Support</li>
                    <li>✓ Custom Integrations</li>
                    <li>✓ Compliance Tools</li>
                </ul>
                <button class="w-full bg-purple-600 text-white py-2 rounded hover:bg-purple-700">Contact Sales</button>
            </div>
        </div>
    </div>

    <!-- Storage Dashboard -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Storage Usage -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Storage Usage</h2>
                <div class="mb-4">
                    <div class="flex justify-between text-sm mb-2">
                        <span>Used: 3.2 GB</span>
                        <span>Available: 1.8 GB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="bg-blue-600 h-3 rounded-full" style="width: 64%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">64% of 5GB used</p>
                </div>
                
                <!-- File Type Breakdown -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">1.2GB</div>
                        <div class="text-sm text-gray-600">Documents</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">0.8GB</div>
                        <div class="text-sm text-gray-600">Images</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">0.9GB</div>
                        <div class="text-sm text-gray-600">Videos</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">0.3GB</div>
                        <div class="text-sm text-gray-600">Other</div>
                    </div>
                </div>
            </div>

            <!-- Recent Files -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Recent Files</h2>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Project_Report.pdf</h3>
                                <p class="text-sm text-gray-500">2.3 MB • 2 hours ago</p>
                            </div>
                        </div>
                        <button class="text-gray-400 hover:text-gray-600">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">vacation_photo.jpg</h3>
                                <p class="text-sm text-gray-500">5.1 MB • 1 day ago</p>
                            </div>
                        </div>
                        <button class="text-gray-400 hover:text-gray-600">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">presentation.mp4</h3>
                                <p class="text-sm text-gray-500">45.2 MB • 3 days ago</p>
                            </div>
                        </div>
                        <button class="text-gray-400 hover:text-gray-600">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
            <!-- Upload Area -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Upload Files</h3>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                    <div class="mt-4">
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Choose Files</button>
                        <p class="mt-2 text-sm text-gray-500">or drag and drop</p>
                    </div>
                </div>
            </div>

            <!-- Security Features -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Security Features</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm text-gray-700">End-to-end encryption</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm text-gray-700">Zero-knowledge architecture</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm text-gray-700">Regular security audits</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <button class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">Create Folder</button>
                    <button class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Share Files</button>
                    <button class="w-full bg-purple-600 text-white py-2 rounded hover:bg-purple-700">Backup Settings</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
