<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentGateway extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'is_active',
        'is_sandbox',
        'configuration',
        'supported_currencies',
        'transaction_fee_percentage',
        'transaction_fee_fixed',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_sandbox' => 'boolean',
        'configuration' => 'array',
        'supported_currencies' => 'array',
        'transaction_fee_percentage' => 'decimal:2',
        'transaction_fee_fixed' => 'decimal:2',
    ];

    /**
     * Get the payment transactions for this gateway.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    /**
     * Get the payment methods for this gateway.
     */
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class);
    }

    /**
     * Get the webhook events for this gateway.
     */
    public function webhookEvents(): HasMany
    {
        return $this->hasMany(PaymentWebhookEvent::class);
    }

    /**
     * Scope for active gateways.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for production gateways.
     */
    public function scopeProduction($query)
    {
        return $query->where('is_sandbox', false);
    }

    /**
     * Scope for sandbox gateways.
     */
    public function scopeSandbox($query)
    {
        return $query->where('is_sandbox', true);
    }

    /**
     * Get active payment gateways ordered by sort order.
     */
    public static function getActive()
    {
        return self::active()
            ->orderBy('sort_order')
            ->orderBy('display_name')
            ->get();
    }

    /**
     * Check if gateway supports a currency.
     */
    public function supportsCurrency(string $currency): bool
    {
        if (!$this->supported_currencies) {
            return true; // If no specific currencies defined, support all
        }

        return in_array(strtoupper($currency), $this->supported_currencies);
    }

    /**
     * Calculate transaction fee.
     */
    public function calculateFee(float $amount): float
    {
        $percentageFee = ($amount * $this->transaction_fee_percentage) / 100;
        return $percentageFee + $this->transaction_fee_fixed;
    }

    /**
     * Get configuration value.
     */
    public function getConfig(string $key, $default = null)
    {
        return $this->configuration[$key] ?? $default;
    }

    /**
     * Set configuration value.
     */
    public function setConfig(string $key, $value): void
    {
        $config = $this->configuration ?? [];
        $config[$key] = $value;
        $this->configuration = $config;
        $this->save();
    }

    /**
     * Get gateway status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        if (!$this->is_active) {
            return 'bg-red-100 text-red-800';
        }

        if ($this->is_sandbox) {
            return 'bg-yellow-100 text-yellow-800';
        }

        return 'bg-green-100 text-green-800';
    }

    /**
     * Get gateway status text.
     */
    public function getStatusTextAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        if ($this->is_sandbox) {
            return 'Sandbox';
        }

        return 'Live';
    }

    /**
     * Get gateway icon.
     */
    public function getIconAttribute(): string
    {
        return match ($this->name) {
            'stripe' => 'M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z',
            'paypal' => 'M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.028-.026.056-.052.08-.744 6.137-4.644 8.51-9.83 8.51h-2.65c-.524 0-.968.382-1.05.9l-1.12 7.106h4.184a.641.641 0 0 0 .633-.74l.033-.205.629-3.98.04-.26a.641.641 0 0 1 .633-.74h.398c3.66 0 6.526-1.487 7.36-5.781.348-1.797.167-3.297-.784-4.349z',
            'paystack' => 'M0 0h24v24H0z',
            default => 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z',
        };
    }

    /**
     * Create default payment gateways.
     */
    public static function createDefaults(): void
    {
        $gateways = [
            [
                'name' => 'stripe',
                'display_name' => 'Stripe',
                'is_active' => false,
                'is_sandbox' => true,
                'configuration' => [
                    'public_key' => '',
                    'secret_key' => '',
                    'webhook_secret' => '',
                ],
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
                'transaction_fee_percentage' => 2.9,
                'transaction_fee_fixed' => 0.30,
                'sort_order' => 1,
            ],
            [
                'name' => 'paypal',
                'display_name' => 'PayPal',
                'is_active' => false,
                'is_sandbox' => true,
                'configuration' => [
                    'client_id' => '',
                    'client_secret' => '',
                    'webhook_id' => '',
                ],
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
                'transaction_fee_percentage' => 3.49,
                'transaction_fee_fixed' => 0.49,
                'sort_order' => 2,
            ],
            [
                'name' => 'paystack',
                'display_name' => 'Paystack',
                'is_active' => false,
                'is_sandbox' => true,
                'configuration' => [
                    'public_key' => '',
                    'secret_key' => '',
                ],
                'supported_currencies' => ['NGN', 'USD', 'GHS', 'ZAR'],
                'transaction_fee_percentage' => 1.5,
                'transaction_fee_fixed' => 0.00,
                'sort_order' => 3,
            ],
        ];

        foreach ($gateways as $gateway) {
            self::firstOrCreate(
                ['name' => $gateway['name']],
                $gateway
            );
        }
    }
}
