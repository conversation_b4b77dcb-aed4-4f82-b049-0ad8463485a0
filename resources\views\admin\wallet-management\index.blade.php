@extends('layouts.admin')

@section('title', 'Wallet Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ route('admin.dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"></path>
                    </svg>
                    Admin Dashboard
                </a>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Wallet Management</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Wallet Management System</h1>
                <p class="text-gray-600 mt-1">Manage currencies, wallets, and payment methods</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="addCurrency()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    Add Currency
                </button>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'currencies' }">
                <button @click="activeTab = 'currencies'" :class="activeTab === 'currencies' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Currency Management
                </button>
                <button @click="activeTab = 'bank'" :class="activeTab === 'bank' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Bank Transfer Settings
                </button>
                <button @click="activeTab = 'conversions'" :class="activeTab === 'conversions' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Conversion History
                </button>
                <button @click="activeTab = 'invoices'" :class="activeTab === 'invoices' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Invoice Management
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'currencies' }">
        <!-- Currency Management Tab -->
        <div x-show="activeTab === 'currencies'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Available Currencies</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- USD Currency -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <span class="text-lg font-bold text-green-600">$</span>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-medium text-gray-900">USD</h4>
                                        <p class="text-sm text-gray-500">US Dollar</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Exchange Rate:</span>
                                    <span class="font-medium">1.00000000</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withdrawal Fee:</span>
                                    <span class="font-medium">$2.50</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Conversion Fee:</span>
                                    <span class="font-medium">1.5%</span>
                                </div>
                            </div>

                            <div class="mt-4 space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Allow Withdrawal</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Conversion Fee Enabled</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>

                            <div class="mt-4 flex items-center justify-between">
                                <button onclick="editCurrency('USD')" class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    Edit
                                </button>
                                <button onclick="deleteCurrency('USD')" class="text-red-600 hover:text-red-900 text-sm">
                                    Delete
                                </button>
                            </div>
                        </div>

                        <!-- NGN Currency -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-lg font-bold text-blue-600">₦</span>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-medium text-gray-900">NGN</h4>
                                        <p class="text-sm text-gray-500">Nigerian Naira</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Exchange Rate:</span>
                                    <span class="font-medium">1,650.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withdrawal Fee:</span>
                                    <span class="font-medium">₦500.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Conversion Fee:</span>
                                    <span class="font-medium">2.0%</span>
                                </div>
                            </div>

                            <div class="mt-4 space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Allow Withdrawal</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Conversion Fee Enabled</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>

                            <div class="mt-4 flex items-center justify-between">
                                <button onclick="editCurrency('NGN')" class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    Edit
                                </button>
                                <button onclick="deleteCurrency('NGN')" class="text-red-600 hover:text-red-900 text-sm">
                                    Delete
                                </button>
                            </div>
                        </div>

                        <!-- EUR Currency -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                        <span class="text-lg font-bold text-purple-600">€</span>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-medium text-gray-900">EUR</h4>
                                        <p class="text-sm text-gray-500">Euro</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                    Inactive
                                </span>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Exchange Rate:</span>
                                    <span class="font-medium">0.92000000</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Withdrawal Fee:</span>
                                    <span class="font-medium">€2.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Conversion Fee:</span>
                                    <span class="font-medium">1.8%</span>
                                </div>
                            </div>

                            <div class="mt-4 space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Allow Withdrawal</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Conversion Fee Enabled</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>

                            <div class="mt-4 flex items-center justify-between">
                                <button onclick="editCurrency('EUR')" class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    Edit
                                </button>
                                <button onclick="deleteCurrency('EUR')" class="text-red-600 hover:text-red-900 text-sm">
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Transfer Settings Tab -->
        <div x-show="activeTab === 'bank'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Bank Transfer Settings</h3>
                    <button onclick="addBankAccount()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        Add Bank Account
                    </button>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- USD Bank Account -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900">USD Bank Account</h4>
                                    <p class="text-sm text-gray-500">For US Dollar payments</p>
                                </div>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Bank Name</label>
                                    <input type="text" value="Chase Bank" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Account Name</label>
                                    <input type="text" value="Environmental Community Platform LLC" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                                    <input type="text" value="****1234" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Routing Number</label>
                                    <input type="text" value="*********" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                </div>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Payment Instructions</label>
                                <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>Please include your invoice number in the payment reference. Payments typically take 1-3 business days to process.</textarea>
                            </div>

                            <div class="mt-4 flex items-center justify-end space-x-3">
                                <button onclick="editBankAccount('USD')" class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    Edit
                                </button>
                                <button onclick="deleteBankAccount('USD')" class="text-red-600 hover:text-red-900 text-sm">
                                    Delete
                                </button>
                            </div>
                        </div>

                        <!-- NGN Bank Account -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900">NGN Bank Account</h4>
                                    <p class="text-sm text-gray-500">For Nigerian Naira payments</p>
                                </div>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Bank Name</label>
                                    <input type="text" value="First Bank of Nigeria" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Account Name</label>
                                    <input type="text" value="Environmental Community Platform Nigeria" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                                    <input type="text" value="****5678" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Sort Code</label>
                                    <input type="text" value="*********" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                                </div>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Payment Instructions</label>
                                <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>Please include your invoice number in the payment narration. Payments are processed within 24 hours during business days.</textarea>
                            </div>

                            <div class="mt-4 flex items-center justify-end space-x-3">
                                <button onclick="editBankAccount('NGN')" class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    Edit
                                </button>
                                <button onclick="deleteBankAccount('NGN')" class="text-red-600 hover:text-red-900 text-sm">
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional tabs will be added in the next part -->
    </div>
</div>

<script>
function addCurrency() {
    // Implementation for adding new currency
    alert('Add Currency functionality will be implemented');
}

function editCurrency(code) {
    // Implementation for editing currency
    alert(`Edit ${code} currency functionality will be implemented`);
}

function deleteCurrency(code) {
    if (confirm(`Are you sure you want to delete ${code} currency?`)) {
        // Implementation for deleting currency
        alert(`Delete ${code} currency functionality will be implemented`);
    }
}

function addBankAccount() {
    // Implementation for adding bank account
    alert('Add Bank Account functionality will be implemented');
}

function editBankAccount(currency) {
    // Implementation for editing bank account
    alert(`Edit ${currency} bank account functionality will be implemented`);
}

function deleteBankAccount(currency) {
    if (confirm(`Are you sure you want to delete ${currency} bank account?`)) {
        // Implementation for deleting bank account
        alert(`Delete ${currency} bank account functionality will be implemented`);
    }
}
</script>
@endsection
