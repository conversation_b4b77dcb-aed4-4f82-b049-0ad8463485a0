<?php $__env->startSection('title', 'Register'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <a href="<?php echo e(route('login')); ?>" class="font-medium text-indigo-600 hover:text-indigo-500">
                    sign in to your existing account
                </a>
            </p>
        </div>

        <?php if($referrer): ?>
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">
                        You're being referred by <?php echo e($referrer->name); ?>

                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>Complete your registration to join their team and start earning commissions!</p>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <form class="mt-8 space-y-6" method="POST" action="<?php echo e(route('register')); ?>">
            <?php echo csrf_field(); ?>
            
            <?php if($referralCode): ?>
                <input type="hidden" name="referral_code" value="<?php echo e($referralCode); ?>">
            <?php endif; ?>
            
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input id="first_name" name="first_name" type="text" autocomplete="given-name" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Enter your first name" value="<?php echo e(old('first_name')); ?>">
                        <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input id="last_name" name="last_name" type="text" autocomplete="family-name" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Enter your last name" value="<?php echo e(old('last_name')); ?>">
                        <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Location Fields -->
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                    <input id="address" name="address" type="text" autocomplete="street-address"
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="Enter your address" value="<?php echo e(old('address')); ?>">
                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                        <input id="city" name="city" type="text" autocomplete="address-level2"
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Enter your city" value="<?php echo e(old('city')); ?>">
                        <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="state" class="block text-sm font-medium text-gray-700">State/Province</label>
                        <input id="state" name="state" type="text" autocomplete="address-level1"
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Enter your state/province" value="<?php echo e(old('state')); ?>">
                        <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div>
                    <label for="country" class="block text-sm font-medium text-gray-700">Country <span class="text-red-500">*</span></label>
                    <select id="country" name="country" autocomplete="country" required
                            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <option value="">Select your country</option>
                        <option value="US" <?php echo e(old('country') === 'US' ? 'selected' : ''); ?>>United States</option>
                        <option value="CA" <?php echo e(old('country') === 'CA' ? 'selected' : ''); ?>>Canada</option>
                        <option value="GB" <?php echo e(old('country') === 'GB' ? 'selected' : ''); ?>>United Kingdom</option>
                        <option value="AU" <?php echo e(old('country') === 'AU' ? 'selected' : ''); ?>>Australia</option>
                        <option value="DE" <?php echo e(old('country') === 'DE' ? 'selected' : ''); ?>>Germany</option>
                        <option value="FR" <?php echo e(old('country') === 'FR' ? 'selected' : ''); ?>>France</option>
                        <option value="IT" <?php echo e(old('country') === 'IT' ? 'selected' : ''); ?>>Italy</option>
                        <option value="ES" <?php echo e(old('country') === 'ES' ? 'selected' : ''); ?>>Spain</option>
                        <option value="NL" <?php echo e(old('country') === 'NL' ? 'selected' : ''); ?>>Netherlands</option>
                        <option value="SE" <?php echo e(old('country') === 'SE' ? 'selected' : ''); ?>>Sweden</option>
                        <option value="NO" <?php echo e(old('country') === 'NO' ? 'selected' : ''); ?>>Norway</option>
                        <option value="DK" <?php echo e(old('country') === 'DK' ? 'selected' : ''); ?>>Denmark</option>
                        <option value="FI" <?php echo e(old('country') === 'FI' ? 'selected' : ''); ?>>Finland</option>
                        <option value="JP" <?php echo e(old('country') === 'JP' ? 'selected' : ''); ?>>Japan</option>
                        <option value="KR" <?php echo e(old('country') === 'KR' ? 'selected' : ''); ?>>South Korea</option>
                        <option value="SG" <?php echo e(old('country') === 'SG' ? 'selected' : ''); ?>>Singapore</option>
                        <option value="NZ" <?php echo e(old('country') === 'NZ' ? 'selected' : ''); ?>>New Zealand</option>
                        <option value="BR" <?php echo e(old('country') === 'BR' ? 'selected' : ''); ?>>Brazil</option>
                        <option value="MX" <?php echo e(old('country') === 'MX' ? 'selected' : ''); ?>>Mexico</option>
                        <option value="IN" <?php echo e(old('country') === 'IN' ? 'selected' : ''); ?>>India</option>
                        <option value="CN" <?php echo e(old('country') === 'CN' ? 'selected' : ''); ?>>China</option>
                        <option value="ZA" <?php echo e(old('country') === 'ZA' ? 'selected' : ''); ?>>South Africa</option>
                        <option value="NG" <?php echo e(old('country') === 'NG' ? 'selected' : ''); ?>>Nigeria</option>
                        <option value="KE" <?php echo e(old('country') === 'KE' ? 'selected' : ''); ?>>Kenya</option>
                        <option value="EG" <?php echo e(old('country') === 'EG' ? 'selected' : ''); ?>>Egypt</option>
                        <option value="AE" <?php echo e(old('country') === 'AE' ? 'selected' : ''); ?>>United Arab Emirates</option>
                        <option value="SA" <?php echo e(old('country') === 'SA' ? 'selected' : ''); ?>>Saudi Arabia</option>
                        <option value="TR" <?php echo e(old('country') === 'TR' ? 'selected' : ''); ?>>Turkey</option>
                        <option value="RU" <?php echo e(old('country') === 'RU' ? 'selected' : ''); ?>>Russia</option>
                        <option value="PL" <?php echo e(old('country') === 'PL' ? 'selected' : ''); ?>>Poland</option>
                        <option value="CZ" <?php echo e(old('country') === 'CZ' ? 'selected' : ''); ?>>Czech Republic</option>
                        <option value="HU" <?php echo e(old('country') === 'HU' ? 'selected' : ''); ?>>Hungary</option>
                        <option value="GR" <?php echo e(old('country') === 'GR' ? 'selected' : ''); ?>>Greece</option>
                        <option value="PT" <?php echo e(old('country') === 'PT' ? 'selected' : ''); ?>>Portugal</option>
                        <option value="IE" <?php echo e(old('country') === 'IE' ? 'selected' : ''); ?>>Ireland</option>
                        <option value="BE" <?php echo e(old('country') === 'BE' ? 'selected' : ''); ?>>Belgium</option>
                        <option value="CH" <?php echo e(old('country') === 'CH' ? 'selected' : ''); ?>>Switzerland</option>
                        <option value="AT" <?php echo e(old('country') === 'AT' ? 'selected' : ''); ?>>Austria</option>
                        <option value="OTHER" <?php echo e(old('country') === 'OTHER' ? 'selected' : ''); ?>>Other</option>
                    </select>
                    <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Date of Birth and Preferred Currency -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700">Date of Birth <span class="text-red-500">*</span></label>
                        <input id="date_of_birth" name="date_of_birth" type="date" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['date_of_birth'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               value="<?php echo e(old('date_of_birth')); ?>" max="<?php echo e(date('Y-m-d', strtotime('-13 years'))); ?>">
                        <?php $__errorArgs = ['date_of_birth'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500">You must be at least 13 years old to register</p>
                    </div>

                    <div>
                        <label for="preferred_currency" class="block text-sm font-medium text-gray-700">Preferred Currency <span class="text-red-500">*</span></label>
                        <select id="preferred_currency" name="preferred_currency" required
                                class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['preferred_currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">Select your preferred currency</option>
                            <option value="USD" <?php echo e(old('preferred_currency') === 'USD' ? 'selected' : ''); ?>>USD - US Dollar</option>
                            <option value="EUR" <?php echo e(old('preferred_currency') === 'EUR' ? 'selected' : ''); ?>>EUR - Euro</option>
                            <option value="GBP" <?php echo e(old('preferred_currency') === 'GBP' ? 'selected' : ''); ?>>GBP - British Pound</option>
                            <option value="CAD" <?php echo e(old('preferred_currency') === 'CAD' ? 'selected' : ''); ?>>CAD - Canadian Dollar</option>
                            <option value="AUD" <?php echo e(old('preferred_currency') === 'AUD' ? 'selected' : ''); ?>>AUD - Australian Dollar</option>
                            <option value="JPY" <?php echo e(old('preferred_currency') === 'JPY' ? 'selected' : ''); ?>>JPY - Japanese Yen</option>
                            <option value="CHF" <?php echo e(old('preferred_currency') === 'CHF' ? 'selected' : ''); ?>>CHF - Swiss Franc</option>
                            <option value="CNY" <?php echo e(old('preferred_currency') === 'CNY' ? 'selected' : ''); ?>>CNY - Chinese Yuan</option>
                            <option value="INR" <?php echo e(old('preferred_currency') === 'INR' ? 'selected' : ''); ?>>INR - Indian Rupee</option>
                            <option value="NGN" <?php echo e(old('preferred_currency') === 'NGN' ? 'selected' : ''); ?>>NGN - Nigerian Naira</option>
                            <option value="ZAR" <?php echo e(old('preferred_currency') === 'ZAR' ? 'selected' : ''); ?>>ZAR - South African Rand</option>
                            <option value="KES" <?php echo e(old('preferred_currency') === 'KES' ? 'selected' : ''); ?>>KES - Kenyan Shilling</option>
                            <option value="EGP" <?php echo e(old('preferred_currency') === 'EGP' ? 'selected' : ''); ?>>EGP - Egyptian Pound</option>
                            <option value="AED" <?php echo e(old('preferred_currency') === 'AED' ? 'selected' : ''); ?>>AED - UAE Dirham</option>
                            <option value="SAR" <?php echo e(old('preferred_currency') === 'SAR' ? 'selected' : ''); ?>>SAR - Saudi Riyal</option>
                            <option value="TRY" <?php echo e(old('preferred_currency') === 'TRY' ? 'selected' : ''); ?>>TRY - Turkish Lira</option>
                            <option value="RUB" <?php echo e(old('preferred_currency') === 'RUB' ? 'selected' : ''); ?>>RUB - Russian Ruble</option>
                            <option value="BRL" <?php echo e(old('preferred_currency') === 'BRL' ? 'selected' : ''); ?>>BRL - Brazilian Real</option>
                            <option value="MXN" <?php echo e(old('preferred_currency') === 'MXN' ? 'selected' : ''); ?>>MXN - Mexican Peso</option>
                            <option value="KRW" <?php echo e(old('preferred_currency') === 'KRW' ? 'selected' : ''); ?>>KRW - South Korean Won</option>
                            <option value="SGD" <?php echo e(old('preferred_currency') === 'SGD' ? 'selected' : ''); ?>>SGD - Singapore Dollar</option>
                            <option value="NZD" <?php echo e(old('preferred_currency') === 'NZD' ? 'selected' : ''); ?>>NZD - New Zealand Dollar</option>
                        </select>
                        <?php $__errorArgs = ['preferred_currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500">This will be your default wallet currency</p>
                    </div>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email Address <span class="text-red-500">*</span></label>
                    <input id="email" name="email" type="email" autocomplete="email" required
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="Enter your email address" value="<?php echo e(old('email')); ?>">
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <div class="relative">
                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input id="password" name="password" type="password" autocomplete="new-password" required
                           class="mt-1 appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="Create a password">
                    <button type="button" onclick="togglePassword('password')"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center mt-6">
                        <svg id="password-eye-open" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg id="password-eye-closed" class="h-5 w-5 text-gray-400 hover:text-gray-600 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                    </button>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="relative">
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password" required
                           class="mt-1 appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           placeholder="Confirm your password">
                    <button type="button" onclick="togglePassword('password_confirmation')"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center mt-6">
                        <svg id="password_confirmation-eye-open" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg id="password_confirmation-eye-closed" class="h-5 w-5 text-gray-400 hover:text-gray-600 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                    </button>
                </div>

                <?php if(!$referralCode): ?>
                <div>
                    <label for="referral_code_input" class="block text-sm font-medium text-gray-700">Referral Code (Optional)</label>
                    <input id="referral_code_input" name="referral_code" type="text" 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php $__errorArgs = ['referral_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           placeholder="Enter referral code if you have one" value="<?php echo e(old('referral_code')); ?>">
                    <?php $__errorArgs = ['referral_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <?php endif; ?>
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Create Account
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function togglePassword(inputId) {
    const passwordInput = document.getElementById(inputId);
    const eyeOpen = document.getElementById(inputId + '-eye-open');
    const eyeClosed = document.getElementById(inputId + '-eye-closed');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeOpen.classList.add('hidden');
        eyeClosed.classList.remove('hidden');
    } else {
        passwordInput.type = 'password';
        eyeOpen.classList.remove('hidden');
        eyeClosed.classList.add('hidden');
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/auth/register.blade.php ENDPATH**/ ?>