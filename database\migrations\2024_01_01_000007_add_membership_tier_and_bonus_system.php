<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('membership_tier', ['earthfriendly', 'light'])->default('earthfriendly')->after('referred_by');
            $table->timestamp('light_member_activated_at')->nullable()->after('activated_at');
        });

        // Create bonus payments table
        Schema::create('bonus_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('bonus_type', ['milestone', 'time_based', 'leaderboard', 'special']);
            $table->string('bonus_name'); // e.g., "10 Referrals Milestone", "First 30 Days", "Monthly Top 3"
            $table->decimal('amount', 10, 2);
            $table->text('description')->nullable();
            $table->json('criteria')->nullable(); // Store bonus criteria
            $table->enum('status', ['pending', 'paid', 'cancelled'])->default('pending');
            $table->timestamp('earned_at');
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'bonus_type']);
            $table->index(['status', 'earned_at']);
        });

        // Create milestone tracking table
        Schema::create('milestone_tracking', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('milestone_count'); // 10, 25, 50, etc.
            $table->decimal('bonus_amount', 10, 2);
            $table->boolean('achieved')->default(false);
            $table->timestamp('achieved_at')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'milestone_count']);
        });

        // Create leaderboard tracking table
        Schema::create('leaderboard_periods', function (Blueprint $table) {
            $table->id();
            $table->string('period_type'); // 'monthly', 'weekly', 'quarterly'
            $table->date('period_start');
            $table->date('period_end');
            $table->enum('status', ['active', 'completed', 'paid'])->default('active');
            $table->json('winners')->nullable(); // Store top performers
            $table->timestamps();

            $table->index(['period_type', 'period_start']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['membership_tier', 'light_member_activated_at']);
        });

        Schema::dropIfExists('bonus_payments');
        Schema::dropIfExists('milestone_tracking');
        Schema::dropIfExists('leaderboard_periods');
    }
};
