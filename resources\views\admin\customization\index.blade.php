@extends('layouts.admin')

@section('title', 'Platform Customization')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ route('admin.dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"></path>
                    </svg>
                    Admin Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ route('admin.settings.index') }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2">Settings</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Platform Customization</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Platform Customization</h1>
                <p class="text-gray-600 mt-1">Customize your platform's appearance, content, and functionality</p>
            </div>
            <div class="flex space-x-3">
                <button class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                    </svg>
                    Reset to Defaults
                </button>
                <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    Save All Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Customization Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'branding' }">
                <button @click="activeTab = 'branding'" :class="activeTab === 'branding' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Company Branding
                </button>
                <button @click="activeTab = 'pricing'" :class="activeTab === 'pricing' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Stage Pricing Plans
                </button>
                <button @click="activeTab = 'widgets'" :class="activeTab === 'widgets' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Widgets & Shortcodes
                </button>
                <button @click="activeTab = 'appearance'" :class="activeTab === 'appearance' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Appearance
                </button>
                <button @click="activeTab = 'footer'" :class="activeTab === 'footer' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Footer & Pages
                </button>
                <button @click="activeTab = 'system'" :class="activeTab === 'system' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    System Settings
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'branding' }">
        <!-- Company Branding Tab -->
        <div x-show="activeTab === 'branding'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Company Information</h3>
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                            <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Environmental Community Platform" placeholder="Enter company name">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                            <input type="email" class="w-full border border-gray-300 rounded-md px-3 py-2" value="<EMAIL>" placeholder="Enter contact email">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Company Description</label>
                        <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter company description">Join our environmental community and make a positive impact on the planet through stage-based activations and collaborative projects.</textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Logo</label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500">
                                            <span>Upload a file</span>
                                            <input type="file" class="sr-only">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Favicon</label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500">
                                            <span>Upload favicon</span>
                                            <input type="file" class="sr-only">
                                        </label>
                                    </div>
                                    <p class="text-xs text-gray-500">ICO, PNG 16x16 or 32x32</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">Save Branding</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Stage Pricing Plans Tab -->
        <div x-show="activeTab === 'pricing'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Stage Pricing Plans</h3>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        Add New Stage
                    </button>
                </div>

                <!-- Stage Plans Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Earth-Friendly Member (Free) -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Earth-Friendly Member</h4>
                            <span class="text-sm text-green-600 bg-green-100 px-2 py-1 rounded">FREE</span>
                        </div>
                        <form class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                                <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2" value="0.00" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Experience Requirement</label>
                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="0-1 years" placeholder="e.g., 0-1 years">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Benefits</label>
                                <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter benefits (one per line)">Basic community access
Environmental tips and resources
Monthly newsletter
Basic project participation</textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Button Text</label>
                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Join Free" placeholder="Button text">
                            </div>
                            <button type="submit" class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Update Stage</button>
                        </form>
                    </div>

                    <!-- Light Member -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Light Member</h4>
                            <span class="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded">$29</span>
                        </div>
                        <form class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                                <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2" value="29.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Experience Requirement</label>
                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="1-2 years" placeholder="e.g., 1-2 years">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Benefits</label>
                                <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter benefits (one per line)">All Earth-Friendly benefits
Advanced project access
Referral earning opportunities
Priority support
Monthly webinars</textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Button Text</label>
                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Activate Light Member" placeholder="Button text">
                            </div>
                            <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">Update Stage</button>
                        </form>
                    </div>

                    <!-- Green Leader -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Green Leader</h4>
                            <span class="text-sm text-green-600 bg-green-100 px-2 py-1 rounded">$99</span>
                        </div>
                        <form class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                                <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2" value="99.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Experience Requirement</label>
                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="2-4 years" placeholder="e.g., 2-4 years">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Benefits</label>
                                <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter benefits (one per line)">All Light Member benefits
Leadership opportunities
Project creation access
Higher referral commissions
Exclusive events access</textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Button Text</label>
                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Become Green Leader" placeholder="Button text">
                            </div>
                            <button type="submit" class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Update Stage</button>
                        </form>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Pricing Plan Notes</h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• Experience requirements replace referral counts (e.g., "0-1 years", "2-4 years", "5-10 years")</li>
                        <li>• Pricing amounts are displayed instead of bonus amounts</li>
                        <li>• Benefits are displayed as bullet points on the frontend</li>
                        <li>• Button text can be customized for each stage</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Widgets & Shortcodes Tab -->
        <div x-show="activeTab === 'widgets'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Widgets & Shortcodes</h3>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        Create Widget
                    </button>
                </div>

                <!-- Available Widgets -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Community Projects Widget -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Community Projects</h4>
                            <span class="text-sm text-green-600 bg-green-100 px-2 py-1 rounded">Active</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Display community projects with filtering options</p>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Project Type</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="all">All Projects</option>
                                    <option value="crowdfund">Crowdfunding</option>
                                    <option value="volunteer">Volunteer</option>
                                    <option value="petition">Petition</option>
                                    <option value="donate">Donation</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Display Type</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="latest">Latest</option>
                                    <option value="featured">Featured</option>
                                    <option value="top_performing">Top Performing</option>
                                    <option value="most_anticipated">Most Anticipated</option>
                                </select>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                                <label class="ml-2 block text-sm text-gray-700">Show Leaderboard</label>
                            </div>
                        </div>

                        <div class="mt-4 p-3 bg-gray-50 rounded">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Shortcode</label>
                            <code class="text-sm text-gray-800">[community_projects type="all" display="latest" leaderboard="1" limit="6"]</code>
                        </div>

                        <button class="mt-4 w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700">Update Widget</button>
                    </div>

                    <!-- Stage Pricing Widget -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-medium text-gray-900">Stage Pricing Plans</h4>
                            <span class="text-sm text-green-600 bg-green-100 px-2 py-1 rounded">Active</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Display stage activation pricing plans</p>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Layout Style</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="grid">Grid Layout</option>
                                    <option value="list">List Layout</option>
                                    <option value="carousel">Carousel</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Stages to Show</label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="all">All Stages</option>
                                    <option value="paid">Paid Stages Only</option>
                                    <option value="featured">Featured Stages</option>
                                </select>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                                <label class="ml-2 block text-sm text-gray-700">Show Benefits</label>
                            </div>
                        </div>

                        <div class="mt-4 p-3 bg-gray-50 rounded">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Shortcode</label>
                            <code class="text-sm text-gray-800">[stage_pricing layout="grid" stages="all" benefits="1"]</code>
                        </div>

                        <button class="mt-4 w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700">Update Widget</button>
                    </div>
                </div>

                <!-- Shortcode Usage Guide -->
                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 class="text-sm font-medium text-yellow-900 mb-2">How to Use Shortcodes</h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• Copy the shortcode from any widget above</li>
                        <li>• Paste it into any page content, post, or custom field</li>
                        <li>• The widget will automatically render on the frontend</li>
                        <li>• Modify parameters to customize the display</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Appearance Tab -->
        <div x-show="activeTab === 'appearance'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Appearance Settings</h3>
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
                            <div class="flex items-center space-x-3">
                                <input type="color" class="h-10 w-16 border border-gray-300 rounded" value="#4F46E5">
                                <input type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2" value="#4F46E5">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Secondary Color</label>
                            <div class="flex items-center space-x-3">
                                <input type="color" class="h-10 w-16 border border-gray-300 rounded" value="#10B981">
                                <input type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2" value="#10B981">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Accent Color</label>
                            <div class="flex items-center space-x-3">
                                <input type="color" class="h-10 w-16 border border-gray-300 rounded" value="#F59E0B">
                                <input type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2" value="#F59E0B">
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Custom CSS</label>
                        <textarea rows="8" class="w-full border border-gray-300 rounded-md px-3 py-2 font-mono text-sm" placeholder="Enter custom CSS rules...">/* Custom CSS */
.custom-button {
    background: linear-gradient(45deg, #4F46E5, #10B981);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}</textarea>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">Save Appearance</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Footer & Pages Tab -->
        <div x-show="activeTab === 'footer'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Footer Settings</h3>
                <form class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Footer Text</label>
                        <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Enter footer description">Join our environmental community and make a positive impact on the planet through collaborative action and sustainable living.</textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Copyright Text</label>
                        <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" value="© 2024 Environmental Community Platform. All rights reserved." placeholder="Copyright text">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Organization Links</label>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <input type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2" value="About Us" placeholder="Link text">
                                <input type="url" class="flex-1 border border-gray-300 rounded-md px-3 py-2" value="/about" placeholder="Link URL">
                                <button type="button" class="text-red-600 hover:text-red-800">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2" value="Our Mission" placeholder="Link text">
                                <input type="url" class="flex-1 border border-gray-300 rounded-md px-3 py-2" value="/mission" placeholder="Link URL">
                                <button type="button" class="text-red-600 hover:text-red-800">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <button type="button" class="mt-3 text-indigo-600 hover:text-indigo-800 text-sm">+ Add Link</button>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">Save Footer</button>
                    </div>
                </form>
            </div>

            <!-- Footer Pages Management -->
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Footer Pages</h3>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        Add Page
                    </button>
                </div>

                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Privacy Policy</h4>
                                <p class="text-sm text-gray-500">/privacy-policy</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-green-600 bg-green-100 px-2 py-1 rounded">Active</span>
                                <button class="text-indigo-600 hover:text-indigo-800 text-sm">Edit</button>
                                <button class="text-red-600 hover:text-red-800 text-sm">Delete</button>
                            </div>
                        </div>
                    </div>
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Terms of Service</h4>
                                <p class="text-sm text-gray-500">/terms-of-service</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-green-600 bg-green-100 px-2 py-1 rounded">Active</span>
                                <button class="text-indigo-600 hover:text-indigo-800 text-sm">Edit</button>
                                <button class="text-red-600 hover:text-red-800 text-sm">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Settings Tab -->
        <div x-show="activeTab === 'system'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">System Settings</h3>
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Default Commission Rate (%)</label>
                            <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2" value="10.00" placeholder="10.00">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Referral Bonus Amount</label>
                            <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2" value="25.00" placeholder="25.00">
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="text-base font-medium text-gray-900">Platform Features</h4>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                                <label class="ml-3 block text-sm text-gray-700">Enable user registration</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                                <label class="ml-3 block text-sm text-gray-700">Enable referral system</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                                <label class="ml-3 block text-sm text-gray-700">Enable stage activations</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label class="ml-3 block text-sm text-gray-700">Enable community projects</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                                <label class="ml-3 block text-sm text-gray-700">Enable messaging system</label>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">Save Settings</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality is handled by Alpine.js
    console.log('Platform Customization page loaded');
});
</script>
@endsection
