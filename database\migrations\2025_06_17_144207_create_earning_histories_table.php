<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('earning_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type'); // referral, commission, bonus, project_completion, marketplace_sale, etc.
            $table->decimal('amount', 10, 2)->default(0);
            $table->decimal('points', 10, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->text('description');
            $table->string('reference_type')->nullable(); // Model class name
            $table->unsignedBigInteger('reference_id')->nullable(); // Model ID
            $table->enum('status', ['pending', 'approved', 'paid', 'cancelled'])->default('pending');
            $table->boolean('auto_pay')->default(false);
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'type']);
            $table->index(['user_id', 'status']);
            $table->index(['reference_type', 'reference_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('earning_histories');
    }
};
