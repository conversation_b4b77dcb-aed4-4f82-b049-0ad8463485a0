<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Withdrawal extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'amount',
        'status',
        'payment_method',
        'payment_details',
        'admin_notes',
        'processed_at',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'payment_details' => 'array',
            'processed_at' => 'datetime',
        ];
    }

    /**
     * Get the user who requested this withdrawal.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get pending withdrawals.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get processing withdrawals.
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    /**
     * Scope to get completed withdrawals.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Mark withdrawal as processing.
     */
    public function markAsProcessing(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->status = 'processing';
        $this->save();

        return true;
    }

    /**
     * Mark withdrawal as completed.
     */
    public function markAsCompleted(string $adminNotes = null): bool
    {
        if (!in_array($this->status, ['pending', 'processing'])) {
            return false;
        }

        $this->status = 'completed';
        $this->processed_at = now();
        if ($adminNotes) {
            $this->admin_notes = $adminNotes;
        }
        $this->save();

        return true;
    }

    /**
     * Reject withdrawal and refund balance.
     */
    public function reject(string $reason = null): bool
    {
        if (!in_array($this->status, ['pending', 'processing'])) {
            return false;
        }

        $this->status = 'rejected';
        $this->processed_at = now();
        if ($reason) {
            $this->admin_notes = $reason;
        }
        $this->save();

        // Refund the amount to user's available balance
        $user = $this->user;
        $user->available_balance += $this->amount;
        $user->save();

        return true;
    }
}
