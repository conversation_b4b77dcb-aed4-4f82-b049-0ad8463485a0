@extends('layouts.admin')

@section('title', 'Helpers Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Helpers Management System</h1>
                <p class="text-gray-600 mt-1">Manage support staff, help desk features, and customer service tools</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportHelperData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Data
                </button>
                <button onclick="addNewHelper()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    Add New Helper
                </button>
            </div>
        </div>
    </div>

    <!-- Helper Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">24</h3>
                    <p class="text-sm text-gray-600">Active Helpers</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">156</h3>
                    <p class="text-sm text-gray-600">Open Tickets</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">2.3h</h3>
                    <p class="text-sm text-gray-600">Avg Response Time</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">94.5%</h3>
                    <p class="text-sm text-gray-600">Satisfaction Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'helpers' }">
                <button @click="activeTab = 'helpers'" :class="activeTab === 'helpers' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Helper Management
                </button>
                <button @click="activeTab = 'tickets'" :class="activeTab === 'tickets' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Support Tickets
                </button>
                <button @click="activeTab = 'knowledge'" :class="activeTab === 'knowledge' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Knowledge Base
                </button>
                <button @click="activeTab = 'settings'" :class="activeTab === 'settings' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Help Desk Settings
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Analytics & Reports
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'helpers' }">
        <!-- Helper Management Tab -->
        <div x-show="activeTab === 'helpers'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Support Staff Management</h3>
                    <div class="flex items-center space-x-3">
                        <input type="text" placeholder="Search helpers..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>All Roles</option>
                            <option>Support Agent</option>
                            <option>Senior Agent</option>
                            <option>Team Lead</option>
                            <option>Manager</option>
                        </select>
                    </div>
                </div>
                    </div>
                    <button type="submit" class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg">
                        Search
                    </button>
                </form>
            </div>

            <!-- Helpers Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Active</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($helpers as $helper)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 flex-shrink-0">
                                            <img class="h-10 w-10 rounded-full" src="{{ $helper->avatar_url }}" alt="">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $helper->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $helper->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $helper->role }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {{ $helper->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ ucfirst($helper->status) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $helper->last_active_at ? $helper->last_active_at->diffForHumans() : 'Never' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('admin.helpers.edit', $helper) }}" 
                                           class="text-blue-600 hover:text-blue-900">Edit</a>
                                        <form action="{{ route('admin.helpers.destroy', $helper) }}" 
                                              method="POST" 
                                              onsubmit="return confirm('Are you sure you want to remove this helper?');"
                                              class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                    No helpers found.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            </div>
        </div>

        <!-- Support Tickets Tab -->
        <div x-show="activeTab === 'tickets'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Support Ticket Management</h3>
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>All Priorities</option>
                            <option>High</option>
                            <option>Medium</option>
                            <option>Low</option>
                        </select>
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>All Status</option>
                            <option>Open</option>
                            <option>In Progress</option>
                            <option>Resolved</option>
                            <option>Closed</option>
                        </select>
                        <input type="text" placeholder="Search tickets..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                    <button onclick="createTicket()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Create Ticket
                    </button>
                </div>

                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <span class="text-sm font-medium text-gray-900">#TICKET-001</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">High Priority</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">In Progress</span>
                            </div>
                            <span class="text-sm text-gray-500">2 hours ago</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-900 mb-1">Login Issues with Two-Factor Authentication</h4>
                        <p class="text-sm text-gray-600 mb-2">User unable to complete 2FA verification process...</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">Assigned to:</span>
                                <span class="text-xs font-medium text-gray-900">John Doe</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="viewTicket('TICKET-001')" class="text-xs text-blue-600 hover:text-blue-900">View</button>
                                <button onclick="assignTicket('TICKET-001')" class="text-xs text-green-600 hover:text-green-900">Reassign</button>
                                <button onclick="closeTicket('TICKET-001')" class="text-xs text-red-600 hover:text-red-900">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Knowledge Base Tab -->
        <div x-show="activeTab === 'knowledge'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Knowledge Base Management</h3>
                    <button onclick="createArticle()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Create Article
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">Getting Started</h4>
                        <p class="text-sm text-gray-600 mb-3">Basic guides for new users</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">12 articles</span>
                            <button onclick="manageCategory('getting-started')" class="text-xs text-blue-600 hover:text-blue-900">Manage</button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">Account Management</h4>
                        <p class="text-sm text-gray-600 mb-3">User account and profile help</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">8 articles</span>
                            <button onclick="manageCategory('account-management')" class="text-xs text-blue-600 hover:text-blue-900">Manage</button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">Payment & Billing</h4>
                        <p class="text-sm text-gray-600 mb-3">Payment processing and billing</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">15 articles</span>
                            <button onclick="manageCategory('payment-billing')" class="text-xs text-blue-600 hover:text-blue-900">Manage</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Desk Settings Tab -->
        <div x-show="activeTab === 'settings'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Help Desk Configuration</h3>
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Default Response Time (hours)</label>
                            <input type="number" value="24" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Auto-Assignment</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option>Round Robin</option>
                                <option>Least Busy</option>
                                <option>Manual Only</option>
                            </select>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Enable Email Notifications</h4>
                                <p class="text-sm text-gray-500">Send email alerts for new tickets</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Customer Satisfaction Surveys</h4>
                                <p class="text-sm text-gray-500">Send surveys after ticket resolution</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics & Reports Tab -->
        <div x-show="activeTab === 'analytics'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Performance Metrics</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Average Response Time</span>
                            <span class="text-sm font-medium text-gray-900">2.3 hours</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Resolution Rate</span>
                            <span class="text-sm font-medium text-green-600">87.5%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Customer Satisfaction</span>
                            <span class="text-sm font-medium text-green-600">94.5%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">First Contact Resolution</span>
                            <span class="text-sm font-medium text-gray-900">72.1%</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Ticket Statistics</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Open Tickets</span>
                            <span class="text-sm font-medium text-yellow-600">156</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">In Progress</span>
                            <span class="text-sm font-medium text-blue-600">89</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Resolved Today</span>
                            <span class="text-sm font-medium text-green-600">34</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Overdue Tickets</span>
                            <span class="text-sm font-medium text-red-600">12</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportHelperData() {
    alert('Export helper data functionality will be implemented');
}

function addNewHelper() {
    alert('Add new helper functionality will be implemented');
}

function editHelper(email) {
    alert(`Edit helper ${email} functionality will be implemented`);
}

function deactivateHelper(email) {
    if (confirm(`Deactivate helper ${email}?`)) {
        alert(`Deactivate helper ${email} functionality will be implemented`);
    }
}

function createTicket() {
    alert('Create ticket functionality will be implemented');
}

function viewTicket(ticketId) {
    alert(`View ticket ${ticketId} functionality will be implemented`);
}

function assignTicket(ticketId) {
    alert(`Assign ticket ${ticketId} functionality will be implemented`);
}

function closeTicket(ticketId) {
    if (confirm(`Close ticket ${ticketId}?`)) {
        alert(`Close ticket ${ticketId} functionality will be implemented`);
    }
}

function createArticle() {
    alert('Create knowledge base article functionality will be implemented');
}

function manageCategory(category) {
    alert(`Manage ${category} category functionality will be implemented`);
}
</script>
@endsection
