<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Payment gateways configuration
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // 'stripe', 'paypal', 'paystack'
            $table->string('display_name');
            $table->boolean('is_active')->default(false);
            $table->boolean('is_sandbox')->default(true);
            $table->json('configuration'); // API keys, settings
            $table->json('supported_currencies')->nullable();
            $table->decimal('transaction_fee_percentage', 5, 2)->default(0);
            $table->decimal('transaction_fee_fixed', 10, 2)->default(0);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->unique('name');
        });

        // Payment transactions
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('payment_gateway_id')->constrained()->onDelete('cascade');
            $table->string('transaction_id')->unique();
            $table->string('gateway_transaction_id')->nullable();
            $table->string('type'); // 'activation', 'commission_withdrawal', 'deposit'
            $table->string('status'); // 'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'
            $table->decimal('amount', 15, 2);
            $table->string('currency', 3);
            $table->decimal('fee_amount', 10, 2)->default(0);
            $table->decimal('net_amount', 15, 2);
            $table->json('gateway_response')->nullable();
            $table->json('metadata')->nullable(); // Additional data
            $table->string('description')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->string('failure_reason')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['type', 'status']);
            $table->index(['gateway_transaction_id']);
        });

        // Payment methods (user saved payment methods)
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('payment_gateway_id')->constrained()->onDelete('cascade');
            $table->string('gateway_method_id'); // Gateway's payment method ID
            $table->string('type'); // 'card', 'bank_account', 'paypal', 'wallet'
            $table->string('last_four')->nullable();
            $table->string('brand')->nullable(); // 'visa', 'mastercard', etc.
            $table->string('country')->nullable();
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
        });

        // Webhook events
        Schema::create('payment_webhook_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payment_gateway_id')->constrained()->onDelete('cascade');
            $table->string('webhook_id'); // Gateway webhook ID
            $table->string('event_type');
            $table->json('payload');
            $table->boolean('processed')->default(false);
            $table->timestamp('processed_at')->nullable();
            $table->string('processing_error')->nullable();
            $table->timestamps();

            $table->index(['webhook_id', 'processed']);
            $table->index(['event_type', 'processed']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_webhook_events');
        Schema::dropIfExists('payment_methods');
        Schema::dropIfExists('payment_transactions');
        Schema::dropIfExists('payment_gateways');
    }
};
