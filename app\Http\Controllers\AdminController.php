<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Currency;
use App\Models\Language;
use App\Models\MembershipStage;
use App\Models\UserStageActivation;
use App\Models\Commission;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Admin dashboard.
     */
    public function dashboard()
    {
        // Get key statistics
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'light_members' => User::where('membership_tier', 'light')->count(),
            'total_activations' => UserStageActivation::count(),
            'total_commissions' => Commission::sum('amount'),
            'pending_commissions' => Commission::where('status', 'pending')->sum('amount'),
            'total_wallets' => Wallet::count(),
            'total_transactions' => WalletTransaction::count(),
            'active_currencies' => Currency::where('is_active', true)->count(),
            'active_languages' => Language::where('is_active', true)->count(),
        ];

        // Approval notifications for admins
        $approvalStats = [
            'pending_users' => User::where('account_status', 'pending')->count(),
            'pending_activations' => UserStageActivation::where('approval_status', 'pending')->count(),
            'pending_commissions' => Commission::where('approval_status', 'pending')->count(),
            'pending_commission_amount' => Commission::where('approval_status', 'pending')->sum('amount'),
            'pending_admin_requests' => User::where('admin_status', 'pending')->count(),
        ];

        // Recent activity
        $recentUsers = User::orderBy('created_at', 'desc')->take(5)->get();
        $recentActivations = UserStageActivation::with(['user', 'membershipStage'])
            ->orderBy('created_at', 'desc')->take(5)->get();
        $recentTransactions = WalletTransaction::with(['wallet.user', 'wallet.currency'])
            ->orderBy('created_at', 'desc')->take(5)->get();

        // Growth data for charts
        $userGrowth = $this->getUserGrowthData();
        $revenueGrowth = $this->getRevenueGrowthData();

        return view('admin.dashboard', compact(
            'stats',
            'approvalStats',
            'recentUsers',
            'recentActivations',
            'recentTransactions',
            'userGrowth',
            'revenueGrowth'
        ));
    }

    /**
     * User management.
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('referral_code', 'like', "%{$search}%");
            });
        }

        // Filter by membership tier
        if ($request->has('tier') && $request->tier) {
            $query->where('membership_tier', $request->tier);
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status);
        }

        $users = $query->with(['wallets.currency', 'stageActivations.membershipStage'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show user details.
     */
    public function userShow($id)
    {
        $user = User::with([
            'wallets.currency',
            'stageActivations.membershipStage',
            'commissions',
            'referrals',
            'referrer'
        ])->findOrFail($id);

        $userStats = [
            'total_earnings' => $user->total_earnings ?? 0,
            'total_referrals' => $user->referrals()->count(),
            'active_stages' => $user->stageActivations()->where('is_active', true)->count(),
            'total_transactions' => WalletTransaction::whereHas('wallet', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->count(),
        ];

        return view('admin.users.show', compact('user', 'userStats'));
    }

    /**
     * Currency management.
     */
    public function currencies()
    {
        $currencies = Currency::orderBy('code')->get();
        return view('admin.currencies.index', compact('currencies'));
    }

    /**
     * Update currency.
     */
    public function updateCurrency(Request $request, Currency $currency)
    {
        $request->validate([
            'exchange_rate' => 'required|numeric|min:0.000001',
            'is_active' => 'boolean',
        ]);

        $currency->update([
            'exchange_rate' => $request->exchange_rate,
            'is_active' => $request->has('is_active'),
        ]);

        return back()->with('success', 'Currency updated successfully');
    }

    /**
     * Language management.
     */
    public function languages()
    {
        $languages = Language::orderBy('name')->get();
        return view('admin.languages.index', compact('languages'));
    }

    /**
     * Update language.
     */
    public function updateLanguage(Request $request, Language $language)
    {
        $request->validate([
            'is_active' => 'boolean',
        ]);

        $language->update([
            'is_active' => $request->has('is_active'),
        ]);

        return back()->with('success', 'Language updated successfully');
    }

    /**
     * Stage management.
     */
    public function stages()
    {
        $stages = MembershipStage::orderBy('order')->get();
        $stageStats = [];

        foreach ($stages as $stage) {
            $stageStats[$stage->id] = [
                'total_activations' => UserStageActivation::where('membership_stage_id', $stage->id)->count(),
                'active_activations' => UserStageActivation::where('membership_stage_id', $stage->id)
                    ->where('is_active', true)->count(),
                'total_commissions' => 0, // Commission tracking for stages not yet implemented
            ];
        }

        return view('admin.stages.index', compact('stages', 'stageStats'));
    }

    /**
     * Update stage.
     */
    public function updateStage(Request $request, MembershipStage $stage)
    {
        // Only top admin can modify stage pricing
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can modify stage pricing and settings.');
        }

        $request->validate([
            'commission_amount' => 'sometimes|numeric|min:0',
            'activation_bonus' => 'sometimes|numeric|min:0',
            'activation_price' => 'sometimes|numeric|min:0',
            'monthly_fee' => 'sometimes|numeric|min:0',
            'is_active' => 'boolean',
            'requires_approval' => 'boolean',
        ]);

        $updateData = [];

        if ($request->has('commission_amount')) {
            $updateData['commission_rate'] = $request->commission_amount;
        }

        if ($request->has('activation_bonus')) {
            $updateData['activation_bonus'] = $request->activation_bonus;
        }

        if ($request->has('activation_price')) {
            $updateData['activation_price'] = $request->activation_price;
        }

        if ($request->has('monthly_fee')) {
            $updateData['monthly_fee'] = $request->monthly_fee;
        }

        if ($request->has('is_active')) {
            $updateData['is_active'] = $request->has('is_active');
        }

        if ($request->has('requires_approval')) {
            $updateData['requires_approval'] = $request->has('requires_approval');
        }

        $stage->update($updateData);

        return back()->with('success', 'Stage updated successfully');
    }

    /**
     * Financial overview.
     */
    public function finances()
    {
        $stats = [
            'total_revenue' => Commission::sum('amount'),
            'pending_commissions' => Commission::where('status', 'pending')->sum('amount'),
            'paid_commissions' => Commission::where('status', 'paid')->sum('amount'),
            'total_wallets' => Wallet::count(),
            'total_transactions' => WalletTransaction::count(),
            'total_wallet_balance' => Wallet::sum('balance'),
            'total_pending_balance' => Wallet::sum('pending_balance'),
            'total_frozen_balance' => Wallet::sum('frozen_balance'),
        ];

        // Currency breakdown
        $currencyBreakdownData = Wallet::select('currency_id', DB::raw('SUM(balance) as total_balance'))
            ->with('currency')
            ->groupBy('currency_id')
            ->get();

        $currencyBreakdown = [];
        foreach ($currencyBreakdownData as $item) {
            $currencyBreakdown[$item->currency->code] = $item->total_balance;
        }

        // Recent transactions
        $recentTransactions = WalletTransaction::with(['wallet.user', 'wallet.currency'])
            ->orderBy('created_at', 'desc')
            ->take(20)
            ->get();

        return view('admin.finances.index', compact('stats', 'currencyBreakdown', 'recentTransactions'));
    }

    /**
     * System settings.
     */
    public function settings()
    {
        $settingGroups = [];

        foreach (\App\Models\SiteSetting::GROUPS as $groupKey => $groupLabel) {
            $settings = \App\Models\SiteSetting::getByGroup($groupKey);
            if (!empty($settings)) {
                $settingGroups[$groupKey] = [
                    'label' => $groupLabel,
                    'settings' => $settings
                ];
            }
        }

        return view('admin.settings.index', compact('settingGroups'));
    }

    /**
     * Update site settings.
     */
    public function updateSettings(Request $request)
    {
        // Only top admin can modify system settings
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can modify system settings.');
        }

        $settings = $request->input('settings', []);

        foreach ($settings as $key => $value) {
            \App\Models\SiteSetting::set($key, $value);
        }

        return back()->with('success', 'Settings updated successfully!');
    }

    /**
     * Stage rewards management.
     */
    public function stageRewards()
    {
        $stages = MembershipStage::with(['stageRewards' => function($query) {
            $query->orderBy('sort_order');
        }])->orderBy('order')->get();

        return view('admin.stage-rewards.index', compact('stages'));
    }

    /**
     * Create stage reward.
     */
    public function createStageReward(Request $request)
    {
        // Only top admin can create stage rewards
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can create stage rewards.');
        }

        $request->validate([
            'membership_stage_id' => 'required|exists:membership_stages,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:instant_payment,bonus_payment',
            'requirement_type' => 'required|in:project_completion,achievements,activities,escrow_purchases,referrals,events,custom',
            'requirements' => 'required|array',
            'reward_amount' => 'nullable|numeric|min:0',
            'reward_points' => 'nullable|integer|min:0',
            'is_repeatable' => 'boolean',
            'max_completions' => 'nullable|integer|min:1',
        ]);

        \App\Models\StageReward::create([
            'membership_stage_id' => $request->membership_stage_id,
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'requirement_type' => $request->requirement_type,
            'requirements' => $request->requirements,
            'reward_amount' => $request->reward_amount,
            'reward_points' => $request->reward_points,
            'is_active' => true,
            'is_repeatable' => $request->has('is_repeatable'),
            'max_completions' => $request->max_completions,
            'sort_order' => \App\Models\StageReward::where('membership_stage_id', $request->membership_stage_id)->count() + 1,
        ]);

        return back()->with('success', 'Stage reward created successfully!');
    }

    /**
     * Update stage reward.
     */
    public function updateStageReward(Request $request, \App\Models\StageReward $reward)
    {
        // Only top admin can update stage rewards
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can update stage rewards.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:instant_payment,bonus_payment',
            'requirement_type' => 'required|in:project_completion,achievements,activities,escrow_purchases,referrals,events,custom',
            'requirements' => 'required|array',
            'reward_amount' => 'nullable|numeric|min:0',
            'reward_points' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_repeatable' => 'boolean',
            'max_completions' => 'nullable|integer|min:1',
        ]);

        $reward->update([
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'requirement_type' => $request->requirement_type,
            'requirements' => $request->requirements,
            'reward_amount' => $request->reward_amount,
            'reward_points' => $request->reward_points,
            'is_active' => $request->has('is_active'),
            'is_repeatable' => $request->has('is_repeatable'),
            'max_completions' => $request->max_completions,
        ]);

        return back()->with('success', 'Stage reward updated successfully!');
    }

    /**
     * Delete stage reward.
     */
    public function deleteStageReward(\App\Models\StageReward $reward)
    {
        // Only top admin can delete stage rewards
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can delete stage rewards.');
        }

        $reward->delete();

        return back()->with('success', 'Stage reward deleted successfully!');
    }

    /**
     * Get user growth data for charts.
     */
    private function getUserGrowthData()
    {
        // Get data for the last 30 days in a single query
        $startDate = Carbon::now()->subDays(29)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        $userCounts = User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date')
            ->toArray();

        $data = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dateKey = $date->format('Y-m-d');
            $data[] = [
                'date' => $date->format('M j'),
                'users' => $userCounts[$dateKey] ?? 0,
            ];
        }
        return $data;
    }

    /**
     * Get revenue growth data for charts.
     */
    private function getRevenueGrowthData()
    {
        // Get data for the last 30 days in a single query
        $startDate = Carbon::now()->subDays(29)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        $revenueSums = Commission::selectRaw('DATE(created_at) as date, SUM(amount) as total')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('total', 'date')
            ->toArray();

        $data = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dateKey = $date->format('Y-m-d');
            $data[] = [
                'date' => $date->format('M j'),
                'revenue' => $revenueSums[$dateKey] ?? 0,
            ];
        }
        return $data;
    }

    /**
     * Toggle user status.
     */
    public function toggleUserStatus(User $user)
    {
        // Check if user can be modified
        if (!$user->canBeModifiedBy(Auth::user())) {
            return back()->with('error', 'You cannot modify this user. Top Admin accounts are protected.');
        }

        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "User {$status} successfully");
    }

    /**
     * Make user admin.
     */
    public function makeAdmin(User $user)
    {
        // Only top admin can directly promote users to admin
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can directly promote users to admin.');
        }

        $user->update([
            'is_admin' => true,
            'admin_status' => 'approved',
            'admin_since' => now(),
            'approved_by' => Auth::id(),
            'approved_at' => now(),
        ]);

        return back()->with('success', 'User promoted to admin successfully');
    }

    /**
     * Remove admin privileges.
     */
    public function removeAdmin(User $user)
    {
        // Check if user can be modified
        if (!$user->canBeModifiedBy(Auth::user())) {
            return back()->with('error', 'You cannot remove admin privileges from this user. Top Admin accounts are protected.');
        }

        $user->update([
            'is_admin' => false,
            'admin_since' => null,
            'admin_status' => null,
        ]);

        return back()->with('success', 'Admin privileges removed successfully');
    }

    /**
     * Approve admin request.
     */
    public function approveAdmin(User $user)
    {
        // Only top admin can approve other admins
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can approve admin requests.');
        }

        $user->update([
            'is_admin' => true,
            'admin_status' => 'approved',
            'admin_since' => now(),
        ]);

        return back()->with('success', 'Admin request approved successfully');
    }

    /**
     * Reject admin request.
     */
    public function rejectAdmin(User $user)
    {
        // Only top admin can reject other admins
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can reject admin requests.');
        }

        $user->update([
            'admin_status' => 'rejected',
        ]);

        return back()->with('success', 'Admin request rejected');
    }

    /**
     * Bulk activate users.
     */
    public function bulkActivateUsers(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $count = User::whereIn('id', $request->user_ids)->update(['is_active' => true]);

        return back()->with('success', "{$count} user(s) activated successfully");
    }

    /**
     * Bulk deactivate users.
     */
    public function bulkDeactivateUsers(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        // Filter out users that cannot be modified
        $userIds = collect($request->user_ids)
            ->reject(function ($id) {
                $user = User::find($id);
                return $id == Auth::id() || !$user || !$user->canBeModifiedBy(Auth::user());
            });

        $count = User::whereIn('id', $userIds)->update(['is_active' => false]);

        $protectedCount = count($request->user_ids) - $userIds->count();
        $message = "{$count} user(s) deactivated successfully";

        if ($protectedCount > 0) {
            $message .= ". {$protectedCount} user(s) were protected and not modified.";
        }

        return back()->with('success', $message);
    }

    /**
     * Bulk make users admin.
     */
    public function bulkMakeAdmin(Request $request)
    {
        // Only top admin can bulk promote users
        if (!Auth::user()->is_top_admin) {
            return back()->with('error', 'Only the Top Administrator can bulk promote users.');
        }

        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $count = User::whereIn('id', $request->user_ids)
            ->where('is_admin', false)
            ->update([
                'is_admin' => true,
                'admin_status' => 'approved',
                'admin_since' => now(),
            ]);

        return back()->with('success', "{$count} user(s) promoted to admin successfully");
    }

    /**
     * Export users to CSV.
     */
    public function exportUsers()
    {
        $users = User::with(['wallets', 'stageActivations.membershipStage'])
            ->orderBy('created_at', 'desc')
            ->get();

        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Name',
                'Email',
                'Membership Tier',
                'Admin Status',
                'Total Earnings',
                'Total Referrals',
                'Active Stages',
                'Is Active',
                'Joined Date',
                'Last Updated'
            ]);

            // CSV data
            foreach ($users as $user) {
                $adminStatus = 'Regular User';
                if ($user->is_top_admin) {
                    $adminStatus = 'Top Admin';
                } elseif ($user->is_admin) {
                    $adminStatus = 'Admin';
                } elseif ($user->admin_status === 'pending') {
                    $adminStatus = 'Pending Admin';
                } elseif ($user->admin_status === 'rejected') {
                    $adminStatus = 'Rejected Admin';
                }

                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    ucfirst($user->membership_tier),
                    $adminStatus,
                    '$' . number_format($user->total_earnings ?? 0, 2),
                    $user->total_referrals ?? 0,
                    $user->stageActivations->where('is_active', true)->count(),
                    $user->is_active ? 'Yes' : 'No',
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->updated_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Show admin permissions management page.
     */
    public function permissions()
    {
        if (!Auth::user()->is_top_admin) {
            abort(403, 'Only the Top Administrator can manage admin permissions.');
        }

        $adminUsers = User::where('is_admin', true)
            ->orderBy('is_top_admin', 'desc')
            ->orderBy('name')
            ->get();

        return view('admin.permissions.index', compact('adminUsers'));
    }

    /**
     * Update admin permissions.
     */
    public function updatePermissions(Request $request, User $user)
    {
        if (!Auth::user()->is_top_admin) {
            abort(403, 'Only the Top Administrator can manage admin permissions.');
        }

        if ($user->is_top_admin) {
            return back()->with('error', 'Cannot modify Top Admin permissions.');
        }

        if (!$user->is_admin) {
            return back()->with('error', 'User is not an admin.');
        }

        $permissions = [];
        $availablePermissions = [
            'user_management',
            'currency_management',
            'language_management',
            'financial_overview',
            'approval_management',
            'bulk_operations',
            'export_data'
        ];

        foreach ($availablePermissions as $permission) {
            $permissions[$permission] = $request->has("permissions.{$permission}");
        }

        // Top Admin exclusive permissions are always false for regular admins
        $permissions['stage_pricing'] = false;
        $permissions['admin_approval'] = false;
        $permissions['system_settings'] = false;

        $user->setAdminPermissions($permissions);

        return back()->with('success', "Permissions updated for {$user->name}");
    }

    /**
     * Show payment gateways management page.
     */
    public function payments()
    {
        $gateways = \App\Models\PaymentGateway::orderBy('sort_order')->get();

        // Get payment statistics
        $stats = [
            'total_transactions' => 0,
            'total_volume' => 0,
            'success_rate' => 0,
        ];

        return view('admin.payments.index', compact('gateways', 'stats'));
    }

    /**
     * Initialize default payment gateways.
     */
    public function initializePayments()
    {
        if (!Auth::user()->is_top_admin) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        try {
            \App\Models\PaymentGateway::createDefaults();
            return response()->json(['success' => true, 'message' => 'Payment gateways initialized successfully.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to initialize payment gateways.'], 500);
        }
    }

    /**
     * Toggle payment gateway status.
     */
    public function togglePaymentGateway(\App\Models\PaymentGateway $gateway)
    {
        if (!Auth::user()->is_admin) {
            abort(403, 'Access denied.');
        }

        $gateway->is_active = !$gateway->is_active;
        $gateway->save();

        $status = $gateway->is_active ? 'enabled' : 'disabled';
        return back()->with('success', "{$gateway->display_name} has been {$status}.");
    }

    /**
     * Configure payment gateway.
     */
    public function configurePaymentGateway(Request $request, \App\Models\PaymentGateway $gateway)
    {
        if (!Auth::user()->is_admin) {
            abort(403, 'Access denied.');
        }

        $configuration = $gateway->configuration ?? [];

        // Update configuration based on gateway type
        switch ($gateway->name) {
            case 'stripe':
                $configuration['public_key'] = $request->input('public_key');
                $configuration['secret_key'] = $request->input('secret_key');
                $configuration['webhook_secret'] = $request->input('webhook_secret');
                break;

            case 'paypal':
                $configuration['client_id'] = $request->input('client_id');
                $configuration['client_secret'] = $request->input('client_secret');
                $configuration['webhook_id'] = $request->input('webhook_id');
                break;

            case 'paystack':
                $configuration['public_key'] = $request->input('public_key');
                $configuration['secret_key'] = $request->input('secret_key');
                break;
        }

        $gateway->configuration = $configuration;
        $gateway->save();

        return back()->with('success', "{$gateway->display_name} configuration updated successfully.");
    }
}
