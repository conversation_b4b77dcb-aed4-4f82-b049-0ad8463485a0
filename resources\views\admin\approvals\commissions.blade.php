@extends('layouts.admin')

@section('title', 'Commission Approvals')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Commission Approvals</h1>
                <p class="mt-2 text-gray-600">Review and approve commission payments</p>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-500">{{ $pendingCommissions->count() }} pending approvals</span>
                <span class="text-sm font-medium text-green-600">${{ number_format($totalPendingAmount, 2) }} total pending</span>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    @if($pendingCommissions->count() > 0)
    <div class="bg-white shadow rounded-lg mb-6 p-4" x-data="{ selectedCommissions: [], showBulkActions: false }" x-init="$watch('selectedCommissions', value => showBulkActions = value.length > 0)">
        <div x-show="showBulkActions" x-transition class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600" x-text="`${selectedCommissions.length} commission(s) selected`"></span>
                <button type="button" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700"
                        onclick="bulkApprove()">
                    Approve Selected
                </button>
                <button type="button" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700"
                        onclick="bulkReject()">
                    Reject Selected
                </button>
            </div>
            <button type="button" @click="selectedCommissions = []; showBulkActions = false" class="text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    </div>
    @endif

    <!-- Pending Commissions -->
    @if($pendingCommissions->count() > 0)
    <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Pending Commission Payments</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                   @change="$event.target.checked ? selectedCommissions = Array.from(document.querySelectorAll('input[name=commission_ids]')).map(cb => cb.value) : selectedCommissions = []">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($pendingCommissions as $commission)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" name="commission_ids" value="{{ $commission->id }}" 
                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                   x-model="selectedCommissions">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-10 w-10 flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">{{ substr($commission->user->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ $commission->user->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $commission->user->email }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ ucfirst($commission->stage) }}</div>
                            <div class="text-sm text-gray-500">{{ $commission->description }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-green-600">${{ number_format($commission->amount, 2) }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $commission->created_at->format('M j, Y') }}</div>
                            <div class="text-sm text-gray-500">{{ $commission->created_at->diffForHumans() }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <form method="POST" action="{{ url('/admin/approvals/commissions/' . $commission->id . '/approve') }}" class="inline">
                                    @csrf
                                    <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-green-600 hover:bg-green-700">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Approve
                                    </button>
                                </form>
                                <button type="button" onclick="showRejectModal({{ $commission->id }}, '{{ $commission->user->name }}', '{{ number_format($commission->amount, 2) }}')" 
                                        class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-red-600 hover:bg-red-700">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                    Reject
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @else
    <div class="bg-white shadow rounded-lg p-6 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No pending commission approvals</h3>
        <p class="mt-1 text-sm text-gray-500">All commission payments have been reviewed.</p>
    </div>
    @endif

    <!-- Recently Processed -->
    @if($recentlyProcessed->count() > 0)
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recently Processed Commissions</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Processed By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($recentlyProcessed as $commission)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $commission->user->name }}</div>
                            <div class="text-sm text-gray-500">{{ $commission->user->email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-green-600">${{ number_format($commission->amount, 2) }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($commission->approval_status === 'approved')
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Approved
                            </span>
                            @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Rejected
                            </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($commission->approvedBy)
                            <div class="text-sm text-gray-900">{{ $commission->approvedBy->name }}</div>
                            @else
                            <span class="text-sm text-gray-500">System</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $commission->approved_at?->format('M j, Y H:i') }}</div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif
</div>

<!-- Reject Modal -->
<div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Reject Commission</h3>
            <form id="rejectForm" method="POST">
                @csrf
                <div class="mt-4">
                    <label for="rejection_reason" class="block text-sm font-medium text-gray-700 text-left">Reason for rejection:</label>
                    <textarea id="rejection_reason" name="approval_notes" rows="3" 
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"
                              placeholder="Please provide a reason for rejection..."></textarea>
                </div>
                <div class="flex items-center justify-between mt-6">
                    <button type="button" onclick="hideRejectModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                        Reject Commission
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRejectModal(commissionId, userName, amount) {
    document.getElementById('modalTitle').textContent = `Reject $${amount} Commission for ${userName}`;
    document.getElementById('rejectForm').action = `/admin/approvals/commissions/${commissionId}/reject`;
    document.getElementById('rejectModal').classList.remove('hidden');
}

function hideRejectModal() {
    document.getElementById('rejectModal').classList.add('hidden');
    document.getElementById('rejection_reason').value = '';
}

function bulkApprove() {
    const selectedCommissions = Array.from(document.querySelectorAll('input[name=commission_ids]:checked')).map(cb => cb.value);
    
    if (selectedCommissions.length === 0) {
        alert('Please select at least one commission.');
        return;
    }

    if (confirm(`Approve ${selectedCommissions.length} selected commission(s)?`)) {
        bulkAction('approve', selectedCommissions);
    }
}

function bulkReject() {
    const selectedCommissions = Array.from(document.querySelectorAll('input[name=commission_ids]:checked')).map(cb => cb.value);
    
    if (selectedCommissions.length === 0) {
        alert('Please select at least one commission.');
        return;
    }

    const reason = prompt(`Reason for rejecting ${selectedCommissions.length} commission(s):`);
    if (reason !== null) {
        bulkAction('reject', selectedCommissions, reason);
    }
}

function bulkAction(action, commissionIds, reason = '') {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/approvals/commissions/bulk-${action}`;
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_token';
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);

    // Add commission IDs
    commissionIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'commission_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    // Add reason if provided
    if (reason) {
        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'approval_notes';
        reasonInput.value = reason;
        form.appendChild(reasonInput);
    }

    document.body.appendChild(form);
    form.submit();
}
</script>
@endsection
