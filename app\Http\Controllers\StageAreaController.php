<?php

namespace App\Http\Controllers;

use App\Models\MembershipStage;
use App\Models\UserStageActivation;
use App\Services\StageAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StageAreaController extends Controller
{
    /**
     * Show the stage area dashboard.
     */
    public function index(string $stageSlug, StageAnalyticsService $analyticsService)
    {
        $user = Auth::user();

        // Get the stage
        $stage = MembershipStage::where('slug', $stageSlug)->firstOrFail();

        // Check if user has activated this stage
        if (!$user->hasActivatedStage($stageSlug)) {
            return redirect()->to('/activations')
                ->with('error', 'You need to activate the ' . $stage->name . ' stage to access this area.');
        }

        // Get user's activation for this stage
        $activation = $user->getStageActivation($stageSlug);

        // Get comprehensive analytics
        $analytics = $analyticsService->getStageAnalytics($user, $stageSlug);

        // Get other members in this stage
        $stageMembers = $this->getStageMembers($stage, $user);

        return view('stages.area', compact(
            'user',
            'stage',
            'activation',
            'analytics',
            'stageMembers'
        ));
    }

    /**
     * Get stage-specific statistics.
     */
    private function getStageStats($user, $stage, $activation)
    {
        $commissions = $activation->commissions();
        
        return [
            'total_earned' => $commissions->sum('amount') + $activation->activation_bonus_paid,
            'commission_earned' => $commissions->sum('amount'),
            'activation_bonus' => $activation->activation_bonus_paid,
            'referrals_count' => $commissions->count(),
            'activated_at' => $activation->activated_at,
            'days_active' => $activation->activated_at->diffInDays(now()),
            'average_daily_earnings' => $activation->activated_at->diffInDays(now()) > 0 
                ? ($commissions->sum('amount') / $activation->activated_at->diffInDays(now()))
                : 0,
        ];
    }

    /**
     * Get stage-specific activities.
     */
    private function getStageActivities($user, $stage, $activation)
    {
        $activities = collect();

        // Add activation event
        $activities->push([
            'type' => 'activation',
            'title' => 'Stage Activated',
            'description' => "Activated {$stage->name} stage",
            'amount' => $activation->activation_bonus_paid,
            'date' => $activation->activated_at,
            'icon' => 'star',
            'color' => 'green'
        ]);

        // Add commission activities
        $commissions = $activation->commissions()->latest()->take(10)->get();
        foreach ($commissions as $commission) {
            $activities->push([
                'type' => 'commission',
                'title' => 'Commission Earned',
                'description' => $commission->description,
                'amount' => $commission->amount,
                'date' => $commission->created_at,
                'icon' => 'dollar-sign',
                'color' => 'blue'
            ]);
        }

        return $activities->sortByDesc('date')->take(20);
    }

    /**
     * Get other members in this stage.
     */
    private function getStageMembers($stage, $currentUser)
    {
        return UserStageActivation::where('membership_stage_id', $stage->id)
            ->where('is_active', true)
            ->where('user_id', '!=', $currentUser->id)
            ->with('user')
            ->latest()
            ->take(10)
            ->get();
    }

    /**
     * Show stage resources and content.
     */
    public function resources(string $stageSlug)
    {
        $user = Auth::user();
        $stage = MembershipStage::where('slug', $stageSlug)->firstOrFail();
        
        // Check if user has activated this stage
        if (!$user->hasActivatedStage($stageSlug)) {
            return redirect()->to('/activations')
                ->with('error', 'You need to activate the ' . $stage->name . ' stage to access these resources.');
        }

        // Get stage-specific resources
        $resources = $this->getStageResources($stage);

        return view('stages.resources', compact('user', 'stage', 'resources'));
    }

    /**
     * Get stage-specific resources.
     */
    private function getStageResources($stage)
    {
        // This would typically come from a database or CMS
        $resources = [
            'bronze' => [
                'guides' => [
                    'Getting Started with Sustainable Living',
                    'Basic Recycling Techniques',
                    'Energy Saving Tips for Beginners'
                ],
                'videos' => [
                    'Welcome to Bronze Stage',
                    'Your First Steps to Sustainability'
                ],
                'tools' => [
                    'Carbon Footprint Calculator',
                    'Recycling Guide'
                ]
            ],
            'silver' => [
                'guides' => [
                    'Intermediate Sustainable Practices',
                    'Water Conservation Strategies',
                    'Sustainable Transportation Options'
                ],
                'videos' => [
                    'Silver Stage Masterclass',
                    'Advanced Energy Efficiency'
                ],
                'tools' => [
                    'Water Usage Tracker',
                    'Transportation Planner'
                ]
            ],
            'gold' => [
                'guides' => [
                    'Advanced Sustainability Techniques',
                    'Renewable Energy Solutions',
                    'Sustainable Food Systems'
                ],
                'videos' => [
                    'Gold Stage Expert Training',
                    'Renewable Energy Installation'
                ],
                'tools' => [
                    'Energy Audit Tool',
                    'Sustainable Meal Planner'
                ]
            ],
            'platinum' => [
                'guides' => [
                    'Community Leadership in Sustainability',
                    'Green Business Practices',
                    'Environmental Impact Assessment'
                ],
                'videos' => [
                    'Platinum Leadership Series',
                    'Building Sustainable Communities'
                ],
                'tools' => [
                    'Community Impact Tracker',
                    'Green Business Calculator'
                ]
            ],
            'diamond' => [
                'guides' => [
                    'Expert-Level Sustainability Consulting',
                    'Climate Change Mitigation Strategies',
                    'Sustainable Technology Innovation'
                ],
                'videos' => [
                    'Diamond Expert Certification',
                    'Climate Solutions Masterclass'
                ],
                'tools' => [
                    'Climate Impact Analyzer',
                    'Innovation Lab Access'
                ]
            ],
            'elite' => [
                'guides' => [
                    'Global Sustainability Leadership',
                    'Policy Development for Sustainability',
                    'Research and Development in Green Tech'
                ],
                'videos' => [
                    'Elite Global Leadership Program',
                    'Policy Making for Sustainability'
                ],
                'tools' => [
                    'Global Impact Dashboard',
                    'Policy Development Kit',
                    'Research Collaboration Platform'
                ]
            ]
        ];

        return $resources[$stage->slug] ?? [];
    }

    /**
     * Show stage rewards.
     */
    public function rewards(string $stageSlug)
    {
        $user = Auth::user();
        $stage = MembershipStage::where('slug', $stageSlug)->firstOrFail();

        // Check if user has activated this stage
        if (!$user->hasActivatedStage($stageSlug)) {
            return redirect()->to('/activations')
                ->with('error', 'You need to activate the ' . $stage->name . ' stage to access these rewards.');
        }

        // Get stage rewards
        $stageRewards = \App\Models\StageReward::where('membership_stage_id', $stage->id)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Get user's progress for each reward
        $userProgress = [];
        foreach ($stageRewards as $reward) {
            $progress = \App\Models\UserStageRewardProgress::where('user_id', $user->id)
                ->where('stage_reward_id', $reward->id)
                ->first();

            if (!$progress) {
                // Create initial progress record
                $progress = \App\Models\UserStageRewardProgress::create([
                    'user_id' => $user->id,
                    'stage_reward_id' => $reward->id,
                    'progress_data' => [],
                    'current_progress' => 0,
                    'required_progress' => $reward->requirements['count'] ?? 1,
                ]);
            }

            $userProgress[$reward->id] = $progress;
        }

        // Get instant payment rewards
        $instantRewards = $stageRewards->where('type', 'instant_payment');

        // Get bonus payment rewards
        $bonusRewards = $stageRewards->where('type', 'bonus_payment');

        // Get user's total earnings from this stage
        $stageEarnings = [
            'total_earned' => $user->total_earnings ?? 0,
            'instant_payments' => \App\Models\EarningHistory::where('user_id', $user->id)
                ->where('type', 'instant_payment')
                ->sum('amount'),
            'bonus_payments' => \App\Models\EarningHistory::where('user_id', $user->id)
                ->where('type', 'bonus_payment')
                ->sum('amount'),
        ];

        return view('stages.rewards', compact(
            'user',
            'stage',
            'stageRewards',
            'userProgress',
            'instantRewards',
            'bonusRewards',
            'stageEarnings'
        ));
    }

    /**
     * Show stage community/forum.
     */
    public function community(string $stageSlug)
    {
        $user = Auth::user();
        $stage = MembershipStage::where('slug', $stageSlug)->firstOrFail();
        
        // Check if user has activated this stage
        if (!$user->hasActivatedStage($stageSlug)) {
            return redirect()->route('membership.index')
                ->with('error', 'You need to activate the ' . $stage->name . ' stage to access the community.');
        }

        // Get stage members
        $stageMembers = $this->getStageMembers($stage, $user);
        
        // Get community stats
        $communityStats = [
            'total_members' => UserStageActivation::where('membership_stage_id', $stage->id)
                ->where('is_active', true)
                ->count(),
            'new_members_this_month' => UserStageActivation::where('membership_stage_id', $stage->id)
                ->where('is_active', true)
                ->whereMonth('activated_at', now()->month)
                ->count(),
        ];

        return view('stages.community', compact('user', 'stage', 'stageMembers', 'communityStats'));
    }
}
