<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ProjectManagementController extends Controller
{
    /**
     * Display the project management dashboard
     */
    public function index()
    {
        // Sample data for the dashboard
        $stats = [
            'total_projects' => 28,
            'active_projects' => 15,
            'completed_projects' => 10,
            'overdue_tasks' => 7,
        ];

        $recentProjects = collect([
            (object)[
                'id' => 1,
                'name' => 'Website Redesign',
                'status' => 'in_progress',
                'progress' => 65,
                'team_size' => 5,
                'deadline' => '2024-02-15',
            ],
            (object)[
                'id' => 2,
                'name' => 'Mobile App Development',
                'status' => 'planning',
                'progress' => 25,
                'team_size' => 8,
                'deadline' => '2024-03-30',
            ],
        ]);

        $upcomingDeadlines = collect([
            (object)[
                'project' => 'API Integration',
                'task' => 'Database Migration',
                'deadline' => '2024-01-10',
                'assignee' => '<PERSON>',
            ],
            (object)[
                'project' => 'Security Audit',
                'task' => 'Vulnerability Assessment',
                'deadline' => '2024-01-12',
                'assignee' => '<PERSON>',
            ],
        ]);

        return view('admin.project-management.index', compact('stats', 'recentProjects', 'upcomingDeadlines'));
    }

    /**
     * Display projects list
     */
    public function projects()
    {
        return view('admin.project-management.projects');
    }

    /**
     * Display tasks management
     */
    public function tasks()
    {
        return view('admin.project-management.tasks');
    }

    /**
     * Display team management
     */
    public function teams()
    {
        return view('admin.project-management.teams');
    }

    /**
     * Display project reports
     */
    public function reports()
    {
        return view('admin.project-management.reports');
    }

    /**
     * Display project settings
     */
    public function settings()
    {
        return view('admin.project-management.settings');
    }
}
