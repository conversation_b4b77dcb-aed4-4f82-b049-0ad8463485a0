<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\EarningHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class WalletController extends Controller
{
    /**
     * Display the wallet dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get or create wallets for all active currencies
        $currencies = Currency::getActive();
        foreach ($currencies as $currency) {
            $user->getOrCreateWallet($currency->code);
        }
        
        // Get user's wallets with currencies
        $wallets = $user->activeWallets()->get();
        
        // Get recent transactions across all wallets
        $recentTransactions = WalletTransaction::whereIn('wallet_id', $wallets->pluck('id'))
            ->with(['wallet.currency'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();
        
        // Calculate total balance in USD
        $totalBalanceUsd = $user->getTotalBalanceUsd();
        
        // Get wallet statistics
        $stats = [
            'total_balance_usd' => $totalBalanceUsd,
            'total_wallets' => $wallets->count(),
            'total_transactions' => WalletTransaction::whereIn('wallet_id', $wallets->pluck('id'))->count(),
            'this_month_transactions' => WalletTransaction::whereIn('wallet_id', $wallets->pluck('id'))
                ->whereMonth('created_at', now()->month)
                ->count(),
        ];
        
        return view('wallet.index', compact('wallets', 'recentTransactions', 'stats', 'currencies'));
    }

    /**
     * Show wallet details for specific currency.
     */
    public function show($currencyCode)
    {
        $user = Auth::user();
        $currency = Currency::where('code', $currencyCode)->where('is_active', true)->firstOrFail();
        $wallet = $user->getOrCreateWallet($currencyCode);
        
        // Get transactions for this wallet
        $transactions = $wallet->transactions()
            ->orderBy('created_at', 'desc')
            ->paginate(20);
        
        // Get wallet statistics
        $stats = [
            'total_credits' => $wallet->transactions()->credits()->completed()->sum('amount'),
            'total_debits' => $wallet->transactions()->debits()->completed()->sum('amount'),
            'pending_amount' => $wallet->pending_balance,
            'frozen_amount' => $wallet->frozen_balance,
            'transaction_count' => $wallet->transactions()->count(),
            'this_month_transactions' => $wallet->transactions()->whereMonth('created_at', now()->month)->count(),
        ];
        
        return view('wallet.show', compact('wallet', 'currency', 'transactions', 'stats'));
    }

    /**
     * Show currency exchange page.
     */
    public function exchange()
    {
        $user = Auth::user();
        $currencies = Currency::getActive();
        $wallets = $user->activeWallets()->get();
        
        return view('wallet.exchange', compact('currencies', 'wallets'));
    }

    /**
     * Process currency exchange.
     */
    public function processExchange(Request $request)
    {
        $request->validate([
            'from_currency' => 'required|exists:currencies,code',
            'to_currency' => 'required|exists:currencies,code|different:from_currency',
            'amount' => 'required|numeric|min:0.01',
        ]);

        $user = Auth::user();
        
        try {
            DB::beginTransaction();
            
            $fromCurrency = Currency::where('code', $request->from_currency)->first();
            $toCurrency = Currency::where('code', $request->to_currency)->first();
            
            $fromWallet = $user->getOrCreateWallet($request->from_currency);
            $toWallet = $user->getOrCreateWallet($request->to_currency);
            
            // Check if sufficient balance
            if ($fromWallet->available_balance < $request->amount) {
                throw new \Exception('Insufficient balance');
            }
            
            // Calculate exchange amount
            $exchangeAmount = $fromCurrency->convertTo($toCurrency, $request->amount);
            
            // Debit from source wallet
            $fromWallet->debit(
                $request->amount,
                "Currency exchange to {$toCurrency->code}",
                'exchange',
                null,
                [
                    'exchange_rate' => $fromCurrency->exchange_rate,
                    'to_currency' => $toCurrency->code,
                    'to_amount' => $exchangeAmount,
                ]
            );
            
            // Credit to destination wallet
            $toWallet->credit(
                $exchangeAmount,
                "Currency exchange from {$fromCurrency->code}",
                'exchange',
                null,
                [
                    'exchange_rate' => $toCurrency->exchange_rate,
                    'from_currency' => $fromCurrency->code,
                    'from_amount' => $request->amount,
                ]
            );
            
            DB::commit();
            
            return redirect()->route('wallet.index')->with('success', 
                "Successfully exchanged {$fromCurrency->formatAmount($request->amount)} to {$toCurrency->formatAmount($exchangeAmount)}"
            );
            
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Exchange failed: ' . $e->getMessage());
        }
    }

    /**
     * Show transaction details.
     */
    public function transaction($transactionId)
    {
        $user = Auth::user();
        $transaction = WalletTransaction::where('transaction_id', $transactionId)
            ->whereHas('wallet', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->with(['wallet.currency'])
            ->firstOrFail();
        
        return view('wallet.transaction', compact('transaction'));
    }

    /**
     * Get wallet balance for AJAX requests.
     */
    public function getBalance($currencyCode)
    {
        $user = Auth::user();
        $wallet = $user->getWallet($currencyCode);
        
        if (!$wallet) {
            return response()->json(['error' => 'Wallet not found'], 404);
        }
        
        return response()->json([
            'balance' => $wallet->balance,
            'available_balance' => $wallet->available_balance,
            'pending_balance' => $wallet->pending_balance,
            'frozen_balance' => $wallet->frozen_balance,
            'formatted_balance' => $wallet->formatted_balance,
            'formatted_available_balance' => $wallet->formatted_available_balance,
        ]);
    }

    /**
     * Get exchange rate between currencies.
     */
    public function getExchangeRate($fromCurrency, $toCurrency)
    {
        $from = Currency::where('code', $fromCurrency)->first();
        $to = Currency::where('code', $toCurrency)->first();
        
        if (!$from || !$to) {
            return response()->json(['error' => 'Currency not found'], 404);
        }
        
        $rate = $from->convertTo($to, 1);
        
        return response()->json([
            'rate' => $rate,
            'formatted_rate' => "1 {$from->code} = {$to->formatAmount($rate)}",
        ]);
    }

    /**
     * Show pay invoice form.
     */
    public function pay()
    {
        $user = Auth::user();
        $currencies = Currency::getActive();

        return view('wallet.pay', compact('currencies'));
    }

    /**
     * Process invoice payment.
     */
    public function processPay(Request $request)
    {
        $request->validate([
            'invoice_number' => 'required|string',
            'payment_method' => 'required|string|in:stripe,paypal,bank_transfer'
        ]);

        // Process payment logic here
        return redirect()->route('wallet.index')->with('success', 'Payment processed successfully!');
    }

    /**
     * Show add balance form.
     */
    public function addBalance()
    {
        $user = Auth::user();
        $currencies = Currency::getActive();

        return view('wallet.add-balance', compact('currencies'));
    }

    /**
     * Process add balance request.
     */
    public function processAddBalance(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'currency_code' => 'required|exists:currencies,code',
            'payment_method' => 'required|in:credit_card,bank_transfer,paypal,crypto',
        ]);

        $user = Auth::user();
        $currency = Currency::where('code', $request->currency_code)->first();
        $wallet = $user->getOrCreateWallet($request->currency_code);

        try {
            DB::beginTransaction();

            // In a real application, you would integrate with payment gateways here
            // For now, we'll simulate a successful payment

            // Add balance to wallet
            $wallet->credit(
                $request->amount,
                "Balance added via {$request->payment_method}",
                'deposit',
                null,
                [
                    'payment_method' => $request->payment_method,
                    'transaction_type' => 'balance_addition'
                ]
            );

            // Update user's available balance (for backward compatibility)
            if ($request->currency_code === 'USD') {
                $user->fill(['available_balance' => $user->available_balance + $request->amount]);
                $user->save();
            }

            DB::commit();

            return redirect()->route('wallet.index')->with('success',
                "Successfully added {$currency->formatAmount($request->amount)} to your wallet!"
            );

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to add balance: ' . $e->getMessage());
        }
    }

    /**
     * Show withdrawal form.
     */
    public function withdrawal()
    {
        $user = Auth::user();

        if (!$user->hasBankDetails()) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please add your bank details before making withdrawals.');
        }

        if (!$user->bank_details_verified) {
            return redirect()->route('wallet.index')
                ->with('error', 'Your bank details are pending verification. You cannot make withdrawals yet.');
        }

        $currencies = Currency::getActive();
        $wallets = $user->activeWallets()->get();

        return view('wallet.withdrawal', compact('currencies', 'wallets'));
    }

    /**
     * Process withdrawal request.
     */
    public function processWithdrawal(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'currency_code' => 'required|exists:currencies,code',
        ]);

        $user = Auth::user();

        if (!$user->hasBankDetails() || !$user->bank_details_verified) {
            return back()->with('error', 'Bank details not verified. Cannot process withdrawal.');
        }

        $currency = Currency::where('code', $request->currency_code)->first();
        $wallet = $user->getWallet($request->currency_code);

        if (!$wallet || $wallet->available_balance < $request->amount) {
            return back()->with('error', 'Insufficient balance for withdrawal.');
        }

        try {
            DB::beginTransaction();

            // Debit from wallet
            $wallet->debit(
                $request->amount,
                "Withdrawal to bank account",
                'withdrawal',
                null,
                [
                    'bank_name' => $user->bank_name,
                    'account_number' => $user->masked_account_number,
                    'status' => 'pending'
                ]
            );

            // Update user's available balance (for backward compatibility)
            if ($request->currency_code === 'USD') {
                $user->available_balance -= $request->amount;
                $user->save();
            }

            DB::commit();

            return redirect()->route('wallet.index')->with('success',
                "Withdrawal request for {$currency->formatAmount($request->amount)} has been submitted and is pending approval."
            );

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to process withdrawal: ' . $e->getMessage());
        }
    }

    /**
     * Convert reward points to balance
     */
    public function convertPoints(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'points_amount' => 'required|integer|min:100|max:' . ($user->reward_points ?? 0),
        ]);

        $pointsToConvert = $request->points_amount;
        $conversionRate = 0.01; // 1 point = $0.01
        $cashAmount = $pointsToConvert * $conversionRate;

        if ($user->reward_points < $pointsToConvert) {
            return back()->with('error', 'Insufficient reward points.');
        }

        try {
            DB::beginTransaction();

            // Deduct points from user
            $user->decrement('reward_points', $pointsToConvert);

            // Add cash to available balance
            $user->increment('available_balance', $cashAmount);

            // Create earning history record
            EarningHistory::create([
                'user_id' => $user->id,
                'type' => 'points_conversion',
                'amount' => $cashAmount,
                'points' => -$pointsToConvert,
                'currency' => 'USD',
                'description' => "Converted {$pointsToConvert} reward points to cash",
                'status' => 'paid',
                'auto_pay' => true,
                'paid_at' => now(),
            ]);

            DB::commit();

            return back()->with('success',
                "Successfully converted {$pointsToConvert} points to \${$cashAmount}!"
            );

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to convert points: ' . $e->getMessage());
        }
    }

    /**
     * Show earning history
     */
    public function earningHistory()
    {
        $user = Auth::user();

        $earnings = $user->earningHistory()
            ->latest()
            ->paginate(20);

        // Summary statistics
        $stats = [
            'total_earned' => $user->earningHistory()->sum('amount'),
            'total_paid' => $user->earningHistory()->where('status', 'paid')->sum('amount'),
            'total_pending' => $user->earningHistory()->where('status', 'pending')->sum('amount'),
            'this_month' => $user->earningHistory()->whereMonth('created_at', now()->month)->sum('amount'),
            'last_month' => $user->earningHistory()->whereMonth('created_at', now()->subMonth()->month)->sum('amount'),
        ];

        // Earnings by type
        $earningsByType = $user->earningHistory()
            ->selectRaw('type, SUM(amount) as total')
            ->groupBy('type')
            ->pluck('total', 'type')
            ->toArray();

        return view('wallet.earning-history', compact('earnings', 'stats', 'earningsByType'));
    }

}
