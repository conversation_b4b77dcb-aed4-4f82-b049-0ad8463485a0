<?php

namespace App\Http\Controllers;

use App\Services\ReferralService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    protected $referralService;

    public function __construct(ReferralService $referralService)
    {
        $this->referralService = $referralService;
    }

    /**
     * Show the enhanced dashboard with comprehensive statistics and charts.
     */
    public function index()
    {
        $user = Auth::user();

        // Enhanced statistics with new features (with safe checks)
        $stats = array_merge($this->referralService->getReferralStats($user), [
            'reward_points' => $user->reward_points ?? 0,
            'activated_stages' => method_exists($user, 'activeStageActivations') ? $user->activeStageActivations()->count() : 0,
            'project_participations' => method_exists($user, 'projectParticipations') ? $user->projectParticipations()->count() : 0,
            'completed_projects' => method_exists($user, 'projectParticipations') ? $user->projectParticipations()->where('status', 'verified')->count() : 0,
            'marketplace_sales' => method_exists($user, 'sellerOrders') ? $user->sellerOrders()->where('status', 'completed')->count() : 0,
            'marketplace_purchases' => method_exists($user, 'buyerOrders') ? $user->buyerOrders()->where('status', 'completed')->count() : 0,
            'this_week_referrals' => $user->referrals()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
        ]);

        // Monthly earnings data for chart (last 12 months)
        $monthlyEarnings = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $earnings = $user->commissions()
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->sum('amount');
            $monthlyEarnings[] = [
                'month' => $date->format('M'),
                'earnings' => $earnings
            ];
        }

        // Weekly referrals data for chart (last 8 weeks)
        $weeklyReferrals = [];
        for ($i = 7; $i >= 0; $i--) {
            $startOfWeek = now()->subWeeks($i)->startOfWeek();
            $endOfWeek = now()->subWeeks($i)->endOfWeek();
            $referrals = $user->referrals()
                ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
                ->count();
            $weeklyReferrals[] = [
                'week' => $startOfWeek->format('M j'),
                'referrals' => $referrals
            ];
        }

        // Earning breakdown by type (with safe checks)
        $earningBreakdown = [
            'referral' => $user->commissions()->where('type', 'direct_referral')->sum('amount'),
            'bonus' => $user->commissions()->where('type', 'milestone_bonus')->sum('amount'),
            'projects' => method_exists($user, 'earningHistory') ? $user->earningHistory()->where('type', 'project_completion')->sum('amount') : 0,
            'marketplace' => method_exists($user, 'earningHistory') ? $user->earningHistory()->where('type', 'marketplace_sale')->sum('amount') : 0,
        ];

        // Get recent activities (with safe checks)
        $recentReferrals = $user->referrals()->latest()->take(5)->get();
        $recentCommissions = $user->commissions()
            ->with('referral.referred')
            ->latest()
            ->take(5)
            ->get();
        $recentProjects = method_exists($user, 'projectParticipations') ?
            $user->projectParticipations()
                ->with('featuredProject')
                ->latest()
                ->take(3)
                ->get() : collect();
        $recentOrders = method_exists($user, 'buyerOrders') ?
            $user->buyerOrders()
                ->with(['product', 'seller'])
                ->latest()
                ->take(3)
                ->get() : collect();

        // Goals and achievements (with safe checks)
        $achievements = [
            'first_referral' => $user->referrals()->count() >= 1,
            'ten_referrals' => $user->referrals()->count() >= 10,
            'first_project' => method_exists($user, 'projectParticipations') ? $user->projectParticipations()->count() >= 1 : false,
            'first_sale' => method_exists($user, 'sellerOrders') ? $user->sellerOrders()->where('status', 'completed')->count() >= 1 : false,
            'hundred_points' => ($user->reward_points ?? 0) >= 100,
            'thousand_points' => ($user->reward_points ?? 0) >= 1000,
        ];

        // Quick actions based on user status
        $quickActions = $this->getQuickActions($user);

        // Get membership stages progression
        $membershipStages = $this->referralService->getMembershipStagesWithProgression($user);

        return view('dashboard.index', compact(
            'user', 'stats', 'monthlyEarnings', 'weeklyReferrals', 'earningBreakdown',
            'recentReferrals', 'recentCommissions', 'recentProjects', 'recentOrders',
            'achievements', 'quickActions', 'membershipStages'
        ));
    }

    /**
     * Show referral management page.
     */
    public function referrals()
    {
        $user = Auth::user();
        
        // Get all referrals with pagination
        $referrals = $user->referrals()
            ->with(['commissions'])
            ->latest()
            ->paginate(20);
        
        // Get referral tree
        $referralTree = $this->referralService->getReferralTree($user, 2);
        
        // Get referral statistics
        $stats = $this->referralService->getReferralStats($user);

        return view('dashboard.referrals', compact(
            'user',
            'referrals',
            'referralTree',
            'stats'
        ));
    }

    /**
     * Show earnings page.
     */
    public function earnings()
    {
        $user = Auth::user();
        
        // Get commissions with pagination
        $commissions = $user->commissions()
            ->with('referral.referred')
            ->latest()
            ->paginate(20);
        
        // Get withdrawal history
        $withdrawals = $user->withdrawals()
            ->latest()
            ->paginate(10);
        
        // Calculate earnings summary
        $earningsSummary = [
            'total_earnings' => $user->total_earnings,
            'available_balance' => $user->available_balance,
            'total_withdrawn' => $user->withdrawals()->completed()->sum('amount'),
            'pending_withdrawals' => $user->withdrawals()->whereIn('status', ['pending', 'processing'])->sum('amount'),
        ];

        return view('dashboard.earnings', compact(
            'user',
            'commissions',
            'withdrawals',
            'earningsSummary'
        ));
    }

    /**
     * Show membership stages page.
     */
    public function membership()
    {
        $user = Auth::user();
        
        // Get membership stages with progression
        $membershipStages = $this->referralService->getMembershipStagesWithProgression($user);
        
        // Get referral statistics
        $stats = $this->referralService->getReferralStats($user);

        return view('dashboard.membership', compact(
            'user',
            'membershipStages',
            'stats'
        ));
    }

    /**
     * Show profile page.
     */
    public function profile()
    {
        $user = Auth::user();

        return view('dashboard.profile', compact('user'));
    }

    /**
     * Update profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        return redirect()->route('dashboard.profile')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Get personalized quick actions for the user
     */
    private function getQuickActions($user)
    {
        $actions = [];

        // If user has no referrals, suggest sharing referral link
        if ($user->referrals()->count() === 0) {
            $actions[] = [
                'title' => 'Share Your Referral Link',
                'description' => 'Start earning by inviting friends',
                'url' => route('awareness.index'),
                'icon' => 'share',
                'color' => 'blue'
            ];
        }

        // If user hasn't activated any stages, suggest activation
        if (method_exists($user, 'activeStageActivations') && $user->activeStageActivations()->count() === 0) {
            $actions[] = [
                'title' => 'Activate Your First Stage',
                'description' => 'Unlock more features and earning opportunities',
                'url' => route('activations.index'),
                'icon' => 'star',
                'color' => 'yellow'
            ];
        }

        // If user has points, suggest conversion
        if (($user->reward_points ?? 0) >= 100) {
            $actions[] = [
                'title' => 'Convert Reward Points',
                'description' => "Convert {$user->reward_points} points to cash",
                'url' => route('wallet.index'),
                'icon' => 'currency',
                'color' => 'green'
            ];
        }

        // If user has balance, suggest withdrawal
        if ($user->available_balance >= 10) {
            $actions[] = [
                'title' => 'Withdraw Earnings',
                'description' => "Withdraw \${$user->available_balance} to your bank",
                'url' => route('wallet.withdrawal'),
                'icon' => 'download',
                'color' => 'purple'
            ];
        }

        // Suggest marketplace if user hasn't made any sales
        if (method_exists($user, 'sellerOrders') && $user->sellerOrders()->count() === 0) {
            $actions[] = [
                'title' => 'Start Selling',
                'description' => 'List your first product in the marketplace',
                'url' => route('marketplace.index'),
                'icon' => 'shopping',
                'color' => 'indigo'
            ];
        }

        return collect($actions)->take(4);
    }
}
