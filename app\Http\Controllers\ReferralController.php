<?php

namespace App\Http\Controllers;

use App\Services\ReferralService;
use App\Models\User;
use App\Models\EarningHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ReferralController extends Controller
{
    protected $referralService;

    public function __construct(ReferralService $referralService)
    {
        $this->referralService = $referralService;
    }

    /**
     * Show awareness dashboard with comprehensive tools.
     */
    public function index()
    {
        $user = Auth::user();
        $stats = $this->referralService->getReferralStats($user);

        // Generate referral URL
        $referralUrl = route('register', ['ref' => $user->referral_code]);

        // Get awareness tools
        $tools = $this->getAwarenessTools($user);

        // Get recent referrals (using the Referral model relationship)
        $recentReferrals = $user->referralRelationships()
            ->with(['referred', 'commissions'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get all direct referrals for the referral management section
        $referrals = $user->referrals()
            ->with(['commissions'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        // Add total_referrals count to each referral
        foreach ($referrals as $referral) {
            $referral->total_referrals = $referral->referrals()->count();
        }

        // Enhanced stats for referral management
        $stats['active_referrals'] = $user->referrals()->where('is_active', true)->count();
        $stats['pending_earnings'] = $user->commissions()->where('status', 'pending')->sum('amount');

        return view('awareness.index', compact('user', 'stats', 'referralUrl', 'tools', 'recentReferrals', 'referrals'));
    }

    /**
     * Show dedicated referrals management page.
     */
    public function referrals()
    {
        $user = Auth::user();
        $stats = $this->referralService->getReferralStats($user);

        // Get all referrals for the referral management section
        $referrals = $user->referrals()
            ->with(['commissions'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        // Add total_referrals count to each referral
        foreach ($referrals as $referral) {
            $referral->total_referrals = $referral->referrals()->count();
        }

        // Enhanced stats for referral management
        $stats['active_referrals'] = $user->referrals()->where('is_active', true)->count();
        $stats['pending_earnings'] = $user->commissions()->where('status', 'pending')->sum('amount');

        return view('awareness.referrals', compact('user', 'stats', 'referrals'));
    }

    /**
     * Validate referral code via AJAX.
     */
    public function validateCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string|max:10',
        ]);

        $referrer = $this->referralService->validateReferralCode($request->code);

        if ($referrer) {
            return response()->json([
                'valid' => true,
                'referrer_name' => $referrer->name,
                'message' => "Valid referral code from {$referrer->name}",
            ]);
        }

        return response()->json([
            'valid' => false,
            'message' => 'Invalid or inactive referral code',
        ]);
    }

    /**
     * Get referral tree data for visualization.
     */
    public function getTreeData(Request $request)
    {
        $user = Auth::user();
        $depth = $request->get('depth', 2);
        
        $treeData = $this->referralService->getReferralTree($user, $depth);

        return response()->json($treeData);
    }

    /**
     * Generate shareable referral materials.
     */
    public function generateMaterials()
    {
        $user = Auth::user();
        $referralUrl = route('register', ['ref' => $user->referral_code]);
        
        $materials = [
            'referral_url' => $referralUrl,
            'referral_code' => $user->referral_code,
            'social_media' => [
                'facebook' => "https://www.facebook.com/sharer/sharer.php?u=" . urlencode($referralUrl),
                'twitter' => "https://twitter.com/intent/tweet?url=" . urlencode($referralUrl) . "&text=" . urlencode("Join me on this amazing platform!"),
                'linkedin' => "https://www.linkedin.com/sharing/share-offsite/?url=" . urlencode($referralUrl),
                'whatsapp' => "https://wa.me/?text=" . urlencode("Join me on this amazing platform! " . $referralUrl),
            ],
            'email_template' => [
                'subject' => 'Join me on this amazing platform!',
                'body' => "Hi there!\n\nI wanted to share this amazing platform with you. You can join using my referral link: {$referralUrl}\n\nUse my referral code: {$user->referral_code}\n\nBest regards,\n{$user->name}",
            ],
            'text_templates' => [
                'short' => "Join me using code: {$user->referral_code} - {$referralUrl}",
                'medium' => "I'm earning great commissions on this platform! Join me using referral code {$user->referral_code}: {$referralUrl}",
                'long' => "I've been using this amazing referral platform and earning great commissions! The system has 6 different membership stages with increasing benefits. Join me using my referral code {$user->referral_code} and start earning today: {$referralUrl}",
            ],
        ];

        return response()->json($materials);
    }

    /**
     * Track referral link clicks (for analytics).
     */
    public function trackClick(Request $request)
    {
        $request->validate([
            'referral_code' => 'required|string|max:10',
            'source' => 'nullable|string|max:50',
        ]);

        // Here you could implement click tracking logic
        // For now, we'll just validate the code exists
        $referrer = $this->referralService->validateReferralCode($request->referral_code);

        if ($referrer) {
            // Log the click (you could create a ReferralClick model for this)
            Log::info('Referral link clicked', [
                'referrer_id' => $referrer->id,
                'referral_code' => $request->referral_code,
                'source' => $request->source,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false, 'message' => 'Invalid referral code']);
    }

    /**
     * Show awareness tools page.
     */
    public function tools()
    {
        $user = Auth::user();
        $referralUrl = route('register', ['ref' => $user->referral_code]);

        $tools = $this->getAwarenessTools($user);
        $banners = $this->getBannerOptions();
        $creatives = $this->getCreativeOptions();

        return view('awareness.tools', compact('user', 'referralUrl', 'tools', 'banners', 'creatives'));
    }

    /**
     * Show awareness analytics page.
     */
    public function analytics()
    {
        $user = Auth::user();
        $stats = $this->referralService->getReferralStats($user);

        // Get detailed analytics
        $analytics = [
            'clicks_by_source' => $this->getClicksBySource($user),
            'conversion_funnel' => $this->getConversionFunnel($user),
            'performance_over_time' => $this->getPerformanceOverTime($user),
            'top_performing_links' => $this->getTopPerformingLinks($user),
        ];

        return view('awareness.analytics', compact('user', 'stats', 'analytics'));
    }

    /**
     * Show detailed referral history with enhanced statistics
     */
    public function history()
    {
        $user = Auth::user();

        // Enhanced statistics
        $stats = [
            'total_referrals' => $user->referrals()->count(),
            'active_referrals' => $user->referrals()->where('is_active', true)->count(),
            'this_month_referrals' => $user->referrals()->whereMonth('created_at', now()->month)->count(),
            'this_week_referrals' => $user->referrals()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'total_earnings' => $user->commissions()->sum('amount'),
            'pending_earnings' => $user->commissions()->where('status', 'pending')->sum('amount'),
            'paid_earnings' => $user->commissions()->where('status', 'paid')->sum('amount'),
            'this_month_earnings' => $user->commissions()->whereMonth('created_at', now()->month)->sum('amount'),
            'conversion_rate' => $this->calculateConversionRate($user),
            'avg_earning_per_referral' => $this->calculateAverageEarning($user),
        ];

        // Monthly data for charts (last 12 months)
        $monthlyData = $this->getMonthlyReferralData($user);

        // Detailed referral history with earnings
        $referralHistory = $user->referrals()
            ->with(['commissions', 'activeStageActivations.membershipStage'])
            ->withCount('referrals as sub_referrals_count')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Earning breakdown by type
        $earningBreakdown = [
            'direct_referral' => $user->commissions()->where('type', 'direct_referral')->sum('amount'),
            'indirect_referral' => $user->commissions()->where('type', 'indirect_referral')->sum('amount'),
            'milestone_bonus' => $user->commissions()->where('type', 'milestone_bonus')->sum('amount'),
            'leadership_bonus' => $user->commissions()->where('type', 'leadership_bonus')->sum('amount'),
        ];

        return view('awareness.history', compact('user', 'stats', 'monthlyData', 'referralHistory', 'earningBreakdown'));
    }

    /**
     * Show latest 10 referrals table
     */
    public function latestReferrals()
    {
        $user = Auth::user();

        $latestReferrals = $user->referrals()
            ->with(['commissions', 'activeStageActivations.membershipStage'])
            ->withCount('referrals as sub_referrals_count')
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        return view('awareness.latest-referrals', compact('latestReferrals'));
    }

    /**
     * Calculate conversion rate
     */
    private function calculateConversionRate(User $user)
    {
        // This would typically use click tracking data
        // For now, we'll use a simple calculation
        $totalReferrals = $user->referrals()->count();
        $activeReferrals = $user->referrals()->where('is_active', true)->count();

        if ($totalReferrals === 0) return 0;

        return round(($activeReferrals / $totalReferrals) * 100, 2);
    }

    /**
     * Calculate average earning per referral
     */
    private function calculateAverageEarning(User $user)
    {
        $totalEarnings = $user->commissions()->sum('amount');
        $totalReferrals = $user->referrals()->count();

        if ($totalReferrals === 0) return 0;

        return round($totalEarnings / $totalReferrals, 2);
    }

    /**
     * Get monthly referral data for charts
     */
    private function getMonthlyReferralData(User $user)
    {
        $data = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $referralCount = $user->referrals()
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();

            $earningsAmount = $user->commissions()
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->sum('amount');

            $data[] = [
                'month' => $date->format('M Y'),
                'month_short' => $date->format('M'),
                'referrals' => $referralCount,
                'earnings' => $earningsAmount
            ];
        }

        return $data;
    }

    /**
     * Get comprehensive awareness tools.
     */
    private function getAwarenessTools($user)
    {
        $referralUrl = route('register', ['ref' => $user->referral_code]);

        return [
            'links' => [
                'primary' => $referralUrl,
                'short' => $this->generateShortLink($referralUrl),
                'tracked' => [
                    'email' => $referralUrl . '?source=email',
                    'social' => $referralUrl . '?source=social',
                    'direct' => $referralUrl . '?source=direct',
                    'blog' => $referralUrl . '?source=blog',
                ],
            ],
            'id_tracking' => [
                'referral_code' => $user->referral_code,
                'user_id' => $user->id,
                'tracking_pixel' => route('awareness.track-click', ['referral_code' => $user->referral_code]),
            ],
            'social_sharing' => [
                'facebook' => "https://www.facebook.com/sharer/sharer.php?u=" . urlencode($referralUrl),
                'twitter' => "https://twitter.com/intent/tweet?url=" . urlencode($referralUrl) . "&text=" . urlencode("Join me on this sustainable living journey!"),
                'linkedin' => "https://www.linkedin.com/sharing/share-offsite/?url=" . urlencode($referralUrl),
                'whatsapp' => "https://wa.me/?text=" . urlencode("Join me on this sustainable living journey! " . $referralUrl),
                'telegram' => "https://t.me/share/url?url=" . urlencode($referralUrl) . "&text=" . urlencode("Join me on this sustainable living journey!"),
            ],
            'email_templates' => [
                'personal' => [
                    'subject' => 'Join me on this sustainable living journey!',
                    'body' => "Hi there!\n\nI wanted to share this amazing sustainable living platform with you. It's helping me build better habits and earn while doing it!\n\nJoin using my link: {$referralUrl}\n\nOr use my code: {$user->referral_code}\n\nBest regards,\n{$user->name}",
                ],
                'professional' => [
                    'subject' => 'Sustainable Living Opportunity',
                    'body' => "Hello,\n\nI'm part of a sustainable living community that offers both personal growth and earning opportunities through their life journey stages.\n\nIf you're interested in sustainable living and building additional income, check it out: {$referralUrl}\n\nReferral code: {$user->referral_code}\n\nBest,\n{$user->name}",
                ],
                'casual' => [
                    'subject' => 'Check this out! 🌱',
                    'body' => "Hey!\n\nFound this cool platform for sustainable living - you can actually earn money while building better habits! 🌱💚\n\nThought you might be interested: {$referralUrl}\n\nUse code: {$user->referral_code}\n\nLet me know what you think!\n{$user->name}",
                ],
            ],
        ];
    }

    /**
     * Get banner options for different sizes and styles.
     */
    private function getBannerOptions()
    {
        return [
            'leaderboard' => [
                'size' => '728x90',
                'variants' => ['green', 'blue', 'earth'],
                'messages' => [
                    'Join the Sustainable Living Journey',
                    'Earn While Living Sustainably',
                    'Build Better Habits, Build Wealth',
                ],
            ],
            'rectangle' => [
                'size' => '300x250',
                'variants' => ['green', 'blue', 'earth'],
                'messages' => [
                    'Start Your Life Journey',
                    'Sustainable Living Rewards',
                    'Grow Green, Earn Green',
                ],
            ],
            'square' => [
                'size' => '250x250',
                'variants' => ['green', 'blue', 'earth'],
                'messages' => [
                    'Go Green',
                    'Live Better',
                    'Earn More',
                ],
            ],
            'skyscraper' => [
                'size' => '160x600',
                'variants' => ['green', 'blue', 'earth'],
                'messages' => [
                    'Sustainable Living Journey',
                    'Eco-Friendly Earnings',
                    'Green Life, Green Income',
                ],
            ],
        ];
    }

    /**
     * Get creative options for different marketing materials.
     */
    private function getCreativeOptions()
    {
        return [
            'images' => [
                'lifestyle' => [
                    'sustainable_home.jpg',
                    'green_family.jpg',
                    'eco_lifestyle.jpg',
                ],
                'earnings' => [
                    'money_growth.jpg',
                    'financial_freedom.jpg',
                    'passive_income.jpg',
                ],
                'journey' => [
                    'life_stages.jpg',
                    'personal_growth.jpg',
                    'achievement.jpg',
                ],
            ],
            'videos' => [
                'testimonials' => [
                    'member_success_story.mp4',
                    'earnings_showcase.mp4',
                    'lifestyle_transformation.mp4',
                ],
                'explainers' => [
                    'how_it_works.mp4',
                    'stage_benefits.mp4',
                    'getting_started.mp4',
                ],
            ],
            'infographics' => [
                'stage_progression.png',
                'earnings_potential.png',
                'sustainable_benefits.png',
            ],
        ];
    }

    /**
     * Generate a short link for easier sharing.
     */
    private function generateShortLink($url)
    {
        // Simple short link generation - in production you might use a service like bit.ly
        $hash = substr(md5($url), 0, 8);
        return url("/r/{$hash}");
    }

    /**
     * Get clicks by source for analytics.
     */
    private function getClicksBySource($user)
    {
        // This would typically query a clicks table
        return [
            'email' => 45,
            'social' => 32,
            'direct' => 28,
            'blog' => 15,
        ];
    }

    /**
     * Get conversion funnel data.
     */
    private function getConversionFunnel($user)
    {
        $totalClicks = 120;
        $registrations = $user->referrals()->count(); // Users referred by this user
        $activations = $user->referrals()->where('membership_tier', 'light')->count(); // Light members among referrals

        return [
            'clicks' => $totalClicks,
            'registrations' => $registrations,
            'activations' => $activations,
            'click_to_registration' => $totalClicks > 0 ? ($registrations / $totalClicks) * 100 : 0,
            'registration_to_activation' => $registrations > 0 ? ($activations / $registrations) * 100 : 0,
        ];
    }

    /**
     * Get performance over time.
     */
    private function getPerformanceOverTime($user)
    {
        // This would typically query historical data
        return [
            'last_30_days' => [
                'clicks' => 120,
                'referrals' => 8,
                'earnings' => 240.00,
            ],
            'last_7_days' => [
                'clicks' => 35,
                'referrals' => 2,
                'earnings' => 60.00,
            ],
            'yesterday' => [
                'clicks' => 5,
                'referrals' => 0,
                'earnings' => 0.00,
            ],
        ];
    }

    /**
     * Get top performing links.
     */
    private function getTopPerformingLinks($user)
    {
        $baseUrl = route('register', ['ref' => $user->referral_code]);

        return [
            [
                'url' => $baseUrl . '?source=email',
                'clicks' => 45,
                'conversions' => 5,
                'conversion_rate' => 11.1,
            ],
            [
                'url' => $baseUrl . '?source=social',
                'clicks' => 32,
                'conversions' => 3,
                'conversion_rate' => 9.4,
            ],
            [
                'url' => $baseUrl . '?source=direct',
                'clicks' => 28,
                'conversions' => 2,
                'conversion_rate' => 7.1,
            ],
        ];
    }
}
