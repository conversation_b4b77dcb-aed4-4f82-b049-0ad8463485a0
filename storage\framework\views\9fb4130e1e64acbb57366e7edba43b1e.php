<?php $__env->startSection('title', 'Stage Activation Pricing Management'); ?>

<?php $__env->startPush('head'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="<?php echo e(route('admin.platform-customization.index')); ?>" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="sr-only">Platform Customization</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Stage Activation Pricing</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="text-3xl font-bold text-gray-900 mt-2">💰 Stage Activation Pricing Management</h1>
                <p class="text-gray-600 mt-1">Configure stage names, pricing amounts, commissions, benefits, and descriptions</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="resetToDefaults()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset to Defaults
                </button>
                <button onclick="saveAllChanges()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save All Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Stage Management Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Stage 1: Starter -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-blue-50 border-b border-blue-200">
                <h3 class="text-lg font-medium text-blue-900">Stage 1: Starter</h3>
                <p class="text-sm text-blue-600">Beginning your sustainable life journey</p>
            </div>
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                        <input type="text" value="Starter" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="1" data-field="name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pricing Amount ($)</label>
                        <input type="number" value="50.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="1" data-field="pricing">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instant Commission ($)</label>
                        <input type="number" value="10.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="1" data-field="instant_commission">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Commission ($)</label>
                        <input type="number" value="5.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="1" data-field="bonus_commission">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Stage Description</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="1" data-field="description">Beginning your sustainable life journey with basic foundations and eco-friendly practices.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Benefits (one per line)</label>
                    <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="1" data-field="benefits">Access to starter resources
Basic sustainability guides
Community forum access
Monthly eco-tips newsletter</textarea>
                </div>
            </div>
        </div>

        <!-- Stage 2: Development -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-green-50 border-b border-green-200">
                <h3 class="text-lg font-medium text-green-900">Stage 2: Development</h3>
                <p class="text-sm text-green-600">Developing sustainable habits and knowledge</p>
            </div>
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                        <input type="text" value="Development" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="2" data-field="name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pricing Amount ($)</label>
                        <input type="number" value="100.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="2" data-field="pricing">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instant Commission ($)</label>
                        <input type="number" value="20.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="2" data-field="instant_commission">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Commission ($)</label>
                        <input type="number" value="10.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="2" data-field="bonus_commission">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Stage Description</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="2" data-field="description">Developing sustainable habits and expanding your knowledge of eco-friendly living practices.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Benefits (one per line)</label>
                    <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="2" data-field="benefits">Advanced sustainability courses
Personal eco-coach access
Green product discounts
Exclusive webinar series</textarea>
                </div>
            </div>
        </div>

        <!-- Stage 3: Action -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-yellow-50 border-b border-yellow-200">
                <h3 class="text-lg font-medium text-yellow-900">Stage 3: Action</h3>
                <p class="text-sm text-yellow-600">Taking active steps towards sustainability</p>
            </div>
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                        <input type="text" value="Action" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="3" data-field="name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pricing Amount ($)</label>
                        <input type="number" value="200.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="3" data-field="pricing">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instant Commission ($)</label>
                        <input type="number" value="40.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="3" data-field="instant_commission">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Commission ($)</label>
                        <input type="number" value="20.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="3" data-field="bonus_commission">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Stage Description</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="3" data-field="description">Taking active steps towards a fully sustainable lifestyle with practical implementation.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Benefits (one per line)</label>
                    <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="3" data-field="benefits">Action plan templates
Implementation guides
Monthly progress reviews
Priority support access</textarea>
                </div>
            </div>
        </div>

        <!-- Stage 4: Management -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-purple-50 border-b border-purple-200">
                <h3 class="text-lg font-medium text-purple-900">Stage 4: Management</h3>
                <p class="text-sm text-purple-600">Managing and optimizing sustainable systems</p>
            </div>
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                        <input type="text" value="Management" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="4" data-field="name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pricing Amount ($)</label>
                        <input type="number" value="350.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="4" data-field="pricing">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instant Commission ($)</label>
                        <input type="number" value="70.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="4" data-field="instant_commission">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Commission ($)</label>
                        <input type="number" value="35.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="4" data-field="bonus_commission">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Stage Description</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="4" data-field="description">Managing and optimizing your sustainable living systems for maximum efficiency and impact.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Benefits (one per line)</label>
                    <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="4" data-field="benefits">Advanced management tools
System optimization guides
Expert consultation calls
Leadership training access</textarea>
                </div>
            </div>
        </div>

        <!-- Stage 5: Abundance -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-red-50 border-b border-red-200">
                <h3 class="text-lg font-medium text-red-900">Stage 5: Abundance</h3>
                <p class="text-sm text-red-600">Living in abundance with sustainable practices</p>
            </div>
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                        <input type="text" value="Abundance" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="5" data-field="name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pricing Amount ($)</label>
                        <input type="number" value="500.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="5" data-field="pricing">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instant Commission ($)</label>
                        <input type="number" value="100.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="5" data-field="instant_commission">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Commission ($)</label>
                        <input type="number" value="50.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="5" data-field="bonus_commission">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Stage Description</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="5" data-field="description">Living in abundance while maintaining sustainable practices and sharing your knowledge with others.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Benefits (one per line)</label>
                    <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="5" data-field="benefits">Abundance mindset training
Wealth building strategies
Mentorship opportunities
Exclusive abundance community</textarea>
                </div>
            </div>
        </div>

        <!-- Stage 6: Retirement -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-indigo-50 border-b border-indigo-200">
                <h3 class="text-lg font-medium text-indigo-900">Stage 6: Retirement</h3>
                <p class="text-sm text-indigo-600">Enjoying the fruits of sustainable living</p>
            </div>
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                        <input type="text" value="Retirement" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="6" data-field="name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pricing Amount ($)</label>
                        <input type="number" value="1000.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="6" data-field="pricing">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instant Commission ($)</label>
                        <input type="number" value="200.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="6" data-field="instant_commission">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Commission ($)</label>
                        <input type="number" value="100.00" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="6" data-field="bonus_commission">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Stage Description</label>
                    <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="6" data-field="description">Enjoying the fruits of a lifetime of sustainable living while mentoring the next generation.</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Benefits (one per line)</label>
                    <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="6" data-field="benefits">Legacy planning tools
Retirement sustainability guides
Elder wisdom sharing platform
Lifetime achievement recognition</textarea>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-medium text-blue-900 mb-4">💡 Configuration Tips</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
            <div>
                <h4 class="font-medium mb-2">Pricing Strategy</h4>
                <ul class="space-y-1 text-blue-700">
                    <li>• Set progressive pricing that increases with stage value</li>
                    <li>• Consider your target audience's budget</li>
                    <li>• Balance accessibility with perceived value</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">Commission Structure</h4>
                <ul class="space-y-1 text-blue-700">
                    <li>• Instant commissions motivate immediate referrals</li>
                    <li>• Bonus commissions reward long-term engagement</li>
                    <li>• Higher stages should offer better commission rates</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function saveAllChanges() {
    const stages = {};

    // Collect all form data
    for (let i = 1; i <= 6; i++) {
        const stageData = {};
        const inputs = document.querySelectorAll(`[data-stage="${i}"]`);

        inputs.forEach(input => {
            const field = input.dataset.field;
            if (field === 'benefits') {
                stageData[field] = input.value.split('\n').filter(line => line.trim());
            } else {
                stageData[field] = input.value;
            }
        });

        stages[i] = stageData;
    }

    // Send data to server
    fetch('<?php echo e(route("admin.platform-customization.stage-pricing.update")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ stages: stages })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success notification
            showNotification('success', data.message);
        } else {
            showNotification('error', 'Failed to save changes. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('error', 'An error occurred while saving changes.');
    });
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all stage settings to default values? This action cannot be undone.')) {
        fetch('<?php echo e(route("admin.platform-customization.stage-pricing.reset")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('success', data.message);
                // Reload page to show default values
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification('error', 'Failed to reset settings. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', 'An error occurred while resetting settings.');
        });
    }
}

function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${
        type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                ${type === 'success'
                    ? '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>'
                    : '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>'
                }
            </svg>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/admin/platform-customization/stage-pricing.blade.php ENDPATH**/ ?>