<?php $__env->startSection('title', 'Stage Activation Pricing Management'); ?>

<?php $__env->startPush('head'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="<?php echo e(route('admin.platform-customization.index')); ?>" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="sr-only">Platform Customization</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Stage Activation Pricing</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="text-3xl font-bold text-gray-900 mt-2">💰 Stage Activation Pricing Management</h1>
                <p class="text-gray-600 mt-1">Configure stage names, pricing amounts, commissions, benefits, and descriptions</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="resetToDefaults()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset to Defaults
                </button>
                <button onclick="saveAllChanges()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save All Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Stage Management Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <?php
            $stageColors = [
                1 => ['bg' => 'bg-blue-50', 'border' => 'border-blue-200', 'text' => 'text-blue-900', 'subtitle' => 'text-blue-600'],
                2 => ['bg' => 'bg-green-50', 'border' => 'border-green-200', 'text' => 'text-green-900', 'subtitle' => 'text-green-600'],
                3 => ['bg' => 'bg-yellow-50', 'border' => 'border-yellow-200', 'text' => 'text-yellow-900', 'subtitle' => 'text-yellow-600'],
                4 => ['bg' => 'bg-purple-50', 'border' => 'border-purple-200', 'text' => 'text-purple-900', 'subtitle' => 'text-purple-600'],
                5 => ['bg' => 'bg-red-50', 'border' => 'border-red-200', 'text' => 'text-red-900', 'subtitle' => 'text-red-600'],
                6 => ['bg' => 'bg-indigo-50', 'border' => 'border-indigo-200', 'text' => 'text-indigo-900', 'subtitle' => 'text-indigo-600'],
            ];
            $stageDescriptions = [
                1 => 'Beginning your sustainable life journey',
                2 => 'Developing sustainable habits and knowledge',
                3 => 'Taking active steps towards sustainability',
                4 => 'Managing and optimizing sustainable systems',
                5 => 'Living in abundance with sustainable practices',
                6 => 'Enjoying the fruits of sustainable living',
            ];
        ?>

        <?php $__currentLoopData = $stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $colors = $stageColors[$stage->sort_order] ?? $stageColors[1];
                $subtitle = $stageDescriptions[$stage->sort_order] ?? 'Stage description';
            ?>

            <!-- Stage <?php echo e($stage->sort_order); ?>: <?php echo e($stage->name); ?> -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="px-6 py-4 <?php echo e($colors['bg']); ?> <?php echo e($colors['border']); ?> border-b">
                    <h3 class="text-lg font-medium <?php echo e($colors['text']); ?>">Stage <?php echo e($stage->sort_order); ?>: <?php echo e($stage->name); ?></h3>
                    <p class="text-sm <?php echo e($colors['subtitle']); ?>"><?php echo e($subtitle); ?></p>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                            <input type="text" value="<?php echo e($stage->name); ?>" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="name">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Activation Fee ($)</label>
                            <input type="number" value="<?php echo e($stage->activation_price); ?>" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="pricing">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Instant Commission ($)</label>
                            <input type="number" value="<?php echo e($stage->commission_rate); ?>" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="instant_commission">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Commission ($)</label>
                            <input type="number" value="<?php echo e($stage->activation_bonus); ?>" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="bonus_commission">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Min Yrs (Optional)</label>
                            <input type="number" value="<?php echo e($stage->min_years ?? ''); ?>" min="0" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="min_years" placeholder="e.g. 1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Max Yrs (Optional)</label>
                            <input type="number" value="<?php echo e($stage->max_years ?? ''); ?>" min="0" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="max_years" placeholder="e.g. 10">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Admin Commission ($)</label>
                            <input type="number" value="<?php echo e($stage->admin_commission ?? '0.00'); ?>" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="admin_commission" placeholder="e.g. 5.00">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stage Description</label>
                        <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="description"><?php echo e($stage->description); ?></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Benefits (one per line)</label>
                        <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" data-stage="<?php echo e($stage->sort_order); ?>" data-field="benefits"><?php echo e(is_array($stage->benefits) ? implode("\n", $stage->benefits) : $stage->benefits); ?></textarea>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </div>


    <!-- Help Section -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-medium text-blue-900 mb-4">💡 Configuration Tips</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
            <div>
                <h4 class="font-medium mb-2">Pricing Strategy</h4>
                <ul class="space-y-1 text-blue-700">
                    <li>• Set progressive pricing that increases with stage value</li>
                    <li>• Consider your target audience's budget</li>
                    <li>• Balance accessibility with perceived value</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">Commission Structure</h4>
                <ul class="space-y-1 text-blue-700">
                    <li>• Instant commissions motivate immediate referrals</li>
                    <li>• Bonus commissions reward long-term engagement</li>
                    <li>• Higher stages should offer better commission rates</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function saveAllChanges() {
    const stages = {};

    // Collect all form data
    for (let i = 1; i <= 6; i++) {
        const stageData = {};
        const inputs = document.querySelectorAll(`[data-stage="${i}"]`);

        inputs.forEach(input => {
            const field = input.dataset.field;
            if (field === 'benefits') {
                stageData[field] = input.value.split('\n').filter(line => line.trim());
            } else if (field === 'min_years' || field === 'max_years') {
                stageData[field] = input.value ? parseInt(input.value) : null;
            } else if (field === 'pricing' || field === 'instant_commission' || field === 'bonus_commission' || field === 'admin_commission') {
                stageData[field] = parseFloat(input.value) || 0;
            } else {
                stageData[field] = input.value;
            }
        });

        stages[i] = stageData;
    }

    // Send data to server
    fetch('<?php echo e(route("admin.platform-customization.stage-pricing.update")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ stages: stages })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success notification
            showNotification(data.message, 'success');
            // Optionally reload the page to reflect changes
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Failed to save changes. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while saving changes.', 'error');
    });
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all stage settings to default values? This action cannot be undone.')) {
        fetch('<?php echo e(route("admin.platform-customization.stage-pricing.reset")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                // Reload page to show default values
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification('Failed to reset settings. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while resetting settings.', 'error');
        });
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                ${type === 'success'
                    ? '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>'
                    : '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>'
                }
            </svg>
            <span class="text-sm">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove notification after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);

    // Add slide-in animation
    notification.style.transform = 'translateX(100%)';
    notification.style.transition = 'transform 0.3s ease-in-out';
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/admin/platform-customization/stage-pricing.blade.php ENDPATH**/ ?>