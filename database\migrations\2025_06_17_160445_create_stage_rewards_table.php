<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stage_rewards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('membership_stage_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->enum('type', ['instant_payment', 'bonus_payment']);
            $table->enum('requirement_type', [
                'project_completion',
                'achievements',
                'activities',
                'escrow_purchases',
                'referrals',
                'events',
                'custom'
            ]);
            $table->json('requirements'); // Flexible requirements structure
            $table->decimal('reward_amount', 10, 2)->nullable();
            $table->integer('reward_points')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_repeatable')->default(false);
            $table->integer('max_completions')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['membership_stage_id', 'is_active']);
        });

        // User reward progress tracking
        Schema::create('user_stage_reward_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('stage_reward_id')->constrained()->onDelete('cascade');
            $table->json('progress_data'); // Track progress towards requirements
            $table->integer('current_progress')->default(0);
            $table->integer('required_progress');
            $table->boolean('is_completed')->default(false);
            $table->timestamp('completed_at')->nullable();
            $table->integer('completion_count')->default(0);
            $table->timestamps();

            $table->unique(['user_id', 'stage_reward_id']);
            $table->index(['user_id', 'is_completed']);
        });

        // Reward redemptions
        Schema::create('stage_reward_redemptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('stage_reward_id')->constrained()->onDelete('cascade');
            $table->foreignId('progress_id')->constrained('user_stage_reward_progress')->onDelete('cascade');
            $table->decimal('amount_awarded', 10, 2)->nullable();
            $table->integer('points_awarded')->nullable();
            $table->enum('status', ['pending', 'approved', 'paid', 'cancelled'])->default('pending');
            $table->timestamp('redeemed_at');
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['stage_reward_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stage_reward_redemptions');
        Schema::dropIfExists('user_stage_reward_progress');
        Schema::dropIfExists('stage_rewards');
    }
};
