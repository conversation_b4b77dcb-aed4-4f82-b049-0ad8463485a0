<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserStageActivation;
use App\Models\Commission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ApprovalController extends Controller
{
    /**
     * Show user approvals page.
     */
    public function users()
    {
        if (!Auth::user() || !Auth::user()->is_admin) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $pendingUsers = User::where('account_status', 'pending')
            ->with('referrer')
            ->orderBy('created_at', 'desc')
            ->get();

        $recentlyProcessed = User::whereIn('account_status', ['approved', 'rejected'])
            ->with(['approvedBy'])
            ->whereNotNull('approved_at')
            ->orderBy('approved_at', 'desc')
            ->take(20)
            ->get();

        return view('admin.approvals.users', compact('pendingUsers', 'recentlyProcessed'));
    }

    /**
     * Approve user registration.
     */
    public function approveUser(User $user)
    {
        if (!Auth::user()->is_admin) {
            return back()->with('error', 'Access denied.');
        }

        $user->update([
            'account_status' => 'approved',
            'is_active' => true,
            'approved_at' => now(),
            'approved_by' => Auth::id(),
        ]);

        return back()->with('success', "User {$user->name} approved successfully");
    }

    /**
     * Reject user registration.
     */
    public function rejectUser(Request $request, User $user)
    {
        if (!Auth::user()->is_admin) {
            return back()->with('error', 'Access denied.');
        }

        $request->validate([
            'approval_notes' => 'required|string|max:1000',
        ]);

        $user->update([
            'account_status' => 'rejected',
            'is_active' => false,
            'approved_at' => now(),
            'approved_by' => Auth::id(),
            'approval_notes' => $request->approval_notes,
        ]);

        return back()->with('success', "User {$user->name} rejected");
    }

    /**
     * Show activation approvals page.
     */
    public function activations()
    {
        if (!Auth::user() || !Auth::user()->is_admin) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $pendingActivations = UserStageActivation::where('approval_status', 'pending')
            ->with(['user', 'membershipStage'])
            ->orderBy('activated_at', 'desc')
            ->get();

        $recentlyProcessed = UserStageActivation::whereIn('approval_status', ['approved', 'rejected'])
            ->with(['user', 'membershipStage', 'approvedBy'])
            ->whereNotNull('approved_at')
            ->orderBy('approved_at', 'desc')
            ->take(20)
            ->get();

        return view('admin.approvals.activations', compact('pendingActivations', 'recentlyProcessed'));
    }

    /**
     * Approve stage activation.
     */
    public function approveActivation(UserStageActivation $activation)
    {
        if (!Auth::user()->is_admin) {
            return back()->with('error', 'Access denied.');
        }

        $activation->update([
            'approval_status' => 'approved',
            'approved_at' => now(),
            'approved_by' => Auth::id(),
        ]);

        return back()->with('success', "Activation approved successfully");
    }

    /**
     * Reject stage activation.
     */
    public function rejectActivation(Request $request, UserStageActivation $activation)
    {
        if (!Auth::user()->is_admin) {
            return back()->with('error', 'Access denied.');
        }

        $request->validate([
            'approval_notes' => 'required|string|max:1000',
        ]);

        $activation->update([
            'approval_status' => 'rejected',
            'is_active' => false,
            'approved_at' => now(),
            'approved_by' => Auth::id(),
            'approval_notes' => $request->approval_notes,
        ]);

        return back()->with('success', "Activation rejected");
    }

    /**
     * Show commission approvals page.
     */
    public function commissions()
    {
        if (!Auth::user() || !Auth::user()->is_admin) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $pendingCommissions = Commission::where('approval_status', 'pending')
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();

        $totalPendingAmount = $pendingCommissions->sum('amount');

        $recentlyProcessed = Commission::whereIn('approval_status', ['approved', 'rejected'])
            ->with(['user', 'approvedBy'])
            ->whereNotNull('approved_at')
            ->orderBy('approved_at', 'desc')
            ->take(20)
            ->get();

        return view('admin.approvals.commissions', compact('pendingCommissions', 'recentlyProcessed', 'totalPendingAmount'));
    }

    /**
     * Approve commission.
     */
    public function approveCommission(Commission $commission)
    {
        if (!Auth::user()->is_admin) {
            return back()->with('error', 'Access denied.');
        }

        $commission->update([
            'approval_status' => 'approved',
            'status' => 'paid',
            'approved_at' => now(),
            'approved_by' => Auth::id(),
            'paid_at' => now(),
        ]);

        // Update user earnings
        $user = $commission->user;
        $user->total_earnings += $commission->amount;
        $user->available_balance += $commission->amount;
        $user->save();

        return back()->with('success', "Commission of \${$commission->amount} approved and paid");
    }

    /**
     * Reject commission.
     */
    public function rejectCommission(Request $request, Commission $commission)
    {
        if (!Auth::user()->is_admin) {
            return back()->with('error', 'Access denied.');
        }

        $request->validate([
            'approval_notes' => 'required|string|max:1000',
        ]);

        $commission->update([
            'approval_status' => 'rejected',
            'status' => 'rejected',
            'approved_at' => now(),
            'approved_by' => Auth::id(),
            'approval_notes' => $request->approval_notes,
        ]);

        return back()->with('success', "Commission rejected");
    }

    /**
     * Bulk approve commissions.
     */
    public function bulkApproveCommissions(Request $request)
    {
        if (!Auth::user()->is_admin) {
            return back()->with('error', 'Access denied.');
        }

        $request->validate([
            'commission_ids' => 'required|array',
            'commission_ids.*' => 'exists:commissions,id',
        ]);

        $commissions = Commission::whereIn('id', $request->commission_ids)
            ->where('approval_status', 'pending')
            ->get();

        $totalAmount = 0;
        foreach ($commissions as $commission) {
            $commission->update([
                'approval_status' => 'approved',
                'status' => 'paid',
                'approved_at' => now(),
                'approved_by' => Auth::id(),
                'paid_at' => now(),
            ]);

            // Update user earnings
            $user = $commission->user;
            $user->total_earnings += $commission->amount;
            $user->available_balance += $commission->amount;
            $user->save();

            $totalAmount += $commission->amount;
        }

        return back()->with('success', "{$commissions->count()} commissions approved totaling \${$totalAmount}");
    }

    /**
     * Bulk reject commissions.
     */
    public function bulkRejectCommissions(Request $request)
    {
        if (!Auth::user()->is_admin) {
            return back()->with('error', 'Access denied.');
        }

        $request->validate([
            'commission_ids' => 'required|array',
            'commission_ids.*' => 'exists:commissions,id',
            'approval_notes' => 'required|string|max:1000',
        ]);

        $count = Commission::whereIn('id', $request->commission_ids)
            ->where('approval_status', 'pending')
            ->update([
                'approval_status' => 'rejected',
                'status' => 'rejected',
                'approved_at' => now(),
                'approved_by' => Auth::id(),
                'approval_notes' => $request->approval_notes,
            ]);

        return back()->with('success', "{$count} commissions rejected");
    }
}
