@extends('layouts.app')

@section('title', 'Community Projects')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Community Projects</h1>
                <p class="mt-2 text-gray-600">Collaborate on sustainable living initiatives and make a positive impact</p>
            </div>
            <a href="{{ route('community.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Community
            </a>
        </div>

        <!-- Project Type Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Petition Projects -->
            <a href="{{ route('community.index', ['tab' => 'petition']) }}" 
               class="group relative bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 transform hover:scale-105">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-white">Petition Projects</h3>
                        <p class="text-sm text-white text-opacity-90">Vote on community initiatives</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-2xl font-bold text-white">{{ $stats['petition_count'] ?? 0 }}</div>
                    <div class="text-sm text-white text-opacity-90">Active petitions</div>
                </div>
            </a>

            <!-- Featured Projects -->
            <a href="{{ route('community.index', ['tab' => 'featured']) }}" 
               class="group relative bg-gradient-to-r from-green-400 to-blue-500 rounded-lg p-6 hover:from-green-500 hover:to-blue-600 transition-all duration-200 transform hover:scale-105">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-white">Featured Projects</h3>
                        <p class="text-sm text-white text-opacity-90">Support funded initiatives</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-2xl font-bold text-white">{{ $stats['featured_count'] ?? 0 }}</div>
                    <div class="text-sm text-white text-opacity-90">Active projects</div>
                </div>
            </a>

            <!-- Closed Petition Projects -->
            <a href="{{ route('community.index', ['tab' => 'closed-petition']) }}" 
               class="group relative bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg p-6 hover:from-purple-500 hover:to-pink-600 transition-all duration-200 transform hover:scale-105">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-white">Closed Petitions</h3>
                        <p class="text-sm text-white text-opacity-90">Completed petition projects</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-2xl font-bold text-white">{{ $stats['closed_petition_count'] ?? 0 }}</div>
                    <div class="text-sm text-white text-opacity-90">Completed</div>
                </div>
            </a>

            <!-- Closed Featured Projects -->
            <a href="{{ route('community.index', ['tab' => 'closed-featured']) }}" 
               class="group relative bg-gradient-to-r from-indigo-400 to-purple-500 rounded-lg p-6 hover:from-indigo-500 hover:to-purple-600 transition-all duration-200 transform hover:scale-105">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-white">Closed Featured</h3>
                        <p class="text-sm text-white text-opacity-90">Completed featured projects</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-2xl font-bold text-white">{{ $stats['closed_featured_count'] ?? 0 }}</div>
                    <div class="text-sm text-white text-opacity-90">Completed</div>
                </div>
            </a>
        </div>

        <!-- Quick Stats -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Project Overview</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">${{ number_format($stats['total_raised'] ?? 0) }}</div>
                    <div class="text-sm text-gray-500">Total Raised</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['total_volunteers'] ?? 0 }}</div>
                    <div class="text-sm text-gray-500">Volunteers</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['total_projects'] ?? 0 }}</div>
                    <div class="text-sm text-gray-500">Total Projects</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['success_rate'] ?? 0 }}%</div>
                    <div class="text-sm text-gray-500">Success Rate</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Project Activity</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                @if(isset($recentActivity) && $recentActivity->count() > 0)
                    @foreach($recentActivity as $activity)
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900">{{ $activity->description }}</p>
                            <p class="text-xs text-gray-500">{{ $activity->created_at->diffForHumans() }}</p>
                        </div>
                    </div>
                    @endforeach
                @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
                    <p class="mt-1 text-sm text-gray-500">Project activity will appear here as it happens.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
