<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DailyVisit extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'visit_date',
        'visit_count',
        'points_awarded'
    ];

    protected $casts = [
        'visit_date' => 'date',
        'points_awarded' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('visit_date', today());
    }

    public function scopeNotAwarded($query)
    {
        return $query->where('points_awarded', false);
    }
}
