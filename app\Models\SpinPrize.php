<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SpinPrize extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'emoji',
        'delivery_method',
        'cash_value',
        'points_value',
        'is_active',
        'weight',
        'max_per_day',
        'max_per_user',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'cash_value' => 'decimal:2',
    ];

    /**
     * Get active prizes
     */
    public static function active()
    {
        return static::where('is_active', true);
    }

    /**
     * Get available prizes for spinning
     */
    public static function getAvailablePrizes()
    {
        return static::active()
            ->select('id', 'name', 'description', 'emoji', 'delivery_method', 'cash_value', 'points_value', 'weight')
            ->get();
    }

    /**
     * Select a random prize based on weights
     */
    public static function selectRandomPrize()
    {
        $prizes = static::active()->get();
        
        if ($prizes->isEmpty()) {
            return null;
        }

        // Calculate total weight
        $totalWeight = $prizes->sum('weight');
        
        // Generate random number
        $random = mt_rand(1, $totalWeight);
        
        // Select prize based on weight
        $currentWeight = 0;
        foreach ($prizes as $prize) {
            $currentWeight += $prize->weight;
            if ($random <= $currentWeight) {
                return $prize;
            }
        }
        
        // Fallback to first prize
        return $prizes->first();
    }

    /**
     * Check if prize can be won today
     */
    public function canBeWonToday()
    {
        if (!$this->max_per_day) {
            return true;
        }

        $todayWins = UserSpin::where('prize_id', $this->id)
            ->whereDate('created_at', today())
            ->count();

        return $todayWins < $this->max_per_day;
    }

    /**
     * Check if user can win this prize
     */
    public function canBeWonByUser($userId)
    {
        if (!$this->max_per_user) {
            return true;
        }

        $userWins = UserSpin::where('user_id', $userId)
            ->where('prize_id', $this->id)
            ->count();

        return $userWins < $this->max_per_user;
    }

    /**
     * Get delivery method display name
     */
    public function getDeliveryMethodDisplayAttribute()
    {
        return match($this->delivery_method) {
            'events' => 'At Events',
            'cash' => 'Cash Payment',
            'pickup_station' => 'Pickup Station',
            'digital' => 'Digital Delivery',
            'instant' => 'Instant',
            default => ucfirst($this->delivery_method),
        };
    }
}
