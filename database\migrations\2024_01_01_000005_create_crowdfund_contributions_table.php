<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('crowdfund_contributions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('crowdfund_campaign_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 15, 2);
            $table->enum('contribution_type', ['donation', 'investment', 'loan'])->default('donation');
            $table->boolean('is_anonymous')->default(false);
            $table->enum('status', ['pending', 'completed', 'refunded', 'escrowed'])->default('pending');
            $table->string('transaction_id')->nullable();
            $table->text('message')->nullable();
            $table->decimal('expected_return', 15, 2)->nullable(); // For investments
            $table->date('expected_return_date')->nullable();
            $table->boolean('return_paid')->default(false);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('crowdfund_contributions');
    }
};
