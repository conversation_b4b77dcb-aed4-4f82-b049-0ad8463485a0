<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WalletTransaction;
use App\Models\UserStageActivation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class UserManagementController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->get('search');
        $status = $request->get('status');
        $stage = $request->get('stage');

        $users = User::query()
            ->when($search, function ($query, $search) {
                return $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%");
                });
            })
            ->when($status, function ($query, $status) {
                if ($status === 'active') {
                    return $query->whereNotNull('email_verified_at');
                } elseif ($status === 'inactive') {
                    return $query->whereNull('email_verified_at');
                }
            })
            ->when($stage, function ($query, $stage) {
                return $query->whereHas('stageActivations.membershipStage', function ($q) use ($stage) {
                    $q->where('slug', $stage);
                });
            })
            ->with(['stageActivations.membershipStage', 'wallets'])
            ->latest()
            ->paginate(20);

        $stats = [
            'total_users' => User::count(),
            'active_users' => User::whereNotNull('email_verified_at')->count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'admin_users' => User::where('is_admin', true)->count(),
        ];

        return view('admin.users.index', compact('users', 'stats', 'search', 'status', 'stage'));
    }

    public function show(User $user)
    {
        $user->load([
            'stageActivations.membershipStage',
            'wallets.currency',
            'referrals',
            'referredBy'
        ]);

        // Get wallet transactions
        $transactions = WalletTransaction::where('user_id', $user->id)
            ->with('currency')
            ->latest()
            ->paginate(10);

        // Get stage activations
        $stageActivations = UserStageActivation::where('user_id', $user->id)
            ->with('membershipStage')
            ->latest()
            ->get();

        // Calculate user statistics
        $stats = [
            'total_balance' => $user->wallets->sum('balance'),
            'total_transactions' => $transactions->total(),
            'active_stages' => $stageActivations->where('is_active', true)->count(),
            'total_referrals' => $user->referrals->count(),
        ];

        return view('admin.users.show', compact('user', 'transactions', 'stageActivations', 'stats'));
    }

    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'is_admin' => 'boolean',
            'is_active' => 'boolean',
            'bank_name' => 'nullable|string|max:255',
            'account_number' => 'nullable|string|max:50',
            'account_holder_name' => 'nullable|string|max:255',
            'routing_number' => 'nullable|string|max:50',
            'swift_code' => 'nullable|string|max:20',
        ]);

        // Update name field for backward compatibility
        $validated['name'] = $validated['first_name'] . ' ' . $validated['last_name'];

        // Handle email verification status
        if (!$validated['is_active'] && $user->email_verified_at) {
            $validated['email_verified_at'] = null;
        } elseif ($validated['is_active'] && !$user->email_verified_at) {
            $validated['email_verified_at'] = now();
        }

        $user->update($validated);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User updated successfully.');
    }

    public function updatePassword(Request $request, User $user)
    {
        $request->validate([
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'Password updated successfully.');
    }

    public function addBalance(Request $request, User $user)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'currency_code' => 'required|string|exists:currencies,code',
            'description' => 'required|string|max:255',
        ]);

        // Get or create wallet for the currency
        $wallet = $user->wallets()->firstOrCreate(
            ['currency_code' => $request->currency_code],
            ['balance' => 0]
        );

        // Add balance
        $wallet->increment('balance', $request->amount);

        // Create transaction record
        WalletTransaction::create([
            'user_id' => $user->id,
            'wallet_id' => $wallet->id,
            'type' => 'credit',
            'amount' => $request->amount,
            'currency_code' => $request->currency_code,
            'description' => $request->description,
            'reference_type' => 'admin_adjustment',
            'reference_id' => auth()->id(),
            'status' => 'completed',
            'admin_notes' => 'Balance added by admin: ' . auth()->user()->name,
        ]);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'Balance added successfully.');
    }

    public function deductBalance(Request $request, User $user)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'currency_code' => 'required|string|exists:currencies,code',
            'description' => 'required|string|max:255',
        ]);

        $wallet = $user->wallets()->where('currency_code', $request->currency_code)->first();

        if (!$wallet || $wallet->balance < $request->amount) {
            return redirect()->route('admin.users.show', $user)
                ->with('error', 'Insufficient balance in the specified currency.');
        }

        // Deduct balance
        $wallet->decrement('balance', $request->amount);

        // Create transaction record
        WalletTransaction::create([
            'user_id' => $user->id,
            'wallet_id' => $wallet->id,
            'type' => 'debit',
            'amount' => $request->amount,
            'currency_code' => $request->currency_code,
            'description' => $request->description,
            'reference_type' => 'admin_adjustment',
            'reference_id' => auth()->id(),
            'status' => 'completed',
            'admin_notes' => 'Balance deducted by admin: ' . auth()->user()->name,
        ]);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'Balance deducted successfully.');
    }

    public function toggleStatus(User $user)
    {
        if ($user->email_verified_at) {
            $user->update(['email_verified_at' => null]);
            $message = 'User deactivated successfully.';
        } else {
            $user->update(['email_verified_at' => now()]);
            $message = 'User activated successfully.';
        }

        return redirect()->route('admin.users.show', $user)
            ->with('success', $message);
    }

    public function destroy(User $user)
    {
        if ($user->is_admin && User::where('is_admin', true)->count() <= 1) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Cannot delete the last admin user.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    public function walletHistory(User $user)
    {
        $transactions = WalletTransaction::where('user_id', $user->id)
            ->with(['currency', 'wallet'])
            ->latest()
            ->paginate(50);

        return view('admin.users.wallet-history', compact('user', 'transactions'));
    }
}
