@extends('layouts.admin')

@section('title', 'Platform Customization')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">🎨 Platform Customization - UPDATED!</h1>
                <p class="text-gray-600 mt-1">Customize your platform's branding, appearance, and user experience</p>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mt-2">
                    <strong>✅ File changes are working!</strong> If you can see this green message, the updates are being applied correctly.
                </div>
                <!-- Debug info -->
                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mt-2" x-data>
                    <strong>🔍 Debug:</strong>
                    Active Tab: <span x-text="activeTab"></span> |
                    Active Sub-tab: <span x-text="activeSubTab[activeTab]"></span> |
                    Clicks: <span x-text="clickCount"></span>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="previewChanges()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Preview Changes
                </button>
                <button onclick="saveAllSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Save All Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced Navigation with Sub-menus -->
    <div class="mb-6" x-data="{ activeTab: 'branding', activeSubTab: { branding: 'logo', appearance: 'colors', layout: 'structure', content: 'messaging', features: 'core' }, clickCount: 0 }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <div class="relative">
                    <button @click="activeTab = 'branding'" :class="activeTab === 'branding' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd"></path>
                        </svg>
                        Branding & Identity
                    </button>
                </div>
                <div class="relative">
                    <button @click="activeTab = 'appearance'" :class="activeTab === 'appearance' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                        </svg>
                        Appearance & Themes
                    </button>
                </div>
                <div class="relative">
                    <button @click="activeTab = 'layout'" :class="activeTab === 'layout' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        Layout & Navigation
                    </button>
                </div>
                <div class="relative">
                    <button @click="activeTab = 'content'" :class="activeTab === 'content' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                        </svg>
                        Content & Messaging
                    </button>
                </div>
                <div class="relative">
                    <button @click="activeTab = 'features'" :class="activeTab === 'features' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                        </svg>
                        Features & Modules
                    </button>
                </div>
            </nav>
        </div>

        <!-- Sub-navigation for each main tab -->
        <div class="mt-4">
            <!-- Branding Sub-tabs -->
            <div x-show="activeTab === 'branding'" class="flex space-x-4 text-sm">
                <button @click="activeSubTab.branding = 'logo'; clickCount++; window.showNotification('✅ Logo & Assets settings loaded!', 'success'); alert('Sub-tab clicked: Logo & Assets')" :class="activeSubTab.branding === 'logo' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Logo & Assets</button>
                <button @click="activeSubTab.branding = 'identity'; window.showNotification('✅ Brand Identity settings loaded!', 'success')" :class="activeSubTab.branding === 'identity' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Brand Identity</button>
                <button @click="activeSubTab.branding = 'social'; window.showNotification('✅ Social Media settings loaded!', 'success')" :class="activeSubTab.branding === 'social' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Social Media</button>
                <button @click="activeSubTab.branding = 'seo'; window.showNotification('✅ SEO settings loaded!', 'success')" :class="activeSubTab.branding === 'seo' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">SEO Settings</button>
            </div>

            <!-- Appearance Sub-tabs -->
            <div x-show="activeTab === 'appearance'" class="flex space-x-4 text-sm">
                <button @click="activeSubTab.appearance = 'colors'; window.showNotification('🎨 Colors & Palette settings loaded!', 'success')" :class="activeSubTab.appearance === 'colors' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Colors & Palette</button>
                <button @click="activeSubTab.appearance = 'typography'; window.showNotification('📝 Typography settings loaded!', 'success')" :class="activeSubTab.appearance === 'typography' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Typography</button>
                <button @click="activeSubTab.appearance = 'themes'; window.showNotification('🎭 Theme settings loaded!', 'success')" :class="activeSubTab.appearance === 'themes' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Themes</button>
                <button @click="activeSubTab.appearance = 'animations'; window.showNotification('✨ Animation settings loaded!', 'success')" :class="activeSubTab.appearance === 'animations' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Animations</button>
            </div>

            <!-- Layout Sub-tabs -->
            <div x-show="activeTab === 'layout'" class="flex space-x-4 text-sm">
                <button @click="activeSubTab.layout = 'structure'; window.showNotification('📐 Page Structure loaded!', 'success')" :class="activeSubTab.layout === 'structure' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Page Structure</button>
                <button @click="activeSubTab.layout = 'navigation'; window.showNotification('🧭 Navigation settings loaded!', 'success')" :class="activeSubTab.layout === 'navigation' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Navigation</button>
                <button @click="activeSubTab.layout = 'responsive'; window.showNotification('📱 Responsive design loaded!', 'success')" :class="activeSubTab.layout === 'responsive' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Responsive</button>
                <button @click="activeSubTab.layout = 'widgets'; window.showNotification('🧩 Widgets settings loaded!', 'success')" :class="activeSubTab.layout === 'widgets' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Widgets</button>
            </div>

            <!-- Content Sub-tabs -->
            <div x-show="activeTab === 'content'" class="flex space-x-4 text-sm">
                <button @click="activeSubTab.content = 'messaging'; window.showNotification('📝 Site Messaging loaded!', 'success')" :class="activeSubTab.content === 'messaging' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Site Messaging</button>
                <button @click="activeSubTab.content = 'pages'; window.showNotification('📄 Static Pages loaded!', 'success')" :class="activeSubTab.content === 'pages' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Static Pages</button>
                <button @click="activeSubTab.content = 'emails'; window.showNotification('📧 Email Templates loaded!', 'success')" :class="activeSubTab.content === 'emails' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Email Templates</button>
                <button @click="activeSubTab.content = 'notifications'; window.showNotification('🔔 Notifications loaded!', 'success')" :class="activeSubTab.content === 'notifications' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Notifications</button>
            </div>

            <!-- Features Sub-tabs -->
            <div x-show="activeTab === 'features'" class="flex space-x-4 text-sm">
                <button @click="activeSubTab.features = 'core'; window.showNotification('⚙️ Core Features loaded!', 'success')" :class="activeSubTab.features === 'core' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Core Features</button>
                <button @click="activeSubTab.features = 'integrations'; window.showNotification('🔗 Integrations loaded!', 'success')" :class="activeSubTab.features === 'integrations' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Integrations</button>
                <button @click="activeSubTab.features = 'modules'; window.showNotification('📦 Modules loaded!', 'success')" :class="activeSubTab.features === 'modules' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Modules</button>
                <button @click="activeSubTab.features = 'advanced'; window.showNotification('🚀 Advanced Features loaded!', 'success')" :class="activeSubTab.features === 'advanced' ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-gray-900'" class="px-3 py-1 rounded-md">Advanced</button>
            </div>
        </div>

        <!-- Tab Content -->
        <div>
        <!-- Branding & Logo Tab -->
        <div x-show="activeTab === 'branding'" class="space-y-6">

            <!-- Logo & Assets Sub-tab -->
            <div x-show="activeSubTab.branding === 'logo'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🎨 Logo & Assets</h3>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Logo Upload</label>
                    <div class="flex items-center space-x-4">
                        <div class="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <button onclick="uploadLogo()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Upload New Logo
                            </button>
                            <p class="text-xs text-gray-500 mt-1">Recommended: 200x200px, PNG or SVG</p>
                        </div>
                    </div>
                </div>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Favicon</label>
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                            <div class="w-4 h-4 bg-indigo-600 rounded"></div>
                        </div>
                        <div>
                            <button onclick="uploadFavicon()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Upload Favicon
                            </button>
                            <p class="text-xs text-gray-500 mt-1">32x32px ICO or PNG file</p>
                        </div>
                    </div>
                </div>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Brand Assets</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <button class="text-indigo-600 hover:text-indigo-500">Upload Header Image</button>
                                <p class="text-sm text-gray-500">1920x400px recommended</p>
                            </div>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <button class="text-indigo-600 hover:text-indigo-500">Upload Background Image</button>
                                <p class="text-sm text-gray-500">1920x1080px recommended</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Brand Identity Sub-tab -->
            <div x-show="activeSubTab.branding === 'identity'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🏢 Brand Identity</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Platform Name</label>
                        <input type="text" value="Earth-Friendly Platform" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tagline</label>
                        <input type="text" value="Building a sustainable future together" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                </div>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Company Description</label>
                    <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" placeholder="Describe your company's mission and values...">We are dedicated to creating sustainable solutions for a better tomorrow. Our platform connects environmentally conscious individuals and businesses to drive positive change.</textarea>
                </div>

                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                        <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Support Email</label>
                        <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                </div>
            </div>

            <!-- Social Media Sub-tab -->
            <div x-show="activeSubTab.branding === 'social'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📱 Social Media</h3>

                <div class="space-y-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/></svg>
                        </div>
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Twitter</label>
                            <input type="url" placeholder="https://twitter.com/yourcompany" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
                        </div>
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Facebook</label>
                            <input type="url" placeholder="https://facebook.com/yourcompany" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>
                        </div>
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">LinkedIn</label>
                            <input type="url" placeholder="https://linkedin.com/company/yourcompany" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/></svg>
                        </div>
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Instagram</label>
                            <input type="url" placeholder="https://instagram.com/yourcompany" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO Settings Sub-tab -->
            <div x-show="activeSubTab.branding === 'seo'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🔍 SEO Settings</h3>

                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                        <input type="text" value="Earth-Friendly Platform - Sustainable Solutions" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <p class="text-xs text-gray-500 mt-1">Recommended: 50-60 characters</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                        <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" placeholder="Describe your platform for search engines...">Join our community of environmentally conscious individuals working together to create sustainable solutions for a better tomorrow.</textarea>
                        <p class="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Keywords</label>
                        <input type="text" value="sustainability, environment, eco-friendly, green living, climate change" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <p class="text-xs text-gray-500 mt-1">Separate keywords with commas</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Open Graph Image</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <button class="text-indigo-600 hover:text-indigo-500">Upload OG Image</button>
                                <p class="text-sm text-gray-500">1200x630px recommended</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Appearance & Themes Tab -->
        <div x-show="activeTab === 'appearance'" class="space-y-6">

            <!-- Colors & Palette Sub-tab -->
            <div x-show="activeSubTab.appearance === 'colors'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🎨 Colors & Palette</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
                        <div class="flex items-center space-x-2">
                            <input type="color" value="#4F46E5" class="w-12 h-10 border border-gray-300 rounded">
                            <input type="text" value="#4F46E5" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Secondary Color</label>
                        <div class="flex items-center space-x-2">
                            <input type="color" value="#10B981" class="w-12 h-10 border border-gray-300 rounded">
                            <input type="text" value="#10B981" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Accent Color</label>
                        <div class="flex items-center space-x-2">
                            <input type="color" value="#F59E0B" class="w-12 h-10 border border-gray-300 rounded">
                            <input type="text" value="#F59E0B" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Background Color</label>
                        <div class="flex items-center space-x-2">
                            <input type="color" value="#F9FAFB" class="w-12 h-10 border border-gray-300 rounded">
                            <input type="text" value="#F9FAFB" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Typography Sub-tab -->
            <div x-show="activeSubTab.appearance === 'typography'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📝 Typography</h3>

                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Primary Font</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>Inter (Current)</option>
                            <option>Roboto</option>
                            <option>Open Sans</option>
                            <option>Lato</option>
                            <option>Montserrat</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Heading Font</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>Inter (Current)</option>
                            <option>Playfair Display</option>
                            <option>Merriweather</option>
                            <option>Source Serif Pro</option>
                        </select>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Base Font Size</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option>14px</option>
                                <option>16px (Current)</option>
                                <option>18px</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Line Height</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option>1.4</option>
                                <option>1.5 (Current)</option>
                                <option>1.6</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Themes Sub-tab -->
            <div x-show="activeSubTab.appearance === 'themes'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🎭 Themes</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="border-2 border-indigo-500 rounded-lg p-4 cursor-pointer">
                        <div class="w-full h-32 bg-gradient-to-br from-indigo-500 to-purple-600 rounded mb-3"></div>
                        <h4 class="font-medium text-gray-900">Modern Professional</h4>
                        <p class="text-sm text-gray-500">Clean and professional design</p>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">Active</span>
                        </div>
                    </div>
                    <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-gray-300">
                        <div class="w-full h-32 bg-gradient-to-br from-green-500 to-blue-600 rounded mb-3"></div>
                        <h4 class="font-medium text-gray-900">Earth-Friendly</h4>
                        <p class="text-sm text-gray-500">Nature-inspired green theme</p>
                        <div class="mt-2">
                            <button class="text-sm text-indigo-600 hover:text-indigo-500">Activate</button>
                        </div>
                    </div>
                    <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-gray-300">
                        <div class="w-full h-32 bg-gradient-to-br from-gray-800 to-gray-900 rounded mb-3"></div>
                        <h4 class="font-medium text-gray-900">Dark Mode</h4>
                        <p class="text-sm text-gray-500">Elegant dark theme</p>
                        <div class="mt-2">
                            <button class="text-sm text-indigo-600 hover:text-indigo-500">Activate</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Animations Sub-tab -->
            <div x-show="activeSubTab.appearance === 'animations'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">✨ Animations</h3>

                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Page Transitions</h4>
                            <p class="text-sm text-gray-500">Smooth transitions between pages</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Hover Effects</h4>
                            <p class="text-sm text-gray-500">Interactive hover animations</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Loading Animations</h4>
                            <p class="text-sm text-gray-500">Animated loading indicators</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Animation Speed</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>Slow</option>
                            <option>Normal (Current)</option>
                            <option>Fast</option>
                        </select>
                    </div>
                </div>
            </div>
                    </div>
                    <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-gray-300">
                        <div class="w-full h-32 bg-gradient-to-br from-gray-800 to-gray-900 rounded mb-3"></div>
                        <h4 class="font-medium text-gray-900">Dark Mode</h4>
                        <p class="text-sm text-gray-500">Elegant dark interface</p>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Typography</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Primary Font</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>Inter (Recommended)</option>
                            <option>Roboto</option>
                            <option>Open Sans</option>
                            <option>Lato</option>
                            <option>Poppins</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Heading Font</label>
                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>Inter (Recommended)</option>
                            <option>Montserrat</option>
                            <option>Playfair Display</option>
                            <option>Source Sans Pro</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Layout & Navigation Tab -->
        <div x-show="activeTab === 'layout'" class="space-y-6">

            <!-- Page Structure Sub-tab -->
            <div x-show="activeSubTab.layout === 'structure'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📐 Page Structure</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="border-2 border-indigo-500 rounded-lg p-4 cursor-pointer">
                        <div class="w-full h-24 border-2 border-gray-300 rounded mb-2">
                            <div class="h-4 bg-gray-300 mb-1"></div>
                            <div class="flex">
                                <div class="w-1/4 h-16 bg-gray-200 mr-1"></div>
                                <div class="flex-1 h-16 bg-gray-100"></div>
                            </div>
                        </div>
                        <h4 class="font-medium text-gray-900 text-sm">Sidebar Left</h4>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">Active</span>
                        </div>
                    </div>
                    <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-gray-300">
                        <div class="w-full h-24 border-2 border-gray-300 rounded mb-2">
                            <div class="h-4 bg-gray-300 mb-1"></div>
                            <div class="h-16 bg-gray-100"></div>
                        </div>
                        <h4 class="font-medium text-gray-900 text-sm">Full Width</h4>
                        <div class="mt-2">
                            <button class="text-sm text-indigo-600 hover:text-indigo-500">Select</button>
                        </div>
                    </div>
                    <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-gray-300">
                        <div class="w-full h-24 border-2 border-gray-300 rounded mb-2">
                            <div class="h-4 bg-gray-300 mb-1"></div>
                            <div class="flex">
                                <div class="flex-1 h-16 bg-gray-100 mr-1"></div>
                                <div class="w-1/4 h-16 bg-gray-200"></div>
                            </div>
                        </div>
                        <h4 class="font-medium text-gray-900 text-sm">Sidebar Right</h4>
                        <div class="mt-2">
                            <button class="text-sm text-indigo-600 hover:text-indigo-500">Select</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Sub-tab -->
            <div x-show="activeSubTab.layout === 'navigation'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🧭 Navigation</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Sticky Navigation</h4>
                            <p class="text-sm text-gray-500">Keep navigation bar fixed at top when scrolling</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Breadcrumbs</h4>
                            <p class="text-sm text-gray-500">Show navigation breadcrumbs on pages</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Search Bar</h4>
                            <p class="text-sm text-gray-500">Show search functionality in navigation</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Responsive Sub-tab -->
            <div x-show="activeSubTab.layout === 'responsive'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📱 Responsive Design</h3>

                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-4">Breakpoints</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Mobile (< 768px)</span>
                                <span class="text-sm font-medium text-green-600">✓ Optimized</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Tablet (768px - 1024px)</span>
                                <span class="text-sm font-medium text-green-600">✓ Optimized</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Desktop (> 1024px)</span>
                                <span class="text-sm font-medium text-green-600">✓ Optimized</span>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Mobile-First Design</h4>
                            <p class="text-sm text-gray-500">Prioritize mobile experience</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Widgets Sub-tab -->
            <div x-show="activeSubTab.layout === 'widgets'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🧩 Widgets</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-medium text-gray-900">Recent Activity</h4>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                        <p class="text-xs text-gray-500">Show recent user activity</p>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-medium text-gray-900">Quick Stats</h4>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer">
                                <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                        <p class="text-xs text-gray-500">Display key metrics</p>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-medium text-gray-900">Weather Widget</h4>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                        <p class="text-xs text-gray-500">Show current weather</p>
                    </div>
                </div>
            </div>
                    <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-gray-300">
                        <div class="w-full h-24 border-2 border-gray-300 rounded mb-2">
                            <div class="h-4 bg-gray-300 mb-1"></div>
                            <div class="flex">
                                <div class="flex-1 h-16 bg-gray-100 mr-1"></div>
                                <div class="w-1/4 h-16 bg-gray-200"></div>
                            </div>
                        </div>
                        <h4 class="font-medium text-gray-900 text-sm">Sidebar Right</h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content & Messaging Tab -->
        <div x-show="activeTab === 'content'" class="space-y-6">

            <!-- Site Messaging Sub-tab -->
            <div x-show="activeSubTab.content === 'messaging'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📝 Site Messaging</h3>

                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Homepage Hero Title</label>
                        <input type="text" value="Welcome to Earth-Friendly Platform" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Homepage Hero Subtitle</label>
                        <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" placeholder="Enter your hero subtitle...">Join our community of environmentally conscious individuals working together to create a sustainable future for our planet.</textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Call-to-Action Button Text</label>
                        <input type="text" value="Get Started Today" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Welcome Message for New Users</label>
                        <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" placeholder="Enter welcome message...">Welcome to our platform! We're excited to have you join our community of change-makers working towards a more sustainable future.</textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Footer Copyright Text</label>
                        <input type="text" value="© 2024 Earth-Friendly Platform. All rights reserved." class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    </div>
                </div>
            </div>

            <!-- Static Pages Sub-tab -->
            <div x-show="activeSubTab.content === 'pages'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📄 Static Pages</h3>

                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <h4 class="text-lg font-medium text-gray-900">Manage Static Pages</h4>
                        <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                            </svg>
                            Add New Page
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="font-medium text-gray-900">About Us</h5>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Published</span>
                            </div>
                            <p class="text-sm text-gray-500 mb-3">Learn about our mission and values</p>
                            <div class="flex space-x-2">
                                <button class="text-sm text-indigo-600 hover:text-indigo-500">Edit</button>
                                <button class="text-sm text-gray-600 hover:text-gray-500">Preview</button>
                                <button class="text-sm text-red-600 hover:text-red-500">Delete</button>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="font-medium text-gray-900">Privacy Policy</h5>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Published</span>
                            </div>
                            <p class="text-sm text-gray-500 mb-3">Our privacy and data protection policy</p>
                            <div class="flex space-x-2">
                                <button class="text-sm text-indigo-600 hover:text-indigo-500">Edit</button>
                                <button class="text-sm text-gray-600 hover:text-gray-500">Preview</button>
                                <button class="text-sm text-red-600 hover:text-red-500">Delete</button>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="font-medium text-gray-900">Terms of Service</h5>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Draft</span>
                            </div>
                            <p class="text-sm text-gray-500 mb-3">Platform terms and conditions</p>
                            <div class="flex space-x-2">
                                <button class="text-sm text-indigo-600 hover:text-indigo-500">Edit</button>
                                <button class="text-sm text-gray-600 hover:text-gray-500">Preview</button>
                                <button class="text-sm text-red-600 hover:text-red-500">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Templates Sub-tab -->
            <div x-show="activeSubTab.content === 'emails'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📧 Email Templates</h3>

                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <h4 class="text-lg font-medium text-gray-900">Email Templates</h4>
                        <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                            </svg>
                            Create Template
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="font-medium text-gray-900">Welcome Email</h5>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            </div>
                            <p class="text-sm text-gray-500 mb-3">Sent to new users upon registration</p>
                            <div class="space-y-2">
                                <div class="text-xs text-gray-600">Subject: Welcome to Earth-Friendly Platform!</div>
                                <div class="text-xs text-gray-600">Last modified: 2 days ago</div>
                            </div>
                            <div class="flex space-x-2 mt-3">
                                <button class="text-sm text-indigo-600 hover:text-indigo-500">Edit</button>
                                <button class="text-sm text-gray-600 hover:text-gray-500">Preview</button>
                                <button class="text-sm text-blue-600 hover:text-blue-500">Test Send</button>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="font-medium text-gray-900">Password Reset</h5>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            </div>
                            <p class="text-sm text-gray-500 mb-3">Password reset instructions</p>
                            <div class="space-y-2">
                                <div class="text-xs text-gray-600">Subject: Reset Your Password</div>
                                <div class="text-xs text-gray-600">Last modified: 1 week ago</div>
                            </div>
                            <div class="flex space-x-2 mt-3">
                                <button class="text-sm text-indigo-600 hover:text-indigo-500">Edit</button>
                                <button class="text-sm text-gray-600 hover:text-gray-500">Preview</button>
                                <button class="text-sm text-blue-600 hover:text-blue-500">Test Send</button>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="font-medium text-gray-900">Weekly Newsletter</h5>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Draft</span>
                            </div>
                            <p class="text-sm text-gray-500 mb-3">Weekly updates and news</p>
                            <div class="space-y-2">
                                <div class="text-xs text-gray-600">Subject: This Week in Sustainability</div>
                                <div class="text-xs text-gray-600">Last modified: 3 days ago</div>
                            </div>
                            <div class="flex space-x-2 mt-3">
                                <button class="text-sm text-indigo-600 hover:text-indigo-500">Edit</button>
                                <button class="text-sm text-gray-600 hover:text-gray-500">Preview</button>
                                <button class="text-sm text-blue-600 hover:text-blue-500">Test Send</button>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="font-medium text-gray-900">Account Verification</h5>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            </div>
                            <p class="text-sm text-gray-500 mb-3">Email verification for new accounts</p>
                            <div class="space-y-2">
                                <div class="text-xs text-gray-600">Subject: Verify Your Email Address</div>
                                <div class="text-xs text-gray-600">Last modified: 5 days ago</div>
                            </div>
                            <div class="flex space-x-2 mt-3">
                                <button class="text-sm text-indigo-600 hover:text-indigo-500">Edit</button>
                                <button class="text-sm text-gray-600 hover:text-gray-500">Preview</button>
                                <button class="text-sm text-blue-600 hover:text-blue-500">Test Send</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Sub-tab -->
            <div x-show="activeSubTab.content === 'notifications'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🔔 Notifications</h3>

                <div class="space-y-6">
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Notification Settings</h4>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Email Notifications</h5>
                                    <p class="text-sm text-gray-500">Send notifications via email</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Push Notifications</h5>
                                    <p class="text-sm text-gray-500">Browser push notifications</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">SMS Notifications</h5>
                                    <p class="text-sm text-gray-500">Text message notifications</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Notification Types</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="font-medium text-gray-900">New User Registration</h5>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                                    </label>
                                </div>
                                <p class="text-sm text-gray-500">Notify when new users register</p>
                            </div>

                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="font-medium text-gray-900">System Updates</h5>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                                    </label>
                                </div>
                                <p class="text-sm text-gray-500">Platform maintenance and updates</p>
                            </div>

                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="font-medium text-gray-900">Security Alerts</h5>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                                    </label>
                                </div>
                                <p class="text-sm text-gray-500">Security-related notifications</p>
                            </div>

                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="font-medium text-gray-900">Payment Notifications</h5>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                                    </label>
                                </div>
                                <p class="text-sm text-gray-500">Payment and billing notifications</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Company Description</label>
                        <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">Earth-Friendly Platform is dedicated to connecting people who care about environmental sustainability and want to make a positive impact on our planet.</textarea>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                            <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Support Email</label>
                            <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features & Modules Tab -->
        <div x-show="activeTab === 'features'" class="space-y-6">

            <!-- Core Features Sub-tab -->
            <div x-show="activeSubTab.features === 'core'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">⚙️ Core Features</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900 mb-3">User Management</h4>

                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">User Registration</h5>
                                <p class="text-sm text-gray-500">Allow new users to register</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Email Verification</h5>
                                <p class="text-sm text-gray-500">Require email verification</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Referral System</h5>
                                <p class="text-sm text-gray-500">User referral program</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900 mb-3">Platform Features</h4>

                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Wallet System</h5>
                                <p class="text-sm text-gray-500">Digital wallet functionality</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Stage System</h5>
                                <p class="text-sm text-gray-500">Membership progression</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Community Projects</h5>
                                <p class="text-sm text-gray-500">Project collaboration</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Integrations Sub-tab -->
            <div x-show="activeSubTab.features === 'integrations'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🔗 Integrations</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M13.5 2c-5.621 0-10.211 4.443-10.475 10h3.025c.264-3.292 2.917-5.945 6.209-6.209V2.525C12.259 2.264 12.879 2 13.5 2zm0 20c5.621 0 10.211-4.443 10.475-10h-3.025c-.264 3.292-2.917 5.945-6.209 6.209v3.266c0 .261-.62.525-1.241.525z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">Stripe</h4>
                                    <p class="text-sm text-gray-500">Payment processing</p>
                                </div>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                        <button class="text-sm text-indigo-600 hover:text-indigo-500">Configure</button>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">Mailchimp</h4>
                                    <p class="text-sm text-gray-500">Email marketing</p>
                                </div>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer">
                                <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                        <button class="text-sm text-indigo-600 hover:text-indigo-500">Configure</button>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">Google Analytics</h4>
                                    <p class="text-sm text-gray-500">Website analytics</p>
                                </div>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer">
                                <div class="w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                        <button class="text-sm text-indigo-600 hover:text-indigo-500">Configure</button>
                    </div>
                </div>
            </div>

            <!-- Modules Sub-tab -->
            <div x-show="activeSubTab.features === 'modules'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📦 Modules</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">Blog Module</h4>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">Content management and blogging</p>
                        <div class="flex space-x-2">
                            <button class="text-sm text-indigo-600 hover:text-indigo-500">Configure</button>
                            <button class="text-sm text-red-600 hover:text-red-500">Disable</button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">Forum Module</h4>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">Community discussion forums</p>
                        <div class="flex space-x-2">
                            <button class="text-sm text-indigo-600 hover:text-indigo-500">Enable</button>
                            <button class="text-sm text-gray-600 hover:text-gray-500">Preview</button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">E-commerce Module</h4>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">Online store and marketplace</p>
                        <div class="flex space-x-2">
                            <button class="text-sm text-indigo-600 hover:text-indigo-500">Configure</button>
                            <button class="text-sm text-red-600 hover:text-red-500">Disable</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Sub-tab -->
            <div x-show="activeSubTab.features === 'advanced'" class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🚀 Advanced Features</h3>

                <div class="space-y-6">
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-4">API & Development</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">REST API</h5>
                                    <p class="text-sm text-gray-500">Enable REST API access</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Webhooks</h5>
                                    <p class="text-sm text-gray-500">Real-time event notifications</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Performance & Caching</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">Redis Caching</h5>
                                    <p class="text-sm text-gray-500">High-performance caching</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">CDN Integration</h5>
                                    <p class="text-sm text-gray-500">Content delivery network</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div> <!-- End Alpine.js context -->
</div>

<script>
// Global state management
const platformCustomization = {
    settings: {
        branding: {},
        appearance: {},
        layout: {},
        content: {},
        features: {}
    },

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.initializeColorPickers();
    },

    loadSettings() {
        // Load current settings from server
        fetch('/admin/platform-customization/settings')
            .then(response => response.json())
            .then(data => {
                this.settings = data;
                this.populateForm();
            })
            .catch(error => console.error('Error loading settings:', error));
    },

    populateForm() {
        // Populate form fields with current settings
        Object.keys(this.settings).forEach(section => {
            Object.keys(this.settings[section]).forEach(key => {
                const element = document.querySelector(`[name="${section}_${key}"]`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = this.settings[section][key];
                    } else {
                        element.value = this.settings[section][key];
                    }
                }
            });
        });
    },

    setupEventListeners() {
        // Auto-save on input changes
        document.addEventListener('input', (e) => {
            if (e.target.matches('[data-auto-save]')) {
                this.debounce(() => this.saveSection(e.target.dataset.section), 1000);
            }
        });

        // Color picker changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('input[type="color"]')) {
                this.updateColorPreview(e.target);
                this.generateCustomCSS();
            }
        });
    },

    initializeColorPickers() {
        // Initialize color pickers with current values
        const colorInputs = document.querySelectorAll('input[type="color"]');
        colorInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateColorPreview(input);
            });
        });
    },

    debounce(func, wait) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(func, wait);
    }
};

// Branding Functions
function uploadLogo(type = 'primary') {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = false;

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 2 * 1024 * 1024) { // 2MB limit
                showNotification('File size must be less than 2MB', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('logo', file);
            formData.append('type', type);

            showNotification('Uploading logo...', 'info');

            fetch('/admin/platform-customization/branding', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Logo uploaded successfully!', 'success');
                    updateLogoPreview(data.logo_url, type);
                } else {
                    showNotification(data.message || 'Upload failed', 'error');
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                showNotification('Upload failed. Please try again.', 'error');
            });
        }
    };

    input.click();
}

function uploadFavicon() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.ico,.png';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('favicon', file);

            fetch('/admin/platform-customization/branding', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Favicon uploaded successfully!', 'success');
                    updateFaviconPreview(data.favicon_url);
                } else {
                    showNotification(data.message || 'Upload failed', 'error');
                }
            });
        }
    };

    input.click();
}

function updateLogoPreview(url, type) {
    const previews = document.querySelectorAll(`.logo-preview-${type}`);
    previews.forEach(preview => {
        preview.src = url;
        preview.style.display = 'block';
    });
}

function updateFaviconPreview(url) {
    const faviconPreview = document.querySelector('.favicon-preview');
    if (faviconPreview) {
        faviconPreview.style.backgroundImage = `url(${url})`;
    }

    // Update actual favicon
    const favicon = document.querySelector('link[rel="icon"]');
    if (favicon) {
        favicon.href = url;
    }
}

function resetBranding() {
    if (confirm('Are you sure you want to reset all branding settings to default?')) {
        fetch('/admin/platform-customization/branding/reset', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Branding reset to default', 'success');
                location.reload();
            }
        });
    }
}

// Color and Appearance Functions
function updateColorScheme() {
    const colors = {
        primary: document.querySelector('#primary_color')?.value,
        secondary: document.querySelector('#secondary_color')?.value,
        accent: document.querySelector('#accent_color')?.value,
        background: document.querySelector('#background_color')?.value,
        text: document.querySelector('#text_color')?.value
    };

    fetch('/admin/platform-customization/colors', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(colors)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Color scheme updated!', 'success');
            generateCustomCSS();
        }
    });
}

function generateCustomCSS() {
    const colors = {
        primary: document.querySelector('#primary_color')?.value || '#4F46E5',
        secondary: document.querySelector('#secondary_color')?.value || '#10B981',
        accent: document.querySelector('#accent_color')?.value || '#F59E0B',
        background: document.querySelector('#background_color')?.value || '#FFFFFF',
        text: document.querySelector('#text_color')?.value || '#1F2937'
    };

    const css = `
        :root {
            --primary-color: ${colors.primary};
            --secondary-color: ${colors.secondary};
            --accent-color: ${colors.accent};
            --background-color: ${colors.background};
            --text-color: ${colors.text};
        }

        .bg-primary { background-color: var(--primary-color) !important; }
        .text-primary { color: var(--primary-color) !important; }
        .border-primary { border-color: var(--primary-color) !important; }
    `;

    // Apply CSS immediately for preview
    let styleElement = document.querySelector('#custom-theme-css');
    if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = 'custom-theme-css';
        document.head.appendChild(styleElement);
    }
    styleElement.textContent = css;
}

// Preview and Save Functions
function previewChanges() {
    const formData = new FormData(document.querySelector('#customization-form'));

    fetch('/admin/platform-customization/preview', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.open(data.preview_url, '_blank');
        }
    });
}

function saveAllSettings() {
    const formData = new FormData();

    // Collect all form data
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            formData.append(input.name, input.checked);
        } else if (input.type === 'file') {
            if (input.files.length > 0) {
                formData.append(input.name, input.files[0]);
            }
        } else {
            formData.append(input.name, input.value);
        }
    });

    showNotification('Saving settings...', 'info');

    fetch('/admin/platform-customization/save-all', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('All settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    })
    .catch(error => {
        console.error('Save error:', error);
        showNotification('Save failed. Please try again.', 'error');
    });
}

function exportSettings() {
    fetch('/admin/platform-customization/export')
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `platform-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            showNotification('Settings exported successfully!', 'success');
        });
}

function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('settings_file', file);

            fetch('/admin/platform-customization/import', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Settings imported successfully!', 'success');
                    location.reload();
                } else {
                    showNotification(data.message || 'Import failed', 'error');
                }
            });
        }
    };

    input.click();
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function updateColorPreview(input) {
    const color = input.value;
    const previewElements = document.querySelectorAll(`[data-color-preview="${input.name}"]`);
    previewElements.forEach(element => {
        element.style.backgroundColor = color;
    });
}

// Sub-menu specific functions
function saveBrandingSettings() {
    const formData = new FormData();

    // Collect branding form data
    const brandingInputs = document.querySelectorAll('[data-section="branding"]');
    brandingInputs.forEach(input => {
        if (input.type === 'checkbox') {
            formData.append(input.name, input.checked);
        } else if (input.type === 'file' && input.files.length > 0) {
            formData.append(input.name, input.files[0]);
        } else {
            formData.append(input.name, input.value);
        }
    });

    fetch('/admin/platform-customization/branding', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Branding settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function saveAppearanceSettings() {
    const formData = new FormData();

    // Collect appearance form data
    const appearanceInputs = document.querySelectorAll('[data-section="appearance"]');
    appearanceInputs.forEach(input => {
        if (input.type === 'checkbox') {
            formData.append(input.name, input.checked);
        } else {
            formData.append(input.name, input.value);
        }
    });

    fetch('/admin/platform-customization/appearance', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Appearance settings saved successfully!', 'success');
            generateCustomCSS();
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function saveLayoutSettings() {
    const formData = new FormData();

    // Collect layout form data
    const layoutInputs = document.querySelectorAll('[data-section="layout"]');
    layoutInputs.forEach(input => {
        if (input.type === 'checkbox') {
            formData.append(input.name, input.checked);
        } else {
            formData.append(input.name, input.value);
        }
    });

    fetch('/admin/platform-customization/layout', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Layout settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function saveContentSettings() {
    const formData = new FormData();

    // Collect content form data
    const contentInputs = document.querySelectorAll('[data-section="content"]');
    contentInputs.forEach(input => {
        if (input.type === 'checkbox') {
            formData.append(input.name, input.checked);
        } else {
            formData.append(input.name, input.value);
        }
    });

    fetch('/admin/platform-customization/content', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Content settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function saveFeaturesSettings() {
    const formData = new FormData();

    // Collect features form data
    const featuresInputs = document.querySelectorAll('[data-section="features"]');
    featuresInputs.forEach(input => {
        if (input.type === 'checkbox') {
            formData.append(input.name, input.checked);
        } else {
            formData.append(input.name, input.value);
        }
    });

    fetch('/admin/platform-customization/features', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Features settings saved successfully!', 'success');
        } else {
            showNotification(data.message || 'Save failed', 'error');
        }
    });
}

function previewTheme(themeName) {
    // Apply theme preview
    document.body.className = `theme-${themeName}`;
    showNotification(`Previewing ${themeName} theme`, 'info');

    // Save theme selection
    fetch('/admin/platform-customization/theme', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ theme: themeName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`${themeName} theme applied!`, 'success');
        }
    });
}

function resetToDefaults(section) {
    if (confirm(`Are you sure you want to reset ${section} settings to defaults?`)) {
        fetch(`/admin/platform-customization/${section}/reset`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${section} settings reset to defaults!`, 'success');
                location.reload();
            }
        });
    }
}

// Add missing showNotification function
window.showNotification = function(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    platformCustomization.init();
    generateCustomCSS();

    // Test notification on page load
    setTimeout(() => {
        showNotification('Platform Customization loaded successfully!', 'success');
    }, 1000);
});
</script>
@endsection