<?php

namespace Database\Seeders;

use App\Models\ProjectType;
use Illuminate\Database\Seeder;

class ProjectTypeSeeder extends Seeder
{
    public function run()
    {
        $projectTypes = [
            [
                'name' => 'Petition',
                'slug' => 'petition',
                'description' => 'Community-driven petitions requiring votes for approval',
                'icon' => 'clipboard-check',
                'color' => '#f59e0b',
                'features' => ['Community voting', 'Public visibility', 'Admin approval'],
                'min_votes_required' => 10,
                'requires_voting' => true,
                'allows_donations' => false,
                'allows_volunteers' => false,
                'allows_crowdfunding' => false,
                'has_stages' => false,
                'default_duration_days' => 30,
                'sort_order' => 1,
            ],
            [
                'name' => 'Crowdfund',
                'slug' => 'crowdfund',
                'description' => 'Fundraising campaigns for startups and business projects',
                'icon' => 'currency-dollar',
                'color' => '#10b981',
                'features' => ['Anonymous donations', 'Investment options', 'Escrow service', 'Progress tracking'],
                'min_votes_required' => 5,
                'requires_voting' => true,
                'allows_donations' => true,
                'allows_volunteers' => false,
                'allows_crowdfunding' => true,
                'has_stages' => true,
                'default_duration_days' => 60,
                'sort_order' => 2,
            ],
            [
                'name' => 'Volunteer',
                'slug' => 'volunteer',
                'description' => 'Community service projects requiring volunteer participation',
                'icon' => 'users',
                'color' => '#3b82f6',
                'features' => ['Volunteer recruitment', 'In-person activities', 'Team coordination', 'Impact tracking'],
                'min_votes_required' => 8,
                'requires_voting' => true,
                'allows_donations' => false,
                'allows_volunteers' => true,
                'allows_crowdfunding' => false,
                'has_stages' => true,
                'default_duration_days' => 45,
                'sort_order' => 3,
            ],
            [
                'name' => 'Environmental',
                'slug' => 'environmental',
                'description' => 'Environmental conservation and sustainability projects',
                'icon' => 'leaf',
                'color' => '#059669',
                'features' => ['Environmental impact', 'Sustainability focus', 'Community engagement', 'Progress monitoring'],
                'min_votes_required' => 12,
                'requires_voting' => true,
                'allows_donations' => true,
                'allows_volunteers' => true,
                'allows_crowdfunding' => true,
                'has_stages' => true,
                'default_duration_days' => 90,
                'sort_order' => 4,
            ],
            [
                'name' => 'Education',
                'slug' => 'education',
                'description' => 'Educational initiatives and learning programs',
                'icon' => 'academic-cap',
                'color' => '#7c3aed',
                'features' => ['Knowledge sharing', 'Skill development', 'Community learning', 'Resource creation'],
                'min_votes_required' => 6,
                'requires_voting' => true,
                'allows_donations' => true,
                'allows_volunteers' => true,
                'allows_crowdfunding' => false,
                'has_stages' => true,
                'default_duration_days' => 60,
                'sort_order' => 5,
            ],
            [
                'name' => 'Technology',
                'slug' => 'technology',
                'description' => 'Tech innovation and digital solution projects',
                'icon' => 'code',
                'color' => '#dc2626',
                'features' => ['Innovation focus', 'Digital solutions', 'Tech development', 'Open source'],
                'min_votes_required' => 15,
                'requires_voting' => true,
                'allows_donations' => true,
                'allows_volunteers' => true,
                'allows_crowdfunding' => true,
                'has_stages' => true,
                'default_duration_days' => 120,
                'sort_order' => 6,
            ],
            [
                'name' => 'Health & Wellness',
                'slug' => 'health-wellness',
                'description' => 'Health, wellness, and medical support projects',
                'icon' => 'heart',
                'color' => '#ec4899',
                'features' => ['Health focus', 'Community wellness', 'Medical support', 'Awareness campaigns'],
                'min_votes_required' => 10,
                'requires_voting' => true,
                'allows_donations' => true,
                'allows_volunteers' => true,
                'allows_crowdfunding' => true,
                'has_stages' => true,
                'default_duration_days' => 75,
                'sort_order' => 7,
            ],
            [
                'name' => 'Social Impact',
                'slug' => 'social-impact',
                'description' => 'Social justice and community improvement projects',
                'icon' => 'globe',
                'color' => '#f97316',
                'features' => ['Social justice', 'Community impact', 'Advocacy', 'Awareness building'],
                'min_votes_required' => 8,
                'requires_voting' => true,
                'allows_donations' => true,
                'allows_volunteers' => true,
                'allows_crowdfunding' => false,
                'has_stages' => true,
                'default_duration_days' => 60,
                'sort_order' => 8,
            ],
        ];

        foreach ($projectTypes as $type) {
            ProjectType::create($type);
        }
    }
}
