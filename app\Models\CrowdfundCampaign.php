<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CrowdfundCampaign extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'title', 'description', 'category', 'target_amount',
        'raised_amount', 'campaign_type', 'profit_percentage', 'duration_days',
        'start_date', 'end_date', 'status', 'requirements', 'images',
        'documents', 'admin_notes', 'approved_at', 'approved_by'
    ];

    protected $casts = [
        'target_amount' => 'decimal:2',
        'raised_amount' => 'decimal:2',
        'profit_percentage' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'requirements' => 'array',
        'images' => 'array',
        'documents' => 'array',
        'approved_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function contributions()
    {
        return $this->hasMany(CrowdfundContribution::class);
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function getProgressPercentageAttribute()
    {
        return $this->target_amount > 0 ? 
            round(($this->raised_amount / $this->target_amount) * 100, 1) : 0;
    }

    public function getDaysRemainingAttribute()
    {
        return max(0, now()->diffInDays($this->end_date, false));
    }

    public function getIsExpiredAttribute()
    {
        return now()->isAfter($this->end_date);
    }

    public function getContributorsCountAttribute()
    {
        return $this->contributions()->distinct('user_id')->count();
    }
}
