@extends('layouts.admin')

@section('title', 'System Maintenance')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">System Maintenance</h1>
        <p class="mt-2 text-gray-600">Manage system performance, clear caches, and perform maintenance tasks</p>
        @if(!auth()->user()->is_top_admin)
        <div class="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L10 11.414l2.707-2.707a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">
                        <strong>Access Denied:</strong> Only the Top Administrator can access maintenance features.
                    </p>
                </div>
            </div>
        </div>
        @endif
    </div>

    @if(auth()->user()->is_top_admin)
    <!-- System Information -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Cache Size</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $maintenanceInfo['cache_size'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Log Files</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $maintenanceInfo['log_files_count'] }} files ({{ $maintenanceInfo['log_files_size'] }})</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Storage Usage</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $maintenanceInfo['storage_usage'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm6 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Database Size</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $maintenanceInfo['database_size'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Cache Management -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Cache Management</h3>
                <p class="text-sm text-gray-500">Clear application caches to improve performance</p>
            </div>
            <div class="p-6 space-y-4">
                <form method="POST" action="{{ route('admin.maintenance.clear-cache') }}" class="inline">
                    @csrf
                    <input type="hidden" name="type" value="all">
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        Clear All Caches
                    </button>
                </form>
                
                <div class="grid grid-cols-2 gap-3">
                    <form method="POST" action="{{ route('admin.maintenance.clear-cache') }}" class="inline">
                        @csrf
                        <input type="hidden" name="type" value="application">
                        <button type="submit" class="w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            App Cache
                        </button>
                    </form>
                    
                    <form method="POST" action="{{ route('admin.maintenance.clear-cache') }}" class="inline">
                        @csrf
                        <input type="hidden" name="type" value="config">
                        <button type="submit" class="w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Config Cache
                        </button>
                    </form>
                    
                    <form method="POST" action="{{ route('admin.maintenance.clear-cache') }}" class="inline">
                        @csrf
                        <input type="hidden" name="type" value="route">
                        <button type="submit" class="w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Route Cache
                        </button>
                    </form>
                    
                    <form method="POST" action="{{ route('admin.maintenance.clear-cache') }}" class="inline">
                        @csrf
                        <input type="hidden" name="type" value="view">
                        <button type="submit" class="w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            View Cache
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Log Management -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Log Management</h3>
                <p class="text-sm text-gray-500">Clear error logs and debug information</p>
            </div>
            <div class="p-6 space-y-4">
                <form method="POST" action="{{ route('admin.maintenance.clear-logs') }}" class="inline">
                    @csrf
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                            onclick="return confirm('Are you sure you want to clear all log files? This action cannot be undone.')">
                        Clear All Log Files
                    </button>
                </form>
                
                <div class="text-sm text-gray-600">
                    <p><strong>Current Status:</strong></p>
                    <p>• {{ $maintenanceInfo['log_files_count'] }} log files</p>
                    <p>• Total size: {{ $maintenanceInfo['log_files_size'] }}</p>
                </div>
            </div>
        </div>

        <!-- System Optimization -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">System Optimization</h3>
                <p class="text-sm text-gray-500">Optimize application performance</p>
            </div>
            <div class="p-6 space-y-4">
                <form method="POST" action="{{ route('admin.maintenance.optimize') }}" class="inline">
                    @csrf
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Optimize Application
                    </button>
                </form>
                
                <div class="text-sm text-gray-600">
                    <p><strong>Optimization includes:</strong></p>
                    <p>• Clear all caches</p>
                    <p>• Cache configurations</p>
                    <p>• Cache routes and views</p>
                    <p>• Optimize autoloader</p>
                </div>
            </div>
        </div>

        <!-- Database Maintenance -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Database Maintenance</h3>
                <p class="text-sm text-gray-500">Database operations and optimization</p>
            </div>
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 gap-3">
                    <form method="POST" action="{{ route('admin.maintenance.database') }}" class="inline">
                        @csrf
                        <input type="hidden" name="action" value="migrate">
                        <button type="submit" class="w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                onclick="return confirm('Run database migrations? This will update the database schema.')">
                            Run Migrations
                        </button>
                    </form>
                    
                    <form method="POST" action="{{ route('admin.maintenance.database') }}" class="inline">
                        @csrf
                        <input type="hidden" name="action" value="optimize">
                        <button type="submit" class="w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Optimize Database
                        </button>
                    </form>
                </div>
                
                <div class="text-sm text-gray-600">
                    <p><strong>Database Size:</strong> {{ $maintenanceInfo['database_size'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">System Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Application</h4>
                    <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Laravel Version:</strong> {{ $maintenanceInfo['laravel_version'] }}</p>
                        <p><strong>PHP Version:</strong> {{ $maintenanceInfo['php_version'] }}</p>
                        <p><strong>Last Optimization:</strong> {{ $maintenanceInfo['last_optimization'] }}</p>
                    </div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Storage</h4>
                    <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Total Storage:</strong> {{ $maintenanceInfo['storage_usage'] }}</p>
                        <p><strong>Cache Size:</strong> {{ $maintenanceInfo['cache_size'] }}</p>
                        <p><strong>Log Files:</strong> {{ $maintenanceInfo['log_files_size'] }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
