<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MarketplaceProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'title',
        'slug',
        'description',
        'features',
        'type',
        'price',
        'escrow_fee_percentage',
        'quantity',
        'delivery_days',
        'images',
        'files',
        'status',
        'is_featured',
        'views',
        'rating',
        'total_sales',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'escrow_fee_percentage' => 'decimal:2',
        'rating' => 'decimal:2',
        'is_featured' => 'boolean',
        'images' => 'array',
        'files' => 'array',
    ];

    /**
     * Get the user that owns the product.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category.
     */
    public function category()
    {
        return $this->belongsTo(MarketplaceCategory::class);
    }

    /**
     * Get the orders for this product.
     */
    public function orders()
    {
        return $this->hasMany(MarketplaceOrder::class, 'product_id');
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute()
    {
        return match($this->status) {
            'draft' => 'gray',
            'pending' => 'yellow',
            'active' => 'green',
            'suspended' => 'red',
            'sold_out' => 'orange',
            default => 'gray',
        };
    }

    /**
     * Get formatted price.
     */
    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->price, 2);
    }

    /**
     * Check if product is available.
     */
    public function getIsAvailableAttribute()
    {
        return $this->status === 'active' &&
               ($this->quantity === null || $this->quantity > 0);
    }
}
