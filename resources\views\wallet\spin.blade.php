@extends('layouts.app')

@section('title', 'Lucky Spin - Win Amazing Prizes!')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">🎰 Lucky Spin</h1>
                <p class="mt-2 text-gray-600">Spin the wheel and win amazing prizes! Free spin once daily, then use points for extra spins.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('wallet.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Wallet
                </a>
            </div>
        </div>
    </div>

    <!-- Spin Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Free Spin Status -->
        <div class="bg-gradient-to-r from-green-400 to-green-600 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold">Free Daily Spin</h3>
                    <p class="text-green-100 text-sm">Available once per day</p>
                </div>
                <div class="text-3xl">🎁</div>
            </div>
            <div class="mt-4">
                @if($canFreeSpin)
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        ✅ Available Now
                    </span>
                @else
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        ⏰ Used Today
                    </span>
                    <p class="text-green-100 text-xs mt-1">Next free spin: {{ $nextFreeSpinTime }}</p>
                @endif
            </div>
        </div>

        <!-- Points Balance -->
        <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold">Your Points</h3>
                    <p class="text-yellow-100 text-sm">Available for extra spins</p>
                </div>
                <div class="text-3xl">💎</div>
            </div>
            <div class="mt-4">
                <span class="text-2xl font-bold">{{ number_format($userPoints) }}</span>
                <p class="text-yellow-100 text-xs">Cost per extra spin: {{ $spinCost }} points</p>
            </div>
        </div>

        <!-- Prize Progress -->
        <div class="bg-gradient-to-r from-purple-400 to-purple-600 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold">Prize Progress</h3>
                    <p class="text-purple-100 text-sm">Spins until next prize</p>
                </div>
                <div class="text-3xl">🏆</div>
            </div>
            <div class="mt-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm">{{ $spinsToWin - $currentSpinCount }} spins left</span>
                    <span class="text-sm">{{ $currentSpinCount }}/{{ $spinsToWin }}</span>
                </div>
                <div class="w-full bg-purple-200 rounded-full h-2">
                    <div class="bg-white h-2 rounded-full" style="width: {{ ($currentSpinCount / $spinsToWin) * 100 }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Spin Wheel Section -->
    <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
        <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">🎯 Spin the Wheel</h2>
            
            <!-- Spin Wheel -->
            <div class="relative mx-auto mb-8" style="width: 300px; height: 300px;">
                <div id="spinWheel" class="w-full h-full rounded-full border-8 border-gray-300 relative overflow-hidden transition-transform duration-3000 ease-out" style="background: conic-gradient(from 0deg, #ff6b6b 0deg 45deg, #4ecdc4 45deg 90deg, #45b7d1 90deg 135deg, #96ceb4 135deg 180deg, #ffeaa7 180deg 225deg, #dda0dd 225deg 270deg, #98d8c8 270deg 315deg, #f7dc6f 315deg 360deg);">
                    <!-- Wheel Segments -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="text-white font-bold text-lg">SPIN!</div>
                    </div>
                </div>
                <!-- Pointer -->
                <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                    <div class="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-red-500"></div>
                </div>
            </div>

            <!-- Spin Buttons -->
            <div class="flex justify-center space-x-4">
                @if($canFreeSpin)
                    <button onclick="spin('free')" class="inline-flex items-center px-8 py-4 bg-green-600 text-white text-lg font-bold rounded-lg hover:bg-green-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                        🎁 FREE SPIN
                    </button>
                @endif
                
                @if($userPoints >= $spinCost)
                    <button onclick="spin('paid')" class="inline-flex items-center px-8 py-4 bg-purple-600 text-white text-lg font-bold rounded-lg hover:bg-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                        💎 EXTRA SPIN ({{ $spinCost }} Points)
                    </button>
                @else
                    <button disabled class="inline-flex items-center px-8 py-4 bg-gray-400 text-white text-lg font-bold rounded-lg cursor-not-allowed">
                        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                        💎 EXTRA SPIN (Need {{ $spinCost - $userPoints }} more points)
                    </button>
                @endif
            </div>

            @if(!$canFreeSpin && $userPoints < $spinCost)
                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p class="text-yellow-800 text-sm">
                        <strong>No spins available!</strong> Come back tomorrow for your free daily spin or earn more points to spin again.
                    </p>
                    <a href="{{ route('points.index') }}" class="inline-flex items-center mt-2 text-yellow-600 hover:text-yellow-500 text-sm font-medium">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        Earn More Points
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Prize Information -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h3 class="text-xl font-bold text-gray-900 mb-4">🏆 Available Prizes</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            @foreach($availablePrizes as $prize)
                <div class="border border-gray-200 rounded-lg p-4 text-center">
                    <div class="text-3xl mb-2">{{ $prize['emoji'] }}</div>
                    <h4 class="font-semibold text-gray-900">{{ $prize['name'] }}</h4>
                    <p class="text-sm text-gray-600">{{ $prize['description'] }}</p>
                    <span class="inline-block mt-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        {{ $prize['delivery_method'] }}
                    </span>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Recent Spin History -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">📊 Recent Spins</h3>
        @if($recentSpins->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Result</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prize</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($recentSpins as $spin)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $spin->created_at->format('M d, Y H:i') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $spin->spin_type === 'free' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800' }}">
                                        {{ $spin->spin_type === 'free' ? '🎁 Free' : '💎 Paid' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $spin->is_winner ? '🎉 Winner!' : '😔 Try Again' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $spin->prize_name ?? 'No Prize' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $spin->prize_claimed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $spin->prize_claimed ? 'Claimed' : 'Pending' }}
                                    </span>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-8">
                <div class="text-gray-400 text-6xl mb-4">🎰</div>
                <p class="text-gray-500">No spins yet. Try your luck with a spin!</p>
            </div>
        @endif
    </div>
</div>

<!-- Spin Result Modal -->
<div id="spinResultModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div id="resultIcon" class="text-6xl mb-4"></div>
            <h3 id="resultTitle" class="text-lg font-bold text-gray-900 mb-2"></h3>
            <p id="resultMessage" class="text-sm text-gray-600 mb-4"></p>
            <div class="items-center px-4 py-3">
                <button onclick="closeModal()" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script src="{{ asset('js/spin-wheel.js') }}"></script>
@endsection
