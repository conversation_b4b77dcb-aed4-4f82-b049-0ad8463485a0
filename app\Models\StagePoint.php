<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StagePoint extends Model
{
    protected $fillable = [
        'stage_name',
        'point_type',
        'points_amount',
        'description',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    public static function getPointsForStage($stageName, $pointType)
    {
        return self::where('stage_name', $stageName)
                   ->where('point_type', $pointType)
                   ->where('is_active', true)
                   ->first();
    }
}
