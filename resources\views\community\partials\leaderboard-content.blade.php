<!-- Leaderboard Content -->
<div class="space-y-6">
    <!-- Top Contributors -->
    @if(isset($leaderboard) && $leaderboard->count() > 0)
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Top Contributors</h3>
            <p class="text-sm text-gray-600">Community members making the biggest impact</p>
        </div>
        
        <div class="divide-y divide-gray-200">
            @foreach($leaderboard as $member)
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if($member->rank <= 3)
                            <div class="w-10 h-10 rounded-full flex items-center justify-center
                                {{ $member->rank === 1 ? 'bg-yellow-100' : ($member->rank === 2 ? 'bg-gray-100' : 'bg-orange-100') }}">
                                <svg class="w-5 h-5 {{ $member->rank === 1 ? 'text-yellow-600' : ($member->rank === 2 ? 'text-gray-600' : 'text-orange-600') }}" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </div>
                            @else
                            <div class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-sm font-medium text-gray-700">#{{ $member->rank }}</span>
                            </div>
                            @endif
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-medium text-gray-900">{{ $member->name }}</h4>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span>{{ $member->projects_created }} projects</span>
                                <span>{{ $member->donations_made }} donations</span>
                                <span>{{ $member->volunteer_hours }}h volunteered</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-indigo-600">{{ number_format($member->points) }}</div>
                        <div class="text-sm text-gray-500">points</div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @else
    <!-- Empty State -->
    <div class="bg-white shadow rounded-lg p-12">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Mock Leaderboard Data</h3>
            <p class="mt-1 text-sm text-gray-500">Here you can see community rankings and achievements.</p>
        </div>
    </div>
    @endif

    <!-- Achievement Categories -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Top Project Creators</h3>
                    <p class="text-sm text-gray-500">Most innovative projects</p>
                </div>
            </div>
            <div class="mt-4 space-y-2">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">John Doe</span>
                    <span class="text-sm font-medium text-blue-600">5 projects</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">Jane Smith</span>
                    <span class="text-sm font-medium text-blue-600">3 projects</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">Mike Johnson</span>
                    <span class="text-sm font-medium text-blue-600">2 projects</span>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Top Donors</h3>
                    <p class="text-sm text-gray-500">Most generous supporters</p>
                </div>
            </div>
            <div class="mt-4 space-y-2">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">Sarah Wilson</span>
                    <span class="text-sm font-medium text-green-600">$2,500</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">David Chen</span>
                    <span class="text-sm font-medium text-green-600">$1,800</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">Maria Rodriguez</span>
                    <span class="text-sm font-medium text-green-600">$1,200</span>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Top Volunteers</h3>
                    <p class="text-sm text-gray-500">Most active helpers</p>
                </div>
            </div>
            <div class="mt-4 space-y-2">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">Alex Thompson</span>
                    <span class="text-sm font-medium text-purple-600">45 hours</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">Lisa Brown</span>
                    <span class="text-sm font-medium text-purple-600">32 hours</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-900">Tom Wilson</span>
                    <span class="text-sm font-medium text-purple-600">28 hours</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Challenge -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Challenge</h3>
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-xl font-bold">Sustainability Champion</h4>
                    <p class="text-indigo-100">Complete 3 environmental projects this month</p>
                </div>
                <div class="text-right">
                    <div class="text-3xl font-bold">7</div>
                    <div class="text-sm text-indigo-100">days left</div>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm mb-2">
                    <span>Progress</span>
                    <span>2/3 projects</span>
                </div>
                <div class="w-full bg-indigo-400 rounded-full h-2">
                    <div class="bg-white h-2 rounded-full" style="width: 66%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
