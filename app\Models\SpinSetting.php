<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SpinSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'spins_required_to_win',
        'spin_cost_points',
        'is_active',
        'prize_delivery_methods',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'prize_delivery_methods' => 'array',
    ];

    /**
     * Get the current spin settings
     */
    public static function current()
    {
        return static::first() ?? static::create([
            'spins_required_to_win' => 10,
            'spin_cost_points' => 100,
            'is_active' => true,
            'prize_delivery_methods' => ['events', 'cash', 'pickup_station', 'digital'],
        ]);
    }

    /**
     * Get spins required to win
     */
    public static function getSpinsRequiredToWin()
    {
        return static::current()->spins_required_to_win;
    }

    /**
     * Get spin cost in points
     */
    public static function getSpinCost()
    {
        return static::current()->spin_cost_points;
    }

    /**
     * Check if spin system is active
     */
    public static function isActive()
    {
        return static::current()->is_active;
    }

    /**
     * Get available delivery methods
     */
    public static function getDeliveryMethods()
    {
        return static::current()->prize_delivery_methods ?? [];
    }
}
