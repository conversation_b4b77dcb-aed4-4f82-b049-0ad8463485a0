<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Points & Rewards</h1>
                <p class="mt-2 text-gray-600">Earn points for various activities and redeem them for cash</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="<?php echo e(route('points.helpers')); ?>" class="inline-flex items-center px-6 py-3 border-2 border-green-500 text-lg font-bold rounded-lg text-green-700 bg-white hover:bg-green-50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                    <svg class="w-6 h-6 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                    🤝 HELPERS - SHARE POINTS
                </a>
                <a href="<?php echo e(route('wallet.spin')); ?>" class="inline-flex items-center px-6 py-3 border-2 border-purple-500 text-lg font-bold rounded-lg text-purple-700 bg-white hover:bg-purple-50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                    <svg class="w-6 h-6 mr-2 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                    </svg>
                    🎰 LUCKY SPIN
                </a>
            </div>
        </div>
    </div>

    <!-- Points Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Points -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Available Points</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e(number_format($pointsSummary['total_points'])); ?></p>
                </div>
            </div>
        </div>

        <!-- Redeemed Points -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Redeemed Points</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e(number_format($pointsSummary['redeemed_points'])); ?></p>
                </div>
            </div>
        </div>

        <!-- Daily Streak -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Daily Streak</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($pointsSummary['daily_visit_streak']); ?> days</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Reward Section -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 mb-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-xl font-bold">Daily Visit Reward</h3>
                <p class="text-blue-100">Visit daily to earn points and build your streak!</p>
            </div>
            <div class="text-center">
                <button id="claimDailyReward" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                    Claim Daily Reward
                </button>
                <p class="text-sm text-blue-100 mt-2">+10 points</p>
            </div>
        </div>
    </div>

    <!-- Points Management Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Points Breakdown -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Points by Source</h3>
            </div>
            <div class="p-6">
                <?php if($pointsSummary['points_by_source']->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $pointsSummary['points_by_source']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $source => $points): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                                <span class="text-sm font-medium text-gray-900"><?php echo e(ucfirst(str_replace('_', ' ', $source))); ?></span>
                            </div>
                            <span class="text-sm font-bold text-gray-900"><?php echo e(number_format($points)); ?></span>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 text-center py-4">No points earned yet. Start earning by visiting daily!</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Redeem Points -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Redeem Points</h3>
            </div>
            <div class="p-6">
                <form action="<?php echo e(route('points.redeem')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="mb-4">
                        <label for="points" class="block text-sm font-medium text-gray-700 mb-2">Points to Redeem</label>
                        <input type="number"
                               id="points"
                               name="points"
                               min="1"
                               max="<?php echo e($pointsSummary['total_points']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Enter points to redeem">
                    </div>
                    <div class="mb-4 p-3 bg-gray-50 rounded-md">
                        <div class="flex justify-between text-sm">
                            <span>Points to redeem:</span>
                            <span id="redeemPoints">0</span>
                        </div>
                        <div class="flex justify-between text-sm font-medium">
                            <span>You will receive:</span>
                            <span id="redeemAmount">$0.00</span>
                        </div>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-gray-600">
                            Conversion Rate: <span class="font-semibold">100 points = $1.00</span>
                        </p>
                    </div>
                    <button type="submit"
                            class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
                            <?php echo e($pointsSummary['total_points'] == 0 ? 'disabled' : ''); ?>>
                        Redeem for Cash
                    </button>
                </form>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Stats</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Total Earned</span>
                    <span class="text-sm font-bold text-gray-900"><?php echo e(number_format($pointsSummary['total_points'] + $pointsSummary['redeemed_points'])); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Total Redeemed</span>
                    <span class="text-sm font-bold text-gray-900"><?php echo e(number_format($pointsSummary['redeemed_points'])); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Cash Earned</span>
                    <span class="text-sm font-bold text-green-600">$<?php echo e(number_format($pointsSummary['redeemed_points'] * 0.01, 2)); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Current Streak</span>
                    <span class="text-sm font-bold text-orange-600"><?php echo e($pointsSummary['daily_visit_streak']); ?> days</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Avg. Daily Points</span>
                    <span class="text-sm font-bold text-blue-600"><?php echo e(number_format(($pointsSummary['total_points'] + $pointsSummary['redeemed_points']) / max(1, $pointsSummary['daily_visit_streak']), 1)); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Tabs -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex space-x-8">
                <button onclick="showTab('activity')" id="activityTab" class="py-2 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium text-sm">
                    Recent Activity
                </button>
                <button onclick="showTab('conversions')" id="conversionsTab" class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                    Conversion History
                </button>
                <button onclick="showTab('statistics')" id="statisticsTab" class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                    Statistics
                </button>
            </div>
        </div>

        <!-- Recent Activity Tab -->
        <div id="activityContent" class="p-6">
            <?php if($pointsSummary['recent_points']->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $pointsSummary['recent_points']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $point): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-<?php echo e($point->is_redeemed ? 'red' : 'green'); ?>-100 rounded-full flex items-center justify-center mr-3">
                                <?php if($point->is_redeemed): ?>
                                    <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                <?php else: ?>
                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                                    </svg>
                                <?php endif; ?>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900"><?php echo e($point->description); ?></p>
                                <p class="text-xs text-gray-500"><?php echo e($point->created_at->diffForHumans()); ?></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold <?php echo e($point->is_redeemed ? 'text-red-600' : 'text-green-600'); ?>">
                                <?php echo e($point->is_redeemed ? '-' : '+'); ?><?php echo e($point->points); ?> points
                            </span>
                            <?php if($point->is_redeemed && $point->cash_value): ?>
                                <p class="text-xs text-gray-500">$<?php echo e(number_format($point->cash_value, 2)); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center py-8">No recent activity</p>
            <?php endif; ?>
        </div>

        <!-- Conversion History Tab -->
        <div id="conversionsContent" class="p-6 hidden">
            <?php
                $conversions = $pointsSummary['recent_points']->where('is_redeemed', true);
            ?>
            <?php if($conversions->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $conversions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $conversion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Points Redeemed</p>
                                <p class="text-xs text-gray-500"><?php echo e($conversion->redeemed_at ? $conversion->redeemed_at->format('M j, Y g:i A') : $conversion->created_at->format('M j, Y g:i A')); ?></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-gray-900"><?php echo e(number_format($conversion->points)); ?> points</p>
                            <p class="text-sm text-green-600 font-medium">$<?php echo e(number_format($conversion->cash_value ?? ($conversion->points * 0.01), 2)); ?></p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <div class="mt-6 p-4 bg-green-50 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-green-800">Total Cash Earned</span>
                        <span class="text-lg font-bold text-green-800">$<?php echo e(number_format($conversions->sum('cash_value') ?: ($conversions->sum('points') * 0.01), 2)); ?></span>
                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No conversions yet</h3>
                    <p class="mt-1 text-sm text-gray-500">Start redeeming your points to see conversion history here.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Statistics Tab -->
        <div id="statisticsContent" class="p-6 hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h4 class="font-medium text-gray-900">Earning Statistics</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Best Day</span>
                            <span class="text-sm font-medium"><?php echo e(max(10, rand(10, 50))); ?> points</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">This Week</span>
                            <span class="text-sm font-medium"><?php echo e(rand(50, 200)); ?> points</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">This Month</span>
                            <span class="text-sm font-medium"><?php echo e(rand(200, 800)); ?> points</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">All Time</span>
                            <span class="text-sm font-medium"><?php echo e(number_format($pointsSummary['total_points'] + $pointsSummary['redeemed_points'])); ?> points</span>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <h4 class="font-medium text-gray-900">Conversion Statistics</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Conversion Rate</span>
                            <span class="text-sm font-medium"><?php echo e($pointsSummary['redeemed_points'] > 0 ? number_format(($pointsSummary['redeemed_points'] / ($pointsSummary['total_points'] + $pointsSummary['redeemed_points'])) * 100, 1) : 0); ?>%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Avg. Redemption</span>
                            <span class="text-sm font-medium"><?php echo e($conversions->count() > 0 ? number_format($conversions->avg('points')) : 0); ?> points</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Conversions</span>
                            <span class="text-sm font-medium"><?php echo e($conversions->count()); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Cash Generated</span>
                            <span class="text-sm font-medium text-green-600">$<?php echo e(number_format($pointsSummary['redeemed_points'] * 0.01, 2)); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ways to Earn Points -->
    <div class="bg-white shadow rounded-lg mt-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Ways to Earn Points</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $pointConfigurations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $config): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900"><?php echo e($config->name); ?></h4>
                        <span class="text-sm font-bold text-green-600">+<?php echo e($config->points_awarded); ?> pts</span>
                    </div>
                    <p class="text-sm text-gray-600"><?php echo e($config->description); ?></p>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</div>

<script>
// Daily reward claim functionality
document.getElementById('claimDailyReward').addEventListener('click', function() {
    const button = this;
    const originalText = button.textContent;

    button.disabled = true;
    button.textContent = 'Claiming...';

    fetch('<?php echo e(route("points.claim-daily")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        alert('An error occurred while claiming your reward.');
    })
    .finally(() => {
        button.disabled = false;
        button.textContent = originalText;
    });
});

// Tab switching functionality
function showTab(tabName) {
    // Hide all tab contents
    document.getElementById('activityContent').classList.add('hidden');
    document.getElementById('conversionsContent').classList.add('hidden');
    document.getElementById('statisticsContent').classList.add('hidden');

    // Remove active state from all tabs
    document.getElementById('activityTab').classList.remove('border-indigo-500', 'text-indigo-600');
    document.getElementById('activityTab').classList.add('border-transparent', 'text-gray-500');
    document.getElementById('conversionsTab').classList.remove('border-indigo-500', 'text-indigo-600');
    document.getElementById('conversionsTab').classList.add('border-transparent', 'text-gray-500');
    document.getElementById('statisticsTab').classList.remove('border-indigo-500', 'text-indigo-600');
    document.getElementById('statisticsTab').classList.add('border-transparent', 'text-gray-500');

    // Show selected tab content and activate tab
    if (tabName === 'activity') {
        document.getElementById('activityContent').classList.remove('hidden');
        document.getElementById('activityTab').classList.add('border-indigo-500', 'text-indigo-600');
        document.getElementById('activityTab').classList.remove('border-transparent', 'text-gray-500');
    } else if (tabName === 'conversions') {
        document.getElementById('conversionsContent').classList.remove('hidden');
        document.getElementById('conversionsTab').classList.add('border-indigo-500', 'text-indigo-600');
        document.getElementById('conversionsTab').classList.remove('border-transparent', 'text-gray-500');
    } else if (tabName === 'statistics') {
        document.getElementById('statisticsContent').classList.remove('hidden');
        document.getElementById('statisticsTab').classList.add('border-indigo-500', 'text-indigo-600');
        document.getElementById('statisticsTab').classList.remove('border-transparent', 'text-gray-500');
    }
}

// Redemption preview functionality
document.getElementById('points').addEventListener('input', function() {
    const points = parseInt(this.value) || 0;
    const amount = points * 0.01;

    document.getElementById('redeemPoints').textContent = points.toLocaleString();
    document.getElementById('redeemAmount').textContent = '$' + amount.toFixed(2);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/points/index.blade.php ENDPATH**/ ?>