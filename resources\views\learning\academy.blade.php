@extends('layouts.app')

@section('title', 'Academy')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 mb-8 text-white">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-4">🎓 Learning Academy</h1>
            <p class="text-xl text-blue-100">Expand your knowledge and skills with our comprehensive learning platform</p>
        </div>
    </div>

    <!-- Learning Paths -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Learning Paths</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white shadow rounded-lg p-6 border-l-4 border-green-500">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900">Environmental Fundamentals</h3>
                </div>
                <p class="text-gray-600 mb-4">Master the basics of environmental science, sustainability, and conservation.</p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">8 Courses • 24 Hours</span>
                    <a href="{{ url('/courses?path=environmental') }}" class="text-green-600 hover:text-green-500 font-medium">Start Learning →</a>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6 border-l-4 border-blue-500">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM9 7a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 000-2H9z"></path>
                            <path d="M7 14a5.971 5.971 0 00-.586-2.591A6.001 6.001 0 002 6a1 1 0 10-2 0 8.001 8.001 0 007.75 7.954 1 1 0 00.25.046h.186A8.013 8.013 0 0015 6a1 1 0 10-2 0 6.001 6.001 0 01-4.414 5.409A5.971 5.971 0 008 14H7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900">Community Leadership</h3>
                </div>
                <p class="text-gray-600 mb-4">Develop leadership skills to drive environmental change in your community.</p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">6 Courses • 18 Hours</span>
                    <a href="{{ url('/courses?path=leadership') }}" class="text-blue-600 hover:text-blue-500 font-medium">Start Learning →</a>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6 border-l-4 border-purple-500">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900">Project Management</h3>
                </div>
                <p class="text-gray-600 mb-4">Learn to plan, execute, and manage successful environmental projects.</p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">5 Courses • 15 Hours</span>
                    <a href="{{ url('/courses?path=project-management') }}" class="text-purple-600 hover:text-purple-500 font-medium">Start Learning →</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Courses -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold text-gray-900">Featured Courses</h2>
            <a href="{{ url('/courses') }}" class="text-indigo-600 hover:text-indigo-500 font-medium">View All Courses →</a>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-r from-green-400 to-blue-500"></div>
                <div class="p-6">
                    <div class="flex items-center mb-2">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Beginner</span>
                        <span class="ml-2 text-sm text-gray-500">4 hours</span>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Climate Change Fundamentals</h3>
                    <p class="text-gray-600 text-sm mb-4">Understanding the science behind climate change and its global impacts.</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">Dr. Sarah Wilson</span>
                        </div>
                        <button class="bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700">Enroll</button>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-r from-blue-400 to-purple-500"></div>
                <div class="p-6">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Intermediate</span>
                        <span class="ml-2 text-sm text-gray-500">6 hours</span>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Sustainable Business Practices</h3>
                    <p class="text-gray-600 text-sm mb-4">Implementing sustainability in business operations and strategy.</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">Michael Chen</span>
                        </div>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">Enroll</button>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-r from-purple-400 to-pink-500"></div>
                <div class="p-6">
                    <div class="flex items-center mb-2">
                        <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">Advanced</span>
                        <span class="ml-2 text-sm text-gray-500">8 hours</span>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Environmental Policy & Advocacy</h3>
                    <p class="text-gray-600 text-sm mb-4">Navigate policy frameworks and drive environmental advocacy.</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">Dr. Emily Rodriguez</span>
                        </div>
                        <button class="bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700">Enroll</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Learning Statistics -->
    <div class="bg-white shadow rounded-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Your Learning Progress</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-600">3</div>
                <div class="text-sm text-gray-600">Courses Completed</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-green-600">24</div>
                <div class="text-sm text-gray-600">Hours Learned</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-purple-600">2</div>
                <div class="text-sm text-gray-600">Certificates Earned</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-orange-600">85%</div>
                <div class="text-sm text-gray-600">Average Score</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-gradient-to-r from-green-500 to-blue-500 rounded-lg p-6 text-white">
            <h3 class="text-xl font-bold mb-2">📚 Browse All Courses</h3>
            <p class="text-green-100 mb-4">Explore our complete catalog of environmental and sustainability courses.</p>
            <a href="{{ url('/courses') }}" class="bg-white text-green-600 px-4 py-2 rounded font-medium hover:bg-green-50">Browse Courses</a>
        </div>
        
        <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white">
            <h3 class="text-xl font-bold mb-2">🎯 Take Assessment</h3>
            <p class="text-purple-100 mb-4">Test your knowledge and get personalized learning recommendations.</p>
            <a href="{{ url('/assessment') }}" class="bg-white text-purple-600 px-4 py-2 rounded font-medium hover:bg-purple-50">Start Assessment</a>
        </div>
    </div>
</div>
@endsection
