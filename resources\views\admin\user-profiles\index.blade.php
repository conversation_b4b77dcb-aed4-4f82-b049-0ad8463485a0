@extends('layouts.admin')

@section('title', 'User Profile Management System')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">User Profile Management System</h1>
                <p class="text-gray-600 mt-1">Manage user profiles, information requests, and privacy settings</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportProfileData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Data
                </button>
                <button onclick="bulkProfileUpdate()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Bulk Update
                </button>
            </div>
        </div>
    </div>

    <!-- Profile Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">8,947</h3>
                    <p class="text-sm text-gray-600">Complete Profiles</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">247</h3>
                    <p class="text-sm text-gray-600">Pending Requests</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">1,456</h3>
                    <p class="text-sm text-gray-600">Private Profiles</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
                        <path fill-rule="evenodd" d="M3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">3,900</h3>
                    <p class="text-sm text-gray-600">Profile Pictures</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'profiles' }">
                <button @click="activeTab = 'profiles'" :class="activeTab === 'profiles' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Profile Management
                </button>
                <button @click="activeTab = 'requests'" :class="activeTab === 'requests' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Information Requests
                </button>
                <button @click="activeTab = 'privacy'" :class="activeTab === 'privacy' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Privacy Settings
                </button>
                <button @click="activeTab = 'verification'" :class="activeTab === 'verification' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Profile Verification
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'profiles' }">
        <!-- Profile Management Tab -->
        <div x-show="activeTab === 'profiles'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">User Profile Management</h3>
                        <div class="flex items-center space-x-3">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">All Profiles</option>
                                <option value="complete">Complete Profiles</option>
                                <option value="incomplete">Incomplete Profiles</option>
                                <option value="private">Private Profiles</option>
                                <option value="public">Public Profiles</option>
                            </select>
                            <input type="text" placeholder="Search profiles..." class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profile Completion</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Privacy</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-600">JS</span>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">John Smith</div>
                                            <div class="text-sm text-gray-500"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">85%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Public
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">New York, USA</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-20</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button onclick="editProfile(1)" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                        <button onclick="viewProfile(1)" class="text-green-600 hover:text-green-900">View</button>
                                        <button onclick="togglePrivacy(1)" class="text-purple-600 hover:text-purple-900">Privacy</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Information Requests Tab -->
        <div x-show="activeTab === 'requests'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Profile Information Requests</h3>
                        <div class="flex items-center space-x-3">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">All Requests</option>
                                <option value="pending">Pending</option>
                                <option value="approved">Approved</option>
                                <option value="rejected">Rejected</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Pending Request -->
                        <div class="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-sm font-medium text-gray-600">AT</span>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Alex Thompson</h4>
                                        <p class="text-sm text-gray-600">Requesting access to John Smith's profile</p>
                                        <p class="text-xs text-gray-500">Requested 2 hours ago</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="approveRequest(1)" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700">
                                        Approve
                                    </button>
                                    <button onclick="rejectRequest(1)" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700">
                                        Reject
                                    </button>
                                    <button onclick="viewRequestDetails(1)" class="text-indigo-600 hover:text-indigo-900 text-sm">Details</button>
                                </div>
                            </div>
                        </div>

                        <!-- Approved Request -->
                        <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-sm font-medium text-gray-600">SJ</span>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Sarah Johnson</h4>
                                        <p class="text-sm text-gray-600">Access granted to Mike Davis's profile</p>
                                        <p class="text-xs text-gray-500">Approved 1 day ago</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                    <button onclick="revokeAccess(2)" class="text-red-600 hover:text-red-900 text-sm">Revoke</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Privacy Settings Tab -->
        <div x-show="activeTab === 'privacy'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Global Privacy Settings</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Privacy Controls -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900">Default Privacy Settings</h4>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Profile Visibility</label>
                                    <p class="text-xs text-gray-500">Default visibility for new profiles</p>
                                </div>
                                <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="public">Public</option>
                                    <option value="private">Private</option>
                                    <option value="members_only">Members Only</option>
                                </select>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Email Visibility</label>
                                    <p class="text-xs text-gray-500">Show email addresses in profiles</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Location Sharing</label>
                                    <p class="text-xs text-gray-500">Allow location information sharing</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>

                        <!-- Request Settings -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900">Request Management</h4>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Auto-approve Requests</label>
                                    <p class="text-xs text-gray-500">Automatically approve profile requests</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Request Expiry</label>
                                    <p class="text-xs text-gray-500">Days before requests expire</p>
                                </div>
                                <input type="number" value="30" class="border border-gray-300 rounded-md px-3 py-2 text-sm w-20">
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Notification Emails</label>
                                    <p class="text-xs text-gray-500">Email users about profile requests</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button onclick="savePrivacySettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportProfileData() {
    alert('Export profile data functionality will be implemented');
}

function bulkProfileUpdate() {
    alert('Bulk profile update functionality will be implemented');
}

function editProfile(id) {
    alert(`Edit profile ${id} functionality will be implemented`);
}

function viewProfile(id) {
    alert(`View profile ${id} functionality will be implemented`);
}

function togglePrivacy(id) {
    alert(`Toggle privacy for profile ${id} functionality will be implemented`);
}

function approveRequest(id) {
    if (confirm('Approve this profile information request?')) {
        alert(`Approve request ${id} functionality will be implemented`);
    }
}

function rejectRequest(id) {
    if (confirm('Reject this profile information request?')) {
        alert(`Reject request ${id} functionality will be implemented`);
    }
}

function viewRequestDetails(id) {
    alert(`View request details ${id} functionality will be implemented`);
}

function revokeAccess(id) {
    if (confirm('Revoke access for this approved request?')) {
        alert(`Revoke access ${id} functionality will be implemented`);
    }
}

function savePrivacySettings() {
    alert('Save privacy settings functionality will be implemented');
}
</script>
@endsection
