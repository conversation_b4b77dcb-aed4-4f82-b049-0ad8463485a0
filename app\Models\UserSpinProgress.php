<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class UserSpinProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'total_spins',
        'current_spin_count',
        'last_free_spin_date',
        'total_wins',
        'total_cash_won',
        'total_points_won',
    ];

    protected $casts = [
        'last_free_spin_date' => 'date',
        'total_cash_won' => 'decimal:2',
    ];

    /**
     * Get the user that owns the progress
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get or create progress for user
     */
    public static function getForUser($userId)
    {
        return static::firstOrCreate(
            ['user_id' => $userId],
            [
                'total_spins' => 0,
                'current_spin_count' => 0,
                'total_wins' => 0,
                'total_cash_won' => 0,
                'total_points_won' => 0,
            ]
        );
    }

    /**
     * Check if user can use free spin today
     */
    public function canUseFreeSpin()
    {
        if (!$this->last_free_spin_date) {
            return true;
        }

        return !$this->last_free_spin_date->isToday();
    }

    /**
     * Record a spin
     */
    public function recordSpin($spinType, $isWinner = false, $cashValue = 0, $pointsValue = 0)
    {
        $this->increment('total_spins');
        $this->increment('current_spin_count');

        if ($spinType === 'free') {
            $this->update(['last_free_spin_date' => today()]);
        }

        if ($isWinner) {
            $this->increment('total_wins');
            $this->increment('total_cash_won', $cashValue);
            $this->increment('total_points_won', $pointsValue);
            
            // Reset current spin count after a win
            $this->update(['current_spin_count' => 0]);
        }
    }

    /**
     * Get spins remaining until guaranteed win
     */
    public function getSpinsToWin()
    {
        $spinsRequired = SpinSetting::getSpinsRequiredToWin();
        return max(0, $spinsRequired - $this->current_spin_count);
    }

    /**
     * Check if user is due for a guaranteed win
     */
    public function isDueForWin()
    {
        return $this->current_spin_count >= SpinSetting::getSpinsRequiredToWin();
    }

    /**
     * Get next free spin time
     */
    public function getNextFreeSpinTime()
    {
        if (!$this->last_free_spin_date) {
            return 'Available now';
        }

        if ($this->canUseFreeSpin()) {
            return 'Available now';
        }

        return $this->last_free_spin_date->addDay()->format('M d, Y');
    }

    /**
     * Get win rate percentage
     */
    public function getWinRateAttribute()
    {
        if ($this->total_spins === 0) {
            return 0;
        }

        return round(($this->total_wins / $this->total_spins) * 100, 1);
    }
}
