<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('languages', function (Blueprint $table) {
            $table->id();
            $table->string('code', 5)->unique(); // en, es, fr, de, etc.
            $table->string('name');
            $table->string('native_name');
            $table->string('flag_emoji', 10)->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->boolean('is_rtl')->default(false);
            $table->timestamps();
        });

        // Insert default languages
        DB::table('languages')->insert([
            [
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English',
                'flag_emoji' => '🇺🇸',
                'is_active' => true,
                'is_default' => true,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'es',
                'name' => 'Spanish',
                'native_name' => 'Español',
                'flag_emoji' => '🇪🇸',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'fr',
                'name' => 'French',
                'native_name' => 'Français',
                'flag_emoji' => '🇫🇷',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'de',
                'name' => 'German',
                'native_name' => 'Deutsch',
                'flag_emoji' => '🇩🇪',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'it',
                'name' => 'Italian',
                'native_name' => 'Italiano',
                'flag_emoji' => '🇮🇹',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'pt',
                'name' => 'Portuguese',
                'native_name' => 'Português',
                'flag_emoji' => '🇵🇹',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'nl',
                'name' => 'Dutch',
                'native_name' => 'Nederlands',
                'flag_emoji' => '🇳🇱',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'zh',
                'name' => 'Chinese',
                'native_name' => '中文',
                'flag_emoji' => '🇨🇳',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'ja',
                'name' => 'Japanese',
                'native_name' => '日本語',
                'flag_emoji' => '🇯🇵',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'ar',
                'name' => 'Arabic',
                'native_name' => 'العربية',
                'flag_emoji' => '🇸🇦',
                'is_active' => true,
                'is_default' => false,
                'is_rtl' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('languages');
    }
};
