<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Wallet extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'currency_id',
        'balance',
        'pending_balance',
        'frozen_balance',
        'is_active',
    ];

    protected $casts = [
        'balance' => 'decimal:6',
        'pending_balance' => 'decimal:6',
        'frozen_balance' => 'decimal:6',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the wallet.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the currency for this wallet.
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Get wallet transactions.
     */
    public function transactions()
    {
        return $this->hasMany(WalletTransaction::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get available balance (balance - frozen).
     */
    public function getAvailableBalanceAttribute()
    {
        return $this->balance - $this->frozen_balance;
    }

    /**
     * Get total balance including pending.
     */
    public function getTotalBalanceAttribute()
    {
        return $this->balance + $this->pending_balance;
    }

    /**
     * Credit the wallet.
     */
    public function credit($amount, $description, $referenceType = null, $referenceId = null, $metadata = null)
    {
        $balanceBefore = $this->balance;
        $this->balance += $amount;
        $this->save();

        return $this->transactions()->create([
            'transaction_id' => $this->generateTransactionId(),
            'type' => 'credit',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->balance,
            'description' => $description,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'status' => 'completed',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Debit the wallet.
     */
    public function debit($amount, $description, $referenceType = null, $referenceId = null, $metadata = null)
    {
        if ($this->available_balance < $amount) {
            throw new \Exception('Insufficient balance');
        }

        $balanceBefore = $this->balance;
        $this->balance -= $amount;
        $this->save();

        return $this->transactions()->create([
            'transaction_id' => $this->generateTransactionId(),
            'type' => 'debit',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->balance,
            'description' => $description,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'status' => 'completed',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Add pending balance.
     */
    public function addPending($amount, $description, $referenceType = null, $referenceId = null)
    {
        $this->pending_balance += $amount;
        $this->save();

        return $this->transactions()->create([
            'transaction_id' => $this->generateTransactionId(),
            'type' => 'credit',
            'amount' => $amount,
            'balance_before' => $this->balance,
            'balance_after' => $this->balance,
            'description' => $description . ' (Pending)',
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'status' => 'pending',
        ]);
    }

    /**
     * Confirm pending balance.
     */
    public function confirmPending($amount)
    {
        if ($this->pending_balance < $amount) {
            throw new \Exception('Insufficient pending balance');
        }

        $this->pending_balance -= $amount;
        $this->balance += $amount;
        $this->save();
    }

    /**
     * Freeze balance.
     */
    public function freeze($amount, $description)
    {
        if ($this->available_balance < $amount) {
            throw new \Exception('Insufficient available balance');
        }

        $this->frozen_balance += $amount;
        $this->save();

        return $this->transactions()->create([
            'transaction_id' => $this->generateTransactionId(),
            'type' => 'debit',
            'amount' => $amount,
            'balance_before' => $this->balance,
            'balance_after' => $this->balance,
            'description' => $description . ' (Frozen)',
            'reference_type' => 'freeze',
            'status' => 'completed',
        ]);
    }

    /**
     * Unfreeze balance.
     */
    public function unfreeze($amount, $description)
    {
        if ($this->frozen_balance < $amount) {
            throw new \Exception('Insufficient frozen balance');
        }

        $this->frozen_balance -= $amount;
        $this->save();

        return $this->transactions()->create([
            'transaction_id' => $this->generateTransactionId(),
            'type' => 'credit',
            'amount' => $amount,
            'balance_before' => $this->balance,
            'balance_after' => $this->balance,
            'description' => $description . ' (Unfrozen)',
            'reference_type' => 'unfreeze',
            'status' => 'completed',
        ]);
    }

    /**
     * Get formatted balance.
     */
    public function getFormattedBalanceAttribute()
    {
        return $this->currency->formatAmount($this->balance);
    }

    /**
     * Get formatted available balance.
     */
    public function getFormattedAvailableBalanceAttribute()
    {
        return $this->currency->formatAmount($this->available_balance);
    }

    /**
     * Generate unique transaction ID.
     */
    private function generateTransactionId()
    {
        return 'TXN' . strtoupper(Str::random(10)) . time();
    }
}
