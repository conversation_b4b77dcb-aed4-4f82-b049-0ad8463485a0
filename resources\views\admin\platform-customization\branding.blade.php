@extends('layouts.admin')

@section('title', 'Branding Settings')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ route('admin.platform-customization.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                                </svg>
                                <span class="sr-only">Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Branding</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="text-3xl font-bold text-gray-900 mt-2">Branding Settings</h1>
                <p class="text-gray-600 mt-1">Customize your platform's brand identity, logos, and visual elements</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="resetBranding()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset to Default
                </button>
                <button onclick="saveBrandingSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Sub-navigation -->
    <div class="mb-8" x-data="{ activeSubTab: 'logo' }">
        <div class="flex space-x-1 bg-gray-50 p-1 rounded-lg">
            <button @click="activeSubTab = 'logo'" :class="activeSubTab === 'logo' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Logo & Assets</button>
            <button @click="activeSubTab = 'identity'" :class="activeSubTab === 'identity' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Brand Identity</button>
            <button @click="activeSubTab = 'social'" :class="activeSubTab === 'social' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">Social Media</button>
            <button @click="activeSubTab = 'seo'" :class="activeSubTab === 'seo' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-white'" class="px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">SEO Settings</button>
        </div>

        <!-- Logo & Assets Tab -->
        <div x-show="activeSubTab === 'logo'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🎨 Logo & Assets</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Primary Logo -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Primary Logo</h4>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div class="mb-4">
                                <img id="primary-logo-preview" src="#" alt="Primary Logo" class="mx-auto h-16 w-auto hidden">
                                <div id="primary-logo-placeholder" class="mx-auto h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <button onclick="uploadLogo('primary')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                                Upload Primary Logo
                            </button>
                            <p class="text-xs text-gray-500 mt-2">PNG, JPG up to 2MB. Recommended: 200x60px</p>
                        </div>
                    </div>

                    <!-- Secondary Logo -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Secondary Logo (Dark)</h4>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-900">
                            <div class="mb-4">
                                <img id="secondary-logo-preview" src="#" alt="Secondary Logo" class="mx-auto h-16 w-auto hidden">
                                <div id="secondary-logo-placeholder" class="mx-auto h-16 w-16 bg-gray-700 rounded-lg flex items-center justify-center">
                                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <button onclick="uploadLogo('secondary')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700">
                                Upload Dark Logo
                            </button>
                            <p class="text-xs text-gray-400 mt-2">For dark backgrounds</p>
                        </div>
                    </div>

                    <!-- Favicon -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Favicon</h4>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div class="mb-4">
                                <div id="favicon-preview" class="mx-auto h-8 w-8 bg-gray-200 rounded"></div>
                            </div>
                            <button onclick="uploadFavicon()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                                Upload Favicon
                            </button>
                            <p class="text-xs text-gray-500 mt-2">ICO, PNG 32x32px</p>
                        </div>
                    </div>

                    <!-- Brand Colors -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 mb-4">Brand Colors</h4>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <input type="color" id="primary_color" value="#4F46E5" class="h-10 w-16 rounded border border-gray-300">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Primary Color</label>
                                    <p class="text-xs text-gray-500">#4F46E5</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="color" id="secondary_color" value="#10B981" class="h-10 w-16 rounded border border-gray-300">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Secondary Color</label>
                                    <p class="text-xs text-gray-500">#10B981</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Brand Identity Tab -->
        <div x-show="activeSubTab === 'identity'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🏢 Brand Identity</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                            <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Your Company Name">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tagline</label>
                            <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Your company tagline">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Brand Description</label>
                            <textarea rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Describe your brand..."></textarea>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                            <input type="email" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="<EMAIL>">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Website URL</label>
                            <input type="url" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="https://yourwebsite.com">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Copyright Text</label>
                            <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="© 2024 Your Company. All rights reserved.">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Media Tab -->
        <div x-show="activeSubTab === 'social'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">📱 Social Media Links</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700">Twitter</label>
                                <input type="url" class="w-full border border-gray-300 rounded-md px-3 py-2 mt-1" placeholder="https://twitter.com/yourcompany">
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700">Facebook</label>
                                <input type="url" class="w-full border border-gray-300 rounded-md px-3 py-2 mt-1" placeholder="https://facebook.com/yourcompany">
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700">LinkedIn</label>
                                <input type="url" class="w-full border border-gray-300 rounded-md px-3 py-2 mt-1" placeholder="https://linkedin.com/company/yourcompany">
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700">Pinterest</label>
                                <input type="url" class="w-full border border-gray-300 rounded-md px-3 py-2 mt-1" placeholder="https://pinterest.com/yourcompany">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SEO Settings Tab -->
        <div x-show="activeSubTab === 'seo'" class="mt-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">🔍 SEO Settings</h3>
                
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                        <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Your Platform - Best Solution for...">
                        <p class="text-xs text-gray-500 mt-1">Recommended: 50-60 characters</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                        <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="Describe your platform in 150-160 characters..."></textarea>
                        <p class="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Keywords</label>
                        <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="keyword1, keyword2, keyword3">
                        <p class="text-xs text-gray-500 mt-1">Separate keywords with commas</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include external JavaScript -->
<script src="{{ asset('js/admin/platform-customization.js') }}"></script>
@endsection
