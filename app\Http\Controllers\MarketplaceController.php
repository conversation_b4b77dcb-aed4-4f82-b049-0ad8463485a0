<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MarketplaceProduct;
use App\Models\MarketplaceCategory;
use App\Models\MarketplaceOrder;
use App\Models\EscrowTransaction;
use App\Models\EarningHistory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class MarketplaceController extends Controller
{

    /**
     * Display marketplace homepage
     */
    public function index(Request $request)
    {
        $query = MarketplaceProduct::where('status', 'active')
            ->with(['user', 'category']);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Type filter
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Price range
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sorting
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('total_sales', 'desc');
                break;
            case 'rating':
                $query->orderBy('rating', 'desc');
                break;
            default:
                $query->latest();
        }

        $products = $query->paginate(12);
        $categories = MarketplaceCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Featured products
        $featuredProducts = MarketplaceProduct::where('status', 'active')
            ->where('is_featured', true)
            ->with(['user', 'category'])
            ->take(6)
            ->get();

        // Statistics
        $stats = [
            'total_products' => MarketplaceProduct::where('status', 'active')->count(),
            'total_vendors' => MarketplaceProduct::where('status', 'active')->distinct('user_id')->count(),
            'total_sales' => MarketplaceOrder::where('status', 'completed')->sum('total_amount'),
            'categories_count' => MarketplaceCategory::where('is_active', true)->count(),
        ];

        return view('marketplace.index', compact('products', 'categories', 'featuredProducts', 'stats'));
    }

    /**
     * Show specific product
     */
    public function show(MarketplaceProduct $product)
    {
        $product->increment('views');

        $product->load(['user', 'category']);

        // Related products
        $relatedProducts = MarketplaceProduct::where('status', 'active')
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->take(4)
            ->get();

        // Recent reviews/orders (you can implement reviews later)
        $recentOrders = MarketplaceOrder::where('product_id', $product->id)
            ->where('status', 'completed')
            ->with('buyer')
            ->latest()
            ->take(5)
            ->get();

        return view('marketplace.show', compact('product', 'relatedProducts', 'recentOrders'));
    }

    /**
     * Purchase product
     */
    public function purchase(Request $request, MarketplaceProduct $product)
    {
        $user = Auth::user();

        $request->validate([
            'quantity' => 'required|integer|min:1',
            'buyer_notes' => 'nullable|string|max:500',
        ]);

        $quantity = $request->quantity;

        // Check if product is available
        if ($product->status !== 'active') {
            return back()->with('error', 'This product is not available for purchase.');
        }

        // Check quantity for physical products
        if ($product->type === 'product' && $product->quantity !== null && $product->quantity < $quantity) {
            return back()->with('error', 'Insufficient quantity available.');
        }

        // Calculate amounts
        $unitPrice = $product->price;
        $totalAmount = $unitPrice * $quantity;
        $escrowAmount = $totalAmount;
        $platformFee = $totalAmount * ($product->escrow_fee_percentage / 100);

        // Check user balance
        if ($user->available_balance < $totalAmount) {
            return back()->with('error', 'Insufficient balance. Please add funds to your wallet.');
        }

        try {
            DB::beginTransaction();

            // Create order
            $order = MarketplaceOrder::create([
                'order_number' => 'MP-' . strtoupper(Str::random(8)),
                'buyer_id' => $user->id,
                'seller_id' => $product->user_id,
                'product_id' => $product->id,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_amount' => $totalAmount,
                'escrow_amount' => $escrowAmount,
                'platform_fee' => $platformFee,
                'status' => 'pending',
                'payment_status' => 'pending',
                'buyer_notes' => $request->buyer_notes,
                'delivery_deadline' => now()->addDays($product->delivery_days),
            ]);

            // Deduct from buyer's balance
            $user->decrement('available_balance', $totalAmount);

            // Create escrow transaction
            EscrowTransaction::create([
                'order_id' => $order->id,
                'amount' => $escrowAmount,
                'type' => 'deposit',
                'status' => 'completed',
                'description' => "Escrow deposit for order {$order->order_number}",
                'processed_at' => now(),
            ]);

            // Update product quantity if applicable
            if ($product->type === 'product' && $product->quantity !== null) {
                $product->decrement('quantity', $quantity);
                if ($product->quantity <= 0) {
                    $product->update(['status' => 'sold_out']);
                }
            }

            // Update order status
            $order->update([
                'status' => 'paid',
                'payment_status' => 'paid',
            ]);

            DB::commit();

            return redirect()->route('marketplace.orders.show', $order)
                ->with('success', 'Order placed successfully! Your payment is held in escrow until delivery.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to process order: ' . $e->getMessage());
        }
    }

    /**
     * Show user's orders
     */
    public function orders()
    {
        $user = Auth::user();

        $buyerOrders = $user->buyerOrders()
            ->with(['product', 'seller'])
            ->latest()
            ->paginate(10, ['*'], 'buyer_page');

        $sellerOrders = $user->sellerOrders()
            ->with(['product', 'buyer'])
            ->latest()
            ->paginate(10, ['*'], 'seller_page');

        return view('marketplace.orders', compact('buyerOrders', 'sellerOrders'));
    }

    /**
     * Show specific order
     */
    public function showOrder(MarketplaceOrder $order)
    {
        $user = Auth::user();

        // Check if user is buyer or seller
        if ($order->buyer_id !== $user->id && $order->seller_id !== $user->id) {
            abort(403, 'Unauthorized access to this order.');
        }

        $order->load(['product', 'buyer', 'seller', 'escrowTransactions']);

        return view('marketplace.order-details', compact('order'));
    }

    /**
     * Mark order as delivered (seller action)
     */
    public function markDelivered(Request $request, MarketplaceOrder $order)
    {
        $user = Auth::user();

        if ($order->seller_id !== $user->id) {
            abort(403, 'Only the seller can mark orders as delivered.');
        }

        if ($order->status !== 'paid' && $order->status !== 'in_progress') {
            return back()->with('error', 'Order cannot be marked as delivered at this time.');
        }

        $request->validate([
            'delivery_notes' => 'nullable|string|max:500',
            'delivery_files.*' => 'nullable|file|max:10240',
        ]);

        $deliveryData = [
            'notes' => $request->delivery_notes,
            'delivered_at' => now()->toISOString(),
        ];

        // Handle file uploads for digital products
        if ($request->hasFile('delivery_files')) {
            $files = [];
            foreach ($request->file('delivery_files') as $file) {
                $path = $file->store('marketplace-deliveries', 'private');
                $files[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                ];
            }
            $deliveryData['files'] = $files;
        }

        $order->update([
            'status' => 'delivered',
            'seller_notes' => $request->delivery_notes,
        ]);

        return back()->with('success', 'Order marked as delivered. Buyer has 7 days to confirm receipt.');
    }

    /**
     * Confirm receipt (buyer action)
     */
    public function confirmReceipt(MarketplaceOrder $order)
    {
        $user = Auth::user();

        if ($order->buyer_id !== $user->id) {
            abort(403, 'Only the buyer can confirm receipt.');
        }

        if ($order->status !== 'delivered') {
            return back()->with('error', 'Order must be delivered before confirming receipt.');
        }

        try {
            DB::beginTransaction();

            // Release escrow to seller
            $sellerAmount = $order->escrow_amount - $order->platform_fee;

            $order->seller->increment('available_balance', $sellerAmount);

            // Create escrow release transaction
            EscrowTransaction::create([
                'order_id' => $order->id,
                'amount' => $order->escrow_amount,
                'type' => 'release',
                'status' => 'completed',
                'description' => "Escrow release for order {$order->order_number}",
                'processed_at' => now(),
            ]);

            // Update order status
            $order->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            // Update product sales count
            $order->product->increment('total_sales');

            // Create earning history for seller
            EarningHistory::create([
                'user_id' => $order->seller_id,
                'type' => 'marketplace_sale',
                'amount' => $sellerAmount,
                'currency' => 'USD',
                'description' => "Marketplace sale: {$order->product->title}",
                'reference_type' => MarketplaceOrder::class,
                'reference_id' => $order->id,
                'status' => 'paid',
                'auto_pay' => true,
                'paid_at' => now(),
            ]);

            DB::commit();

            return back()->with('success', 'Receipt confirmed! Payment has been released to the seller.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to confirm receipt: ' . $e->getMessage());
        }
    }
}
