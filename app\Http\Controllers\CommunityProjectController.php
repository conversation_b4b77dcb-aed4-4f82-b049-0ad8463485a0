<?php

namespace App\Http\Controllers;

use App\Models\CommunityProject;
use App\Models\ProjectVote;
use App\Models\ProjectDonation;
use App\Models\ProjectVolunteer;
use App\Models\UserCredibilityScore;
use App\Models\UserStageActivation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CommunityProjectController extends Controller
{
    /**
     * Display the community projects dashboard.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $tab = $request->get('tab', 'dashboard');

        
        // Check if user has required stage activation for creating projects
        $canCreateProject = $this->canUserCreateProject($user);
        
        $query = CommunityProject::with(['user', 'votes', 'donations', 'volunteers']);
        
        // Filter by tab
        switch ($tab) {
            case 'dashboard':
                return $this->showDashboardTab($request);
            case 'projects':
                // Show projects overview with buttons - don't filter, just show the buttons page
                return $this->showProjectsTab($request);
            case 'petition':
                $query->petition();
                break;
            case 'featured':
                $query->featured();
                break;
            case 'closed-petition':
            case 'closed-featured':
                $query->closed();
                break;
            case 'discussions':
                return $this->showDiscussionsTab($request);
            case 'events':
                return $this->showEventsTab($request);
            case 'challenges':
                return $this->showChallengesTab($request);
            case 'leaderboard':
                return $this->showLeaderboardTab($request);
        }
        
        // Apply filters
        if ($request->filled('type')) {
            $query->byType($request->type);
        }
        
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }
        
        // Sort
        $sort = $request->get('sort', 'recent');
        switch ($sort) {
            case 'popular':
                $query->popular();
                break;
            case 'recent':
            default:
                $query->recent();
                break;
        }
        
        $projects = $query->paginate(12);
        
        // Get statistics
        $stats = [
            'petition_count' => CommunityProject::petition()->count(),
            'featured_count' => CommunityProject::featured()->count(),
            'closed_count' => CommunityProject::closed()->count(),
            'total_raised' => CommunityProject::sum('amount_raised'),
            'total_volunteers' => CommunityProject::sum('volunteer_count'),
        ];
        
        // Get project types for filter
        $projectTypes = [
            'environmentalist' => 'Environmentalist',
            'conservationist' => 'Conservationist',
            'stewards' => 'Stewards',
            'volunteers' => 'Volunteers',
            'philanthropy' => 'Philanthropy',
            'education' => 'Education',
            'health' => 'Health',
            'community_development' => 'Community Development',
            'technology' => 'Technology',
            'arts_culture' => 'Arts & Culture'
        ];
        
        // Handle different tabs
        if ($tab === 'crowdfund') {
            return $this->showCrowdfundTab($request);
        } elseif ($tab === 'discussions') {
            return $this->showDiscussionsTab($request);
        } elseif ($tab === 'events') {
            return $this->showEventsTab($request);
        } elseif ($tab === 'leaderboard') {
            return $this->showLeaderboardTab($request);
        }

        return view('community.index', compact(
            'projects',
            'stats',
            'tab',
            'projectTypes',
            'canCreateProject'
        ));
    }

    /**
     * Show the dashboard tab with combined statistics.
     */
    private function showDashboardTab(Request $request)
    {
        $tab = 'dashboard';

        // Combined statistics from all tabs
        $stats = [
            // Projects statistics
            'total_projects' => 35,
            'active_projects' => 28,
            'completed_projects' => 15,
            'total_raised' => 125000,
            'total_volunteers' => 456,
            'success_rate' => 78.5,

            // Crowdfund statistics
            'total_campaigns' => 24,
            'active_campaigns' => 18,
            'total_campaign_raised' => 89000,
            'total_contributors' => 234,

            // Discussions statistics
            'total_discussions' => 42,
            'active_discussions' => 25,
            'total_participants' => 189,
            'new_discussions_week' => 8,

            // Events statistics
            'upcoming_events' => 12,
            'total_attendees' => 342,
            'completed_events' => 28,
            'events_this_month' => 6,

            // Leaderboard statistics
            'total_members' => 1250,
            'active_contributors' => 340,
            'total_points_awarded' => 125000,
            'top_contributor_points' => 2450,

            // Overall community statistics
            'total_community_raised' => 214000, // Projects + Campaigns
            'total_community_members' => 1250,
            'total_activities' => 143, // Projects + Campaigns + Discussions + Events
            'community_growth_rate' => 15.2, // Monthly growth percentage
        ];

        // Recent community activity across all tabs
        $recentActivity = collect([
            (object) [
                'type' => 'project',
                'title' => 'New project: "Save Local Forest"',
                'description' => 'Community petition to protect 500 acres of forest',
                'user' => 'Sarah Wilson',
                'created_at' => now()->subHours(2),
                'icon' => 'project',
                'color' => 'blue'
            ],
            (object) [
                'type' => 'campaign',
                'title' => 'Campaign milestone reached',
                'description' => '"Green Energy Startup" reached 65% funding goal',
                'user' => 'John Doe',
                'created_at' => now()->subHours(4),
                'icon' => 'campaign',
                'color' => 'green'
            ],
            (object) [
                'type' => 'discussion',
                'title' => 'New discussion started',
                'description' => '"Community Garden Initiative" discussion',
                'user' => 'Jane Smith',
                'created_at' => now()->subHours(6),
                'icon' => 'discussion',
                'color' => 'purple'
            ],
            (object) [
                'type' => 'event',
                'title' => 'Event registration opened',
                'description' => '"Community Cleanup Day" - 45 attendees registered',
                'user' => 'Mike Johnson',
                'created_at' => now()->subHours(8),
                'icon' => 'event',
                'color' => 'orange'
            ],
            (object) [
                'type' => 'achievement',
                'title' => 'Community milestone',
                'description' => 'Total community raised surpassed $200,000',
                'user' => 'System',
                'created_at' => now()->subDay(),
                'icon' => 'achievement',
                'color' => 'yellow'
            ],
        ]);

        return view('community.index', compact('tab', 'stats', 'recentActivity'));
    }

    /**
     * Show the crowdfund tab.
     */
    private function showCrowdfundTab(Request $request)
    {
        $tab = 'crowdfund';

        // Mock crowdfunding campaigns data
        $campaigns = collect([
            (object) [
                'id' => 1,
                'title' => 'Green Energy Startup',
                'description' => 'Revolutionary solar panel technology for homes',
                'target_amount' => 50000,
                'raised_amount' => 32500,
                'campaign_type' => 'investment',
                'category' => 'technology',
                'days_remaining' => 15,
                'contributors_count' => 127,
                'progress_percentage' => 65,
                'user' => (object) ['name' => 'John Doe'],
                'created_at' => now()->subDays(10),
            ],
            (object) [
                'id' => 2,
                'title' => 'Community Garden Project',
                'description' => 'Building sustainable gardens in urban areas',
                'target_amount' => 25000,
                'raised_amount' => 18750,
                'campaign_type' => 'donation',
                'category' => 'environment',
                'days_remaining' => 22,
                'contributors_count' => 89,
                'progress_percentage' => 75,
                'user' => (object) ['name' => 'Jane Smith'],
                'created_at' => now()->subDays(5),
            ],
            (object) [
                'id' => 3,
                'title' => 'Educational App Development',
                'description' => 'Mobile app for environmental education',
                'target_amount' => 35000,
                'raised_amount' => 12250,
                'campaign_type' => 'loan',
                'category' => 'education',
                'days_remaining' => 30,
                'contributors_count' => 45,
                'progress_percentage' => 35,
                'user' => (object) ['name' => 'Mike Johnson'],
                'created_at' => now()->subDays(3),
            ],
        ]);

        $stats = [
            'total_campaigns' => 24,
            'active_campaigns' => 18,
            'total_raised' => 125000,
            'total_contributors' => 456,
        ];

        return view('community.index', compact('tab', 'campaigns', 'stats'));
    }

    /**
     * Show the discussions tab.
     */
    private function showDiscussionsTab(Request $request)
    {
        $tab = 'discussions';

        // Mock discussions data - replace with actual implementation
        $discussions = collect([
            (object) [
                'id' => 1,
                'title' => 'Community Garden Initiative',
                'author' => 'John Doe',
                'replies' => 15,
                'created_at' => now()->subHours(2),
                'last_activity' => now()->subMinutes(30),
            ],
            (object) [
                'id' => 2,
                'title' => 'Renewable Energy Discussion',
                'author' => 'Jane Smith',
                'replies' => 8,
                'created_at' => now()->subDay(),
                'last_activity' => now()->subHours(3),
            ],
        ]);

        $stats = [
            'total_discussions' => 25,
            'active_discussions' => 12,
            'total_participants' => 150,
            'new_this_week' => 5,
        ];

        return view('community.index', compact('tab', 'discussions', 'stats'));
    }

    /**
     * Show the events tab.
     */
    private function showEventsTab(Request $request)
    {
        $tab = 'events';

        // Mock events data - replace with actual implementation
        $events = collect([
            (object) [
                'id' => 1,
                'title' => 'Community Cleanup Day',
                'date' => now()->addWeek(),
                'location' => 'Central Park',
                'attendees' => 45,
                'max_attendees' => 100,
            ],
            (object) [
                'id' => 2,
                'title' => 'Environmental Workshop',
                'date' => now()->addWeeks(2),
                'location' => 'Community Center',
                'attendees' => 23,
                'max_attendees' => 50,
            ],
        ]);

        $stats = [
            'upcoming_events' => 8,
            'total_attendees' => 234,
            'events_this_month' => 12,
            'completed_events' => 45,
        ];

        return view('community.index', compact('tab', 'events', 'stats'));
    }

    /**
     * Show the leaderboard tab.
     */
    private function showLeaderboardTab(Request $request)
    {
        $tab = 'leaderboard';

        // Mock leaderboard data - replace with actual implementation
        $leaderboard = collect([
            (object) [
                'rank' => 1,
                'name' => 'John Doe',
                'points' => 2450,
                'projects_created' => 5,
                'donations_made' => 12,
                'volunteer_hours' => 45,
            ],
            (object) [
                'rank' => 2,
                'name' => 'Jane Smith',
                'points' => 2100,
                'projects_created' => 3,
                'donations_made' => 18,
                'volunteer_hours' => 32,
            ],
        ]);

        $stats = [
            'total_members' => 1250,
            'active_contributors' => 340,
            'total_points_awarded' => 125000,
            'top_contributor_points' => 2450,
        ];

        return view('community.index', compact('tab', 'leaderboard', 'stats'));
    }

    /**
     * Show the challenges tab.
     */
    private function showChallengesTab(Request $request)
    {
        $tab = 'challenges';

        // Challenge statistics
        $stats = [
            'active_challenges' => 12,
            'total_participants' => 456,
            'completed_challenges' => 28,
            'total_points_awarded' => 15000,
            'success_rate' => 85.2,
            'avg_participants' => 38
        ];

        // Active challenges data
        $activeChallenges = collect([
            (object) [
                'id' => 1,
                'title' => '30-Day Plastic Free Challenge',
                'description' => 'Eliminate single-use plastics from your daily routine for 30 days.',
                'participants' => 245,
                'points_reward' => 50,
                'days_left' => 15,
                'progress' => 65,
                'status' => 'active',
                'category' => 'waste_reduction'
            ],
            (object) [
                'id' => 2,
                'title' => 'Energy Saving Week',
                'description' => 'Reduce your energy consumption by 20% for one week.',
                'participants' => 189,
                'points_reward' => 30,
                'days_left' => 8,
                'progress' => 45,
                'status' => 'active',
                'category' => 'energy'
            ],
            (object) [
                'id' => 3,
                'title' => 'Community Garden Project',
                'description' => 'Help establish a community garden in your neighborhood.',
                'participants' => 67,
                'points_reward' => 100,
                'days_left' => -3,
                'progress' => 25,
                'status' => 'starting_soon',
                'category' => 'community'
            ]
        ]);

        return view('community.index', compact('tab', 'stats', 'activeChallenges'));
    }

    /**
     * Display the projects overview page with buttons.
     */
    public function projects()
    {
        $user = Auth::user();

        // Get statistics for all project types
        $stats = [
            'petition_count' => CommunityProject::petition()->count(),
            'featured_count' => CommunityProject::featured()->count(),
            'closed_petition_count' => CommunityProject::petition()->closed()->count(),
            'closed_featured_count' => CommunityProject::featured()->closed()->count(),
            'total_raised' => CommunityProject::sum('amount_raised'),
            'total_volunteers' => CommunityProject::sum('volunteer_count'),
            'total_projects' => CommunityProject::count(),
            'success_rate' => CommunityProject::count() > 0 ?
                round((CommunityProject::closed()->count() / CommunityProject::count()) * 100, 1) : 0,
        ];

        // Get recent activity (last 10 activities)
        $recentActivity = collect([
            (object) [
                'description' => 'New petition project created: "Save Local Forest"',
                'created_at' => now()->subHours(2),
            ],
            (object) [
                'description' => 'Featured project "Clean Ocean Initiative" reached funding goal',
                'created_at' => now()->subHours(5),
            ],
            (object) [
                'description' => 'Petition "Renewable Energy for Schools" approved',
                'created_at' => now()->subDay(),
            ],
        ]);

        return view('community.projects', compact('stats', 'recentActivity'));
    }

    /**
     * Show the projects tab with mock data.
     */
    private function showProjectsTab(Request $request)
    {
        $user = Auth::user();
        $tab = 'projects';

        // Check if user has required stage activation for creating projects
        $canCreateProject = $this->canUserCreateProject($user);

        // Mock projects data
        $projects = collect([
            (object) [
                'id' => 1,
                'title' => 'Save Local Forest Initiative',
                'description' => 'Petition to protect 500 acres of local forest from development',
                'project_type' => 'petition',
                'status' => 'voting',
                'votes_count' => 87,
                'votes_required' => 100,
                'voting_progress' => 87,
                'days_remaining' => 12,
                'user' => (object) ['name' => 'Sarah Wilson'],
                'created_at' => now()->subDays(8),
            ],
            (object) [
                'id' => 2,
                'title' => 'Clean Ocean Initiative',
                'description' => 'Community project to clean local beaches and waterways',
                'project_type' => 'featured',
                'status' => 'featured',
                'amount_needed' => 15000,
                'amount_raised' => 12750,
                'funding_progress' => 85,
                'volunteer_count' => 34,
                'days_remaining' => 18,
                'user' => (object) ['name' => 'David Chen'],
                'created_at' => now()->subDays(15),
            ],
            (object) [
                'id' => 3,
                'title' => 'Renewable Energy for Schools',
                'description' => 'Installing solar panels in local schools',
                'project_type' => 'petition',
                'status' => 'approved',
                'votes_count' => 156,
                'votes_required' => 100,
                'voting_progress' => 100,
                'days_remaining' => 0,
                'user' => (object) ['name' => 'Maria Rodriguez'],
                'created_at' => now()->subDays(25),
            ],
        ]);

        // Mock statistics
        $stats = [
            'petition_count' => 12,
            'featured_count' => 8,
            'closed_count' => 15,
            'total_raised' => 85000,
            'total_volunteers' => 234,
            'total_projects' => 35,
            'success_rate' => 78.5,
        ];

        // Mock recent activity
        $recentActivity = collect([
            (object) [
                'description' => 'New petition project created: "Save Local Forest"',
                'created_at' => now()->subHours(2),
            ],
            (object) [
                'description' => 'Featured project "Clean Ocean Initiative" reached 85% funding',
                'created_at' => now()->subHours(5),
            ],
            (object) [
                'description' => 'Petition "Renewable Energy for Schools" approved',
                'created_at' => now()->subDay(),
            ],
        ]);

        return view('community.index', compact(
            'projects',
            'stats',
            'tab',
            'canCreateProject',
            'recentActivity'
        ));
    }

    /**
     * Show the form for creating a new project.
     */
    public function create()
    {
        $user = Auth::user();
        
        if (!$this->canUserCreateProject($user)) {
            return redirect()->route('community.index')
                ->with('error', 'You need to have an activated/approved action stage to create projects.');
        }
        
        $projectTypes = [
            'environmentalist' => 'Environmentalist',
            'conservationist' => 'Conservationist',
            'stewards' => 'Stewards',
            'volunteers' => 'Volunteers',
            'philanthropy' => 'Philanthropy',
            'education' => 'Education',
            'health' => 'Health',
            'community_development' => 'Community Development',
            'technology' => 'Technology',
            'arts_culture' => 'Arts & Culture'
        ];
        
        return view('community.create', compact('projectTypes'));
    }

    /**
     * Store a newly created project.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        if (!$this->canUserCreateProject($user)) {
            return redirect()->route('community.index')
                ->with('error', 'You need to have an activated/approved action stage to create projects.');
        }
        
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'required|string',
            'amount_needed' => 'required|numeric|min:0',
            'project_type' => 'required|in:environmentalist,conservationist,stewards,volunteers,philanthropy,education,health,community_development,technology,arts_culture',
            'project_duration_days' => 'required|integer|min:1|max:365',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'videos.*' => 'mimes:mp4,avi,mov,wmv|max:10240',
            'documents.*' => 'mimes:pdf,doc,docx|max:5120'
        ]);
        
        // Get admin settings for voting requirements and duration
        $votingDurationDays = 30; // Default, should be configurable by admin
        $votesRequired = 10; // Default, should be configurable by admin
        
        $project = new CommunityProject();
        $project->user_id = $user->id;
        $project->title = $request->title;
        $project->description = $request->description;
        $project->requirements = $request->requirements;
        $project->amount_needed = $request->amount_needed;
        $project->project_type = $request->project_type;
        $project->project_duration_days = $request->project_duration_days;
        $project->votes_required = $votesRequired;
        $project->voting_deadline = Carbon::now()->addDays($votingDurationDays);
        
        // Handle file uploads
        $project->images = $this->handleFileUploads($request, 'images', 'community/images');
        $project->videos = $this->handleFileUploads($request, 'videos', 'community/videos');
        $project->documents = $this->handleFileUploads($request, 'documents', 'community/documents');
        
        $project->save();
        
        // Update user credibility score
        $this->updateUserCredibilityScore($user, 'project_created');
        
        return redirect()->route('community.show', $project)
            ->with('success', 'Project petition created successfully! Voting period has started.');
    }

    /**
     * Display the specified project.
     */
    public function show(CommunityProject $project)
    {
        $user = Auth::user();
        
        $project->load([
            'user', 
            'votes.user', 
            'donations' => function($query) {
                $query->completed()->orderBy('created_at', 'desc');
            },
            'volunteers.user', 
            'stages', 
            'teamCredits'
        ]);
        
        $userVote = $project->votes()->where('user_id', $user->id)->first();
        $userDonation = $project->donations()->where('user_id', $user->id)->completed()->sum('amount');
        $userVolunteer = $project->volunteers()->where('user_id', $user->id)->first();
        
        $canVote = $project->canUserVote($user);
        $canDonate = $project->canUserDonate($user);
        $canVolunteer = $project->canUserVolunteer($user);
        
        return view('community.show', compact(
            'project', 
            'userVote', 
            'userDonation', 
            'userVolunteer',
            'canVote', 
            'canDonate', 
            'canVolunteer'
        ));
    }

    /**
     * Vote for a project.
     */
    public function vote(Request $request, CommunityProject $project)
    {
        $user = Auth::user();

        // Check if user is the project creator
        if ($project->user_id === $user->id) {
            return back()->with('error', 'You cannot vote on your own project.');
        }

        if (!$project->canUserVote($user)) {
            return back()->with('error', 'You cannot vote for this project.');
        }

        $request->validate([
            'vote_type' => 'required|in:support,against',
            'comment' => 'nullable|string|max:500'
        ]);

        ProjectVote::create([
            'project_id' => $project->id,
            'user_id' => $user->id,
            'vote_type' => $request->vote_type,
            'comment' => $request->comment
        ]);

        $project->updateVotesCount();

        // Update user credibility score
        $this->updateUserCredibilityScore($user, 'vote_cast');

        // Check if project meets voting requirements
        if ($project->votes_count >= $project->votes_required) {
            // Notify admin for approval
            // TODO: Implement admin notification
        }

        return back()->with('success', 'Your vote has been recorded!');
    }

    /**
     * Check if user can create projects.
     */
    private function canUserCreateProject($user): bool
    {
        // Check if user has activated/approved action stage
        return UserStageActivation::where('user_id', $user->id)
            ->whereHas('membershipStage', function($query) {
                $query->where('name', 'like', '%action%');
            })
            ->where('approval_status', 'approved')
            ->exists();
    }

    /**
     * Handle file uploads.
     */
    private function handleFileUploads(Request $request, string $field, string $path): ?array
    {
        if (!$request->hasFile($field)) {
            return null;
        }
        
        $files = [];
        foreach ($request->file($field) as $file) {
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs($path, $filename, 'public');
            $files[] = $filePath;
        }
        
        return $files;
    }

    /**
     * Update user credibility score.
     */
    private function updateUserCredibilityScore($user, string $action, $value = null): void
    {
        $credibility = UserCredibilityScore::firstOrCreate(['user_id' => $user->id]);

        switch ($action) {
            case 'project_created':
                $credibility->incrementProjectsCreated();
                break;
            case 'project_completed':
                $credibility->incrementProjectsCompleted();
                break;
            case 'project_approved':
                $credibility->incrementProjectsApproved();
                break;
            case 'vote_cast':
                $credibility->incrementVotesCast();
                break;
            case 'donation_made':
                if ($value) {
                    $credibility->addDonation($value);
                }
                break;
            case 'volunteer_hours':
                if ($value) {
                    $credibility->addVolunteerHours($value);
                }
                break;
        }
    }

    /**
     * Donate to a project.
     */
    public function donate(Request $request, CommunityProject $project)
    {
        $user = Auth::user();

        if (!$project->canUserDonate($user)) {
            return back()->with('error', 'You cannot donate to this project.');
        }

        $request->validate([
            'amount' => 'required|numeric|min:1',
            'is_anonymous' => 'boolean',
            'donor_name' => 'nullable|string|max:255',
            'message' => 'nullable|string|max:500'
        ]);

        $amount = $request->amount;

        // Check if user has sufficient balance
        if ($user->available_balance < $amount) {
            return back()->with('error', 'Insufficient wallet balance. Please add funds to your wallet.');
        }

        // Deduct from user's wallet
        $user->available_balance -= $amount;
        $user->save();

        // Create donation record
        $donation = ProjectDonation::create([
            'project_id' => $project->id,
            'user_id' => $user->id,
            'amount' => $amount,
            'is_anonymous' => $request->boolean('is_anonymous'),
            'donor_name' => $request->donor_name ?: $user->name,
            'message' => $request->message,
            'status' => 'completed'
        ]);

        // Create wallet transaction record
        \App\Models\WalletTransaction::create([
            'user_id' => $user->id,
            'type' => 'debit',
            'amount' => $amount,
            'currency_code' => 'USD', // Default currency
            'description' => "Donation to project: {$project->title}",
            'reference_type' => 'project_donation',
            'reference_id' => $donation->id,
            'status' => 'completed'
        ]);

        $project->updateAmountRaised();

        // Update user credibility score
        $this->updateUserCredibilityScore($user, 'donation_made', $amount);

        return back()->with('success', 'Thank you for your donation! Amount has been deducted from your wallet.');
    }

    /**
     * Volunteer for a project.
     */
    public function volunteer(Request $request, CommunityProject $project)
    {
        $user = Auth::user();

        if (!$project->canUserVolunteer($user)) {
            return back()->with('error', 'You cannot volunteer for this project.');
        }

        $request->validate([
            'skills_offered' => 'required|string',
            'availability' => 'required|string',
            'message' => 'nullable|string|max:500'
        ]);

        ProjectVolunteer::create([
            'project_id' => $project->id,
            'user_id' => $user->id,
            'skills_offered' => $request->skills_offered,
            'availability' => $request->availability,
            'message' => $request->message
        ]);

        return back()->with('success', 'Your volunteer application has been submitted!');
    }

    /**
     * Delete a project (only by creator if denied).
     */
    public function destroy(CommunityProject $project)
    {
        $user = Auth::user();

        if ($project->user_id !== $user->id) {
            return back()->with('error', 'You can only delete your own projects.');
        }

        if ($project->status !== 'denied') {
            return back()->with('error', 'You can only delete denied projects.');
        }

        // Delete associated files
        $this->deleteProjectFiles($project);

        $project->delete();

        return redirect()->route('community.index')
            ->with('success', 'Project deleted successfully.');
    }

    /**
     * Delete project files.
     */
    private function deleteProjectFiles(CommunityProject $project): void
    {
        $fileFields = ['images', 'videos', 'documents'];

        foreach ($fileFields as $field) {
            if ($project->$field) {
                foreach ($project->$field as $file) {
                    Storage::disk('public')->delete($file);
                }
            }
        }
    }

    /**
     * Get widget data for homepage.
     */
    public function getWidgetData()
    {
        $topProjects = [
            'petition' => CommunityProject::petition()
                ->popular()
                ->with('user')
                ->take(10)
                ->get(),
            'featured' => CommunityProject::featured()
                ->orderBy('amount_raised', 'desc')
                ->with('user')
                ->take(10)
                ->get(),
            'closed' => CommunityProject::closed()
                ->orderBy('credibility_score', 'desc')
                ->with('user')
                ->take(10)
                ->get()
        ];

        return $topProjects;
    }

    /**
     * Show community discussions.
     */
    private function showDiscussions(Request $request)
    {
        $user = Auth::user();

        // For now, show a placeholder view
        // In the future, this could integrate with a forum system
        $discussions = collect([
            (object) [
                'id' => 1,
                'title' => 'Best practices for sustainable living',
                'author' => 'Community Admin',
                'replies' => 23,
                'views' => 156,
                'last_activity' => now()->subHours(2),
                'category' => 'General Discussion'
            ],
            (object) [
                'id' => 2,
                'title' => 'How to reduce plastic waste in daily life',
                'author' => 'EcoWarrior',
                'replies' => 15,
                'views' => 89,
                'last_activity' => now()->subHours(5),
                'category' => 'Environmental Tips'
            ],
            (object) [
                'id' => 3,
                'title' => 'Community garden project updates',
                'author' => 'GreenThumb',
                'replies' => 8,
                'views' => 45,
                'last_activity' => now()->subDays(1),
                'category' => 'Project Updates'
            ]
        ]);

        return view('community.discussions', compact('discussions'));
    }

    /**
     * Show community events.
     */
    private function showEvents(Request $request)
    {
        $user = Auth::user();

        // Sample events data
        $events = collect([
            (object) [
                'id' => 1,
                'title' => 'Community Cleanup Day',
                'description' => 'Join us for a community-wide cleanup event at the local park.',
                'date' => now()->addDays(7),
                'location' => 'Central Park',
                'attendees' => 25,
                'max_attendees' => 50,
                'organizer' => 'Community Team',
                'type' => 'cleanup'
            ],
            (object) [
                'id' => 2,
                'title' => 'Sustainable Living Workshop',
                'description' => 'Learn practical tips for reducing your environmental footprint.',
                'date' => now()->addDays(14),
                'location' => 'Community Center',
                'attendees' => 12,
                'max_attendees' => 30,
                'organizer' => 'EcoEducator',
                'type' => 'workshop'
            ],
            (object) [
                'id' => 3,
                'title' => 'Tree Planting Initiative',
                'description' => 'Help us plant 100 trees in the community forest area.',
                'date' => now()->addDays(21),
                'location' => 'Forest Reserve',
                'attendees' => 18,
                'max_attendees' => 40,
                'organizer' => 'Forest Friends',
                'type' => 'environmental'
            ]
        ]);

        return view('community.events', compact('events'));
    }

    /**
     * Show community leaderboard.
     */
    private function showLeaderboard(Request $request)
    {
        $user = Auth::user();

        // Get top contributors by credibility score
        $topContributors = UserCredibilityScore::with('user')
            ->orderBy('total_score', 'desc')
            ->take(20)
            ->get();

        // Get top project creators (using whereHas for SQLite compatibility)
        $topCreators = User::whereHas('communityProjects')
            ->withCount('communityProjects as projects_count')
            ->orderBy('projects_count', 'desc')
            ->take(10)
            ->get();

        // Get top donors (using whereHas for SQLite compatibility)
        $topDonors = User::whereHas('projectDonations')
            ->withSum('projectDonations as total_donated', 'amount')
            ->orderBy('total_donated', 'desc')
            ->take(10)
            ->get();

        // Get top volunteers (using whereHas for SQLite compatibility)
        $topVolunteers = User::whereHas('projectVolunteers')
            ->withSum('projectVolunteers as total_hours', 'hours_contributed')
            ->orderBy('total_hours', 'desc')
            ->take(10)
            ->get();

        return view('community.leaderboard', compact(
            'topContributors',
            'topCreators',
            'topDonors',
            'topVolunteers'
        ));
    }
}
