

<?php $__env->startSection('title', 'Security Management System'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Security Management System</h1>
                <p class="text-gray-600 mt-1">Manage platform security settings, access controls, and monitoring</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportSecurityLogs()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Logs
                </button>
                <button onclick="saveSecuritySettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Security Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Secure</h3>
                    <p class="text-sm text-gray-600">System Status</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">1,247</h3>
                    <p class="text-sm text-gray-600">Active Sessions</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">23</h3>
                    <p class="text-sm text-gray-600">Security Alerts</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">5</h3>
                    <p class="text-sm text-gray-600">Blocked IPs</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'authentication' }">
                <button @click="activeTab = 'authentication'" :class="activeTab === 'authentication' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Authentication
                </button>
                <button @click="activeTab = 'access'" :class="activeTab === 'access' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Access Control
                </button>
                <button @click="activeTab = 'monitoring'" :class="activeTab === 'monitoring' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Security Monitoring
                </button>
                <button @click="activeTab = 'policies'" :class="activeTab === 'policies' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Security Policies
                </button>
                <button @click="activeTab = 'audit'" :class="activeTab === 'audit' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Audit Logs
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'authentication' }">
        <!-- Authentication Tab -->
        <div x-show="activeTab === 'authentication'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Authentication Settings</h3>
                <form id="securitySettingsForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Login Attempts</label>
                            <input type="number" name="max_login_attempts" value="5" min="1" max="10" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <p class="text-xs text-gray-500 mt-1">Number of failed attempts before account lockout</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Lockout Duration (minutes)</label>
                            <input type="number" name="lockout_duration" value="30" min="5" max="1440" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <p class="text-xs text-gray-500 mt-1">How long accounts remain locked</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                            <input type="number" name="session_timeout" value="120" min="15" max="480" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <p class="text-xs text-gray-500 mt-1">Automatic logout after inactivity</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password Minimum Length</label>
                            <input type="number" name="password_min_length" value="8" min="6" max="20" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <p class="text-xs text-gray-500 mt-1">Minimum characters required for passwords</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Require Two-Factor Authentication</h4>
                                <p class="text-sm text-gray-500">Force all users to enable 2FA</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="require_2fa" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Password Complexity Requirements</h4>
                                <p class="text-sm text-gray-500">Require uppercase, lowercase, numbers, and symbols</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Remember Me Option</h4>
                                <p class="text-sm text-gray-500">Allow users to stay logged in longer</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                        Save Authentication Settings
                    </button>
                </form>
            </div>
        </div>

        <!-- Access Control Tab -->
        <div x-show="activeTab === 'access'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">IP Access Control</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Whitelist IPs (Admin Access)</label>
                        <textarea rows="4" placeholder="***********&#10;********&#10;***********" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"></textarea>
                        <p class="text-xs text-gray-500 mt-1">One IP address per line. Leave empty to allow all IPs.</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Blacklist IPs</label>
                        <textarea rows="4" placeholder="*************&#10;*********" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"></textarea>
                        <p class="text-xs text-gray-500 mt-1">Blocked IP addresses. One per line.</p>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Role-Based Access Control</h3>
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">Super Admin</h4>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Full Access</span>
                        </div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                User Management
                            </div>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Financial Access
                            </div>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                System Settings
                            </div>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Security Management
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">Admin</h4>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Limited Access</span>
                        </div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                User Management
                            </div>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                Financial Access
                            </div>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                                System Settings
                            </div>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                Security Management
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Monitoring Tab -->
        <div x-show="activeTab === 'monitoring'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Real-time Security Monitoring</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Login Attempts</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-2 bg-green-50 rounded">
                                <span class="text-sm"><EMAIL></span>
                                <span class="text-xs text-green-600">Success - 2 min ago</span>
                            </div>
                            <div class="flex items-center justify-between p-2 bg-red-50 rounded">
                                <span class="text-sm"><EMAIL></span>
                                <span class="text-xs text-red-600">Failed - 5 min ago</span>
                            </div>
                            <div class="flex items-center justify-between p-2 bg-green-50 rounded">
                                <span class="text-sm"><EMAIL></span>
                                <span class="text-xs text-green-600">Success - 8 min ago</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Security Alerts</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-2 bg-yellow-50 rounded">
                                <span class="text-sm">Multiple failed logins</span>
                                <span class="text-xs text-yellow-600">IP: *************</span>
                            </div>
                            <div class="flex items-center justify-between p-2 bg-red-50 rounded">
                                <span class="text-sm">Suspicious activity detected</span>
                                <span class="text-xs text-red-600">User ID: 1234</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Policies Tab -->
        <div x-show="activeTab === 'policies'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Security Policies</h3>
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Automatic Account Lockout</h4>
                            <p class="text-sm text-gray-500">Lock accounts after multiple failed login attempts</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">IP-based Blocking</h4>
                            <p class="text-sm text-gray-500">Automatically block suspicious IP addresses</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Security Audit Logging</h4>
                            <p class="text-sm text-gray-500">Log all security-related events</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Audit Logs Tab -->
        <div x-show="activeTab === 'audit'" class="space-y-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Security Audit Logs</h3>
                <div class="flex items-center mb-4 space-x-2">
                    <input type="date" id="logDate" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <select id="logType" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="">All Events</option>
                        <option value="login">Login Events</option>
                        <option value="password">Password Changes</option>
                        <option value="2fa">2FA Events</option>
                        <option value="admin">Admin Actions</option>
                    </select>
                    <button onclick="fetchSecurityLogs(1)" class="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700">Filter</button>
                    <button onclick="exportLogs()" class="px-4 py-2 bg-gray-600 text-white rounded-md text-sm hover:bg-gray-700">Export CSV</button>
                </div>
                <div id="securityLogs" class="space-y-2">
                    <div class="border border-gray-200 rounded p-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium">Login Attempt</span>
                            <span class="text-xs text-gray-500">2024-06-29 14:30:25</span>
                        </div>
                        <div class="text-sm text-gray-600 mt-1">User: <EMAIL> | IP: *********** | Status: Success</div>
                    </div>
                    <div class="border border-gray-200 rounded p-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium">Failed Login</span>
                            <span class="text-xs text-gray-500">2024-06-29 14:25:10</span>
                        </div>
                        <div class="text-sm text-gray-600 mt-1">User: <EMAIL> | IP: *********** | Reason: Invalid credentials</div>
                    </div>
                    <div class="border border-gray-200 rounded p-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium">Password Change</span>
                            <span class="text-xs text-gray-500">2024-06-29 13:45:33</span>
                        </div>
                        <div class="text-sm text-gray-600 mt-1">User: <EMAIL> | IP: ******** | Status: Success</div>
                    </div>
                </div>
                <div id="logsPagination" class="mt-4 flex justify-center space-x-2">
                    <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">Previous</button>
                    <button class="px-3 py-1 bg-red-600 text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">2</button>
                    <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">3</button>
                    <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">Next</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize form if it exists
    const form = document.getElementById('securitySettingsForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            saveSecuritySettings();
        });
    }
});

function exportSecurityLogs() {
    alert('Export security logs functionality will be implemented');
}

function saveSecuritySettings() {
    alert('Security settings saved successfully!');
}

function fetchSecurityLogs(page = 1) {
    alert('Fetch security logs functionality will be implemented');
}

function exportLogs() {
    alert('Export logs functionality will be implemented');
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/admin/security-management/index.blade.php ENDPATH**/ ?>