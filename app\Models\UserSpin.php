<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserSpin extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'spin_type',
        'points_spent',
        'is_winner',
        'prize_id',
        'prize_name',
        'prize_description',
        'delivery_method',
        'cash_value',
        'points_value',
        'prize_claimed',
        'prize_claimed_at',
        'admin_notes',
    ];

    protected $casts = [
        'is_winner' => 'boolean',
        'prize_claimed' => 'boolean',
        'prize_claimed_at' => 'datetime',
        'cash_value' => 'decimal:2',
    ];

    /**
     * Get the user that owns the spin
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the prize associated with the spin
     */
    public function prize()
    {
        return $this->belongsTo(SpinPrize::class);
    }

    /**
     * Get recent spins for a user
     */
    public static function getRecentSpinsForUser($userId, $limit = 10)
    {
        return static::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get unclaimed prizes for a user
     */
    public static function getUnclaimedPrizesForUser($userId)
    {
        return static::where('user_id', $userId)
            ->where('is_winner', true)
            ->where('prize_claimed', false)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Mark prize as claimed
     */
    public function claimPrize($adminNotes = null)
    {
        $this->update([
            'prize_claimed' => true,
            'prize_claimed_at' => now(),
            'admin_notes' => $adminNotes,
        ]);
    }

    /**
     * Get spin type display name
     */
    public function getSpinTypeDisplayAttribute()
    {
        return match($this->spin_type) {
            'free' => 'Free Daily Spin',
            'paid' => 'Extra Spin',
            default => ucfirst($this->spin_type),
        };
    }

    /**
     * Get status display
     */
    public function getStatusDisplayAttribute()
    {
        if (!$this->is_winner) {
            return 'No Prize';
        }

        return $this->prize_claimed ? 'Prize Claimed' : 'Prize Pending';
    }
}
