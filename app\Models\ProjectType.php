<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'slug', 'description', 'icon', 'color', 'features',
        'min_votes_required', 'requires_voting', 'allows_donations',
        'allows_volunteers', 'allows_crowdfunding', 'has_stages',
        'default_duration_days', 'is_active', 'sort_order'
    ];

    protected $casts = [
        'features' => 'array',
        'requires_voting' => 'boolean',
        'allows_donations' => 'boolean',
        'allows_volunteers' => 'boolean',
        'allows_crowdfunding' => 'boolean',
        'has_stages' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function projects()
    {
        return $this->hasMany(CommunityProject::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    public function getFeatureListAttribute()
    {
        $features = [];
        if ($this->requires_voting) $features[] = 'Community Voting';
        if ($this->allows_donations) $features[] = 'Anonymous Donations';
        if ($this->allows_volunteers) $features[] = 'Volunteer Recruitment';
        if ($this->allows_crowdfunding) $features[] = 'Crowdfunding';
        if ($this->has_stages) $features[] = 'Project Stages';
        
        return array_merge($features, $this->features ?? []);
    }
}
