<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\WithdrawalController;
use App\Http\Controllers\MembershipController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\MaintenanceController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\FeaturedProjectController;
use App\Http\Controllers\MarketplaceController;

// Public routes
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);
});

// Email verification routes
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', [App\Http\Controllers\Auth\VerificationController::class, 'show'])->name('verification.notice');
    Route::get('/email/verify/{id}/{hash}', [App\Http\Controllers\Auth\VerificationController::class, 'verify'])->name('verification.verify');
    Route::post('/email/resend', [App\Http\Controllers\Auth\VerificationController::class, 'resend'])->name('verification.resend');
    Route::post('/email/bypass', [App\Http\Controllers\Auth\VerificationController::class, 'bypass'])->name('verification.bypass');
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
});

// Dashboard routes (protected by auth and email verification middleware)
Route::middleware(['auth', 'verified'])->group(function () {
    // Main dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Dashboard sub-pages
    Route::get('/dashboard/referrals', [DashboardController::class, 'referrals'])->name('dashboard.referrals');
    Route::get('/dashboard/earnings', function() { return redirect()->route('wallet.index'); })->name('dashboard.earnings');
    Route::get('/dashboard/membership', function() { return redirect()->route('activations.index'); })->name('dashboard.membership');
    Route::get('/dashboard/profile', [DashboardController::class, 'profile'])->name('dashboard.profile');
    Route::put('/dashboard/profile', [DashboardController::class, 'updateProfile'])->name('dashboard.profile.update');

    // Awareness (Referral) management
    Route::get('/awareness', [ReferralController::class, 'index'])->name('awareness.index');
    Route::get('/awareness/referrals', [ReferralController::class, 'referrals'])->name('awareness.referrals');
    Route::post('/awareness/validate-code', [ReferralController::class, 'validateCode'])->name('awareness.validate-code');
    Route::get('/awareness/tree-data', [ReferralController::class, 'getTreeData'])->name('awareness.tree-data');
    Route::get('/awareness/materials', [ReferralController::class, 'generateMaterials'])->name('awareness.materials');
    Route::post('/awareness/track-click', [ReferralController::class, 'trackClick'])->name('awareness.track-click');
    Route::get('/awareness/tools', [ReferralController::class, 'tools'])->name('awareness.tools');
    Route::get('/awareness/analytics', [ReferralController::class, 'analytics'])->name('awareness.analytics');

    // Club routes (accessible to all users) - MUST come before catch-all routes
    Route::prefix('community')->name('community.')->group(function () {
        Route::get('/clubs', function () {
            $user = auth()->user();
            return view('community.clubs.index', [
                'userStageLevel' => $user ? ($user->stage_level ?? 1) : 1,
                'userEliteInvitation' => $user ? ($user->elite_invitation ?? false) : false,
                'pendingInvitations' => $user ? ($user->pending_invitations ?? 0) : 0
            ]);
        })->name('clubs.index');

        Route::get('/clubs/vip', function () {
            $user = auth()->user();
            if (!$user || ($user->stage_level ?? 1) < 3) {
                return redirect()->route('community.clubs.index')->with('error', 'VIP Club requires Stage 3 activation.');
            }
            return view('community.clubs.vip');
        })->name('clubs.vip');

        Route::get('/clubs/vvip', function () {
            $user = auth()->user();
            if (!$user || ($user->stage_level ?? 1) < 5) {
                return redirect()->route('community.clubs.index')->with('error', 'VVIP Club requires Stage 5 activation.');
            }
            return view('community.clubs.vvip');
        })->name('clubs.vvip');

        Route::get('/clubs/elite', function () {
            $user = auth()->user();
            if (!$user || ($user->stage_level ?? 1) < 6 || !($user->elite_invitation ?? false)) {
                return redirect()->route('community.clubs.index')->with('error', 'Elite Club requires Stage 6 activation and invitation.');
            }
            return view('community.clubs.elite');
        })->name('clubs.elite');

        Route::get('/clubs/elite/insurance', function () {
            return view('community.clubs.elite-insurance');
        })->name('clubs.elite.insurance');

        Route::get('/clubs/elite/security', function () {
            return view('community.clubs.elite-security');
        })->name('clubs.elite.security');

        Route::get('/clubs/elite/networking', function () {
            return view('community.clubs.elite-networking');
        })->name('clubs.elite.networking');

        // Club actions that require authentication
        Route::middleware('auth')->group(function () {
            Route::post('/clubs/elite/request-invitation', function () {
                return response()->json(['success' => true, 'message' => 'Elite invitation request submitted for review.']);
            });

            Route::post('/clubs/elite/purchase-license', function () {
                return response()->json(['success' => true, 'message' => 'Leadership license purchase initiated.']);
            });
        });
    });

    // Community Projects
    Route::prefix('community')->name('community.')->middleware('auth')->group(function () {
        Route::get('/', [App\Http\Controllers\CommunityProjectController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\CommunityProjectController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\CommunityProjectController::class, 'store'])->name('store');
        Route::post('/{project}/vote', [App\Http\Controllers\CommunityProjectController::class, 'vote'])->name('vote');
        Route::post('/{project}/donate', [App\Http\Controllers\CommunityProjectController::class, 'donate'])->name('donate');
        Route::post('/{project}/volunteer', [App\Http\Controllers\CommunityProjectController::class, 'volunteer'])->name('volunteer');
        Route::delete('/{project}', [App\Http\Controllers\CommunityProjectController::class, 'destroy'])->name('destroy');
        Route::post('/projects/auto-save', function () { return response()->json(['success' => true]); })->name('projects.auto-save');

        // This catch-all route MUST come last to avoid conflicts
        Route::get('/{project}', [App\Http\Controllers\CommunityProjectController::class, 'show'])->name('show');
    });

    // Crowdfunding routes
    Route::prefix('crowdfund')->name('crowdfund.')->middleware('auth')->group(function () {
        Route::get('/', [App\Http\Controllers\CrowdfundController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\CrowdfundController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\CrowdfundController::class, 'store'])->name('store');
        Route::get('/{campaign}', [App\Http\Controllers\CrowdfundController::class, 'show'])->name('show');
        Route::post('/{campaign}/contribute', [App\Http\Controllers\CrowdfundController::class, 'contribute'])->name('contribute');
    });

    // Wallet management
    Route::get('/wallet', [WalletController::class, 'index'])->name('wallet.index');
    Route::get('/wallet/add-balance', [WalletController::class, 'addBalanceForm'])->name('wallet.add-balance');
    Route::post('/wallet/add-balance', [WalletController::class, 'addBalance'])->name('wallet.process-add-balance');
    Route::get('/wallet/withdrawal', [WalletController::class, 'withdrawalForm'])->name('wallet.withdrawal');
    Route::post('/wallet/withdrawal', [WalletController::class, 'processWithdrawal'])->name('wallet.process-withdrawal');
    Route::get('/wallet/{currency}', [WalletController::class, 'show'])->name('wallet.show');
    Route::get('/wallet/exchange', [WalletController::class, 'exchange'])->name('wallet.exchange');
    Route::post('/wallet/exchange', [WalletController::class, 'processExchange'])->name('wallet.process-exchange');
    Route::get('/wallet/transaction/{transactionId}', [WalletController::class, 'transaction'])->name('wallet.transaction');
    Route::get('/api/wallet/balance/{currency}', [WalletController::class, 'getBalance'])->name('wallet.balance');
    Route::get('/api/wallet/exchange-rate/{from}/{to}', [WalletController::class, 'getExchangeRate'])->name('wallet.exchange-rate');

    // Activations management (formerly Membership)
    Route::get('/activations', [MembershipController::class, 'index'])->name('activations.index');
    Route::post('/activations/activate', [MembershipController::class, 'activate'])->name('activations.activate');
    Route::get('/activations/upgrade', [MembershipController::class, 'upgradeOptions'])->name('activations.upgrade');
    Route::get('/activations/stage/{stage}', [MembershipController::class, 'getStageDetails'])->name('activations.stage-details');

    // Legacy membership routes (redirect to activations)
    Route::get('/membership', function() { return redirect()->route('activations.index'); });
    Route::post('/membership/activate', function() { return redirect()->route('activations.activate'); });

    // Profile management
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.update-password');
    Route::put('/profile/bank-details', [App\Http\Controllers\ProfileController::class, 'updateBankDetails'])->name('profile.update-bank-details');

    // Public profile routes
    Route::get('/user/{user}', function ($user) {
        $user = \App\Models\User::findOrFail($user);
        $stats = [
            'total_earnings' => 0,
            'total_referrals' => 0,
            'member_since' => $user->created_at,
            'activated_stages_count' => 0
        ];
        $activatedStages = collect([]);
        return view('profile.show', compact('user', 'stats', 'activatedStages'));
    })->name('user.profile.show');

    // Profile request routes
    Route::get('/user/{user}/request', function ($user) {
        $user = \App\Models\User::findOrFail($user);
        $previousRequests = collect([]); // Mock data
        return view('profile.request-form', compact('user', 'previousRequests'));
    })->name('profile.request');

    Route::post('/user/{user}/request', function ($user) {
        // Handle profile request submission
        return redirect()->back()->with('success', 'Profile request submitted successfully!');
    })->name('profile.request.submit');

    // Profile Information Request System
    Route::post('/profile/request-access', function () {
        return response()->json(['success' => true, 'message' => 'Profile access request sent successfully!']);
    })->name('profile.request-access');

    // Stage areas
    Route::get('/stages/{stage}', [App\Http\Controllers\StageAreaController::class, 'index'])->name('stages.area');
    Route::get('/stages/{stage}/resources', [App\Http\Controllers\StageAreaController::class, 'resources'])->name('stages.resources');
    Route::get('/stages/{stage}/rewards', [App\Http\Controllers\StageAreaController::class, 'rewards'])->name('stages.rewards');
    Route::get('/stages/{stage}/community', [App\Http\Controllers\StageAreaController::class, 'community'])->name('stages.community');

    // Stage activities
    Route::get('/stages/{stage}/activities', [App\Http\Controllers\ActivitiesController::class, 'index'])->name('stages.activities.index');
    Route::get('/stages/{stage}/activities/{activity}', [App\Http\Controllers\ActivitiesController::class, 'show'])->name('stages.activities.show');
    Route::post('/stages/{stage}/activities/{activity}/start', [App\Http\Controllers\ActivitiesController::class, 'start'])->name('stages.activities.start');
    Route::post('/stages/{stage}/activities/{activity}/complete', [App\Http\Controllers\ActivitiesController::class, 'complete'])->name('stages.activities.complete');
    Route::get('/stages/{stage}/activities/type/{type}', [App\Http\Controllers\ActivitiesController::class, 'byType'])->name('stages.activities.by-type');
    Route::get('/stages/{stage}/activities-progress', [App\Http\Controllers\ActivitiesController::class, 'progress'])->name('stages.activities.progress');
    Route::get('/activity-resources/{resource}/download', [App\Http\Controllers\ActivitiesController::class, 'downloadResource'])->name('activities.resource.download');

    // Language switching
    Route::get('/language/{language}', [LanguageController::class, 'switch'])->name('language.switch');
    Route::get('/api/languages', [LanguageController::class, 'getAvailable'])->name('language.available');

    // Wallet routes
    Route::get('/wallet', [WalletController::class, 'index'])->name('wallet.index');
    Route::get('/wallet/{currency}', [WalletController::class, 'show'])->name('wallet.show');
    Route::get('/wallet/pay', [WalletController::class, 'pay'])->name('wallet.pay');
    Route::post('/wallet/pay', [WalletController::class, 'processPay'])->name('wallet.process-pay');
    Route::get('/wallet/add-balance', [WalletController::class, 'addBalance'])->name('wallet.add-balance');
    Route::post('/wallet/add-balance', [WalletController::class, 'processAddBalance'])->name('wallet.process-add-balance');
    Route::get('/wallet/withdrawal', [WalletController::class, 'withdrawal'])->name('wallet.withdrawal');
    Route::post('/wallet/withdrawal', [WalletController::class, 'processWithdrawal'])->name('wallet.process-withdrawal');
    Route::get('/wallet/exchange', [WalletController::class, 'exchange'])->name('wallet.exchange');
    Route::post('/wallet/exchange', [WalletController::class, 'processExchange'])->name('wallet.process-exchange');
    Route::post('/wallet/convert-points', [WalletController::class, 'convertPoints'])->name('wallet.convert-points');
    Route::get('/wallet/earning-history', [WalletController::class, 'earningHistory'])->name('wallet.earning-history');
    Route::get('/wallet/transactions', function () { return view('wallet.transactions'); })->name('wallet.transactions');

    // Spin system routes
    Route::get('/wallet/spin', [App\Http\Controllers\SpinController::class, 'index'])->name('wallet.spin');
    Route::post('/wallet/spin', [App\Http\Controllers\SpinController::class, 'spin'])->name('wallet.process-spin');

    // Notifications routes
    Route::get('/notifications', function () { return view('notifications.index'); })->name('notifications.index');
    Route::get('/notifications/settings', function () { return view('notifications.settings'); })->name('notifications.settings');
    Route::post('/notifications/settings', function () { return redirect()->back()->with('success', 'Notification preferences updated successfully!'); })->name('notifications.settings.update');
    Route::post('/notifications/{notification}/read', function () { return response()->json(['success' => true]); })->name('notifications.read');
    Route::post('/notifications/mark-all-read', function () { return response()->json(['success' => true]); })->name('notifications.mark-all-read');

    // Messages routes
    Route::get('/messages', function () { return view('messages.index'); })->name('messages.index');
    Route::get('/messages/compose', function () { return view('messages.compose'); })->name('messages.compose');
    Route::post('/messages/send', function () { return redirect()->route('messages.index')->with('success', 'Message sent successfully!'); })->name('messages.send');

    // Featured Projects routes
    Route::get('/featured-projects', [FeaturedProjectController::class, 'index'])->name('featured-projects.index');
    Route::get('/featured-projects/{project}', [FeaturedProjectController::class, 'show'])->name('featured-projects.show');
    Route::post('/featured-projects/{project}/enroll', [FeaturedProjectController::class, 'enroll'])->name('featured-projects.enroll');
    Route::post('/featured-projects/{project}/submit-proof', [FeaturedProjectController::class, 'submitProof'])->name('featured-projects.submit-proof');
    Route::post('/featured-projects/participations/{participation}/verify', [FeaturedProjectController::class, 'verify'])->name('featured-projects.verify');
});

// Marketplace routes (some public, some auth required)
Route::get('/marketplace', [MarketplaceController::class, 'index'])->name('marketplace.index');
Route::get('/marketplace/products/{product}', [MarketplaceController::class, 'show'])->name('marketplace.show');

Route::middleware('auth')->group(function () {
    Route::post('/marketplace/products/{product}/purchase', [MarketplaceController::class, 'purchase'])->name('marketplace.purchase');
    Route::get('/marketplace/orders', [MarketplaceController::class, 'orders'])->name('marketplace.orders');
    Route::get('/marketplace/orders/{order}', [MarketplaceController::class, 'showOrder'])->name('marketplace.orders.show');
    Route::post('/marketplace/orders/{order}/mark-delivered', [MarketplaceController::class, 'markDelivered'])->name('marketplace.orders.mark-delivered');
    Route::post('/marketplace/orders/{order}/confirm-receipt', [MarketplaceController::class, 'confirmReceipt'])->name('marketplace.orders.confirm-receipt');

    // Points routes
    Route::get('/points', [App\Http\Controllers\PointsController::class, 'index'])->name('points.index');
    Route::get('/points/helpers', function () { return view('points.helpers'); })->name('points.helpers');
    Route::post('/points/redeem', [App\Http\Controllers\PointsController::class, 'redeem'])->name('points.redeem');
    Route::post('/points/claim-daily', [App\Http\Controllers\PointsController::class, 'claimDailyReward'])->name('points.claim-daily');
    Route::post('/points/share', function () { return response()->json(['success' => true]); });
    Route::post('/points/request', function () { return response()->json(['success' => true]); });
    Route::post('/points/fulfill-request', function () { return response()->json(['success' => true]); });

    // Organization Structure Pages
    Route::get('/about', function () { return view('organization.about'); })->name('organization.about');
    Route::get('/mission', function () { return view('organization.mission'); })->name('organization.mission');
    Route::get('/leadership', function () { return view('organization.leadership'); })->name('organization.leadership');
    Route::get('/impact', function () { return view('organization.impact'); })->name('organization.impact');
    Route::get('/partnerships', function () { return view('organization.partnerships'); })->name('organization.partnerships');
    Route::get('/success-stories', function () { return view('engagement.success-stories'); })->name('organization.success-stories');

    // Learning & Growth Pages
    Route::get('/academy', function () { return view('learning.academy'); })->name('learning.academy');
    Route::get('/courses', function () { return view('learning.courses'); })->name('learning.courses');
    Route::get('/resources', function () { return view('learning.resources'); })->name('learning.resources');
    Route::get('/webinars', function () { return view('learning.webinars'); })->name('learning.webinars');
    Route::get('/certifications', function () { return view('learning.certifications'); })->name('learning.certifications');

    // Additional Engagement Pages
    Route::get('/news', function () { return view('engagement.news'); })->name('engagement.news');
    Route::get('/events', function () { return view('engagement.events'); })->name('engagement.events');
    Route::get('/blog', function () { return view('engagement.blog'); })->name('engagement.blog');
    Route::get('/volunteer', function () { return view('engagement.volunteer'); })->name('engagement.volunteer');

    // Virtual Tools Pages
    Route::get('/virtual-tools/identity', function () { return view('virtual-tools.identity'); })->name('virtual-tools.identity');
    Route::get('/virtual-tools/environment', function () { return view('virtual-tools.environment'); })->name('virtual-tools.environment');
    Route::get('/virtual-tools/storage', function () { return view('virtual-tools.storage'); })->name('virtual-tools.storage');
    Route::get('/virtual-tools/vpn', function () { return view('virtual-tools.vpn'); })->name('virtual-tools.vpn');
    Route::get('/virtual-tools/assistant', function () { return view('virtual-tools.assistant'); })->name('virtual-tools.assistant');

    // Frontend Widgets (for non-registered users)
    Route::get('/widget/community-projects', [App\Http\Controllers\WidgetController::class, 'communityProjects'])->name('widget.community-projects');
});

// Admin authentication routes
Route::prefix('admin')->group(function () {
    Route::get('/login', [App\Http\Controllers\Auth\AdminAuthController::class, 'showLoginForm'])->name('admin.login');
    Route::post('/login', [App\Http\Controllers\Auth\AdminAuthController::class, 'login']);
    Route::get('/register', [App\Http\Controllers\Auth\AdminAuthController::class, 'showRegistrationForm'])->name('admin.register');
    Route::post('/register', [App\Http\Controllers\Auth\AdminAuthController::class, 'register']);
    Route::post('/logout', [App\Http\Controllers\Auth\AdminAuthController::class, 'logout'])->name('admin.logout');
});

// Admin routes
Route::middleware(['auth', \App\Http\Middleware\AdminMiddleware::class])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');

    // User management
    Route::get('/users', [AdminController::class, 'users'])->name('users.index');
    Route::get('/users/export', [AdminController::class, 'exportUsers'])->name('users.export');
    Route::get('/users/{user}', [AdminController::class, 'userShow'])->name('users.show');
    Route::post('/users/{user}/toggle-status', [AdminController::class, 'toggleUserStatus'])->name('users.toggle-status');
    Route::post('/users/{user}/make-admin', [AdminController::class, 'makeAdmin'])->name('users.make-admin');
    Route::post('/users/{user}/remove-admin', [AdminController::class, 'removeAdmin'])->name('users.remove-admin');
    Route::post('/users/{user}/approve-admin', [AdminController::class, 'approveAdmin'])->name('users.approve-admin');
    Route::post('/users/{user}/reject-admin', [AdminController::class, 'rejectAdmin'])->name('users.reject-admin');

    // Bulk user actions
    Route::post('/users/bulk-activate', [AdminController::class, 'bulkActivateUsers'])->name('users.bulk-activate');
    Route::post('/users/bulk-deactivate', [AdminController::class, 'bulkDeactivateUsers'])->name('users.bulk-deactivate');
    Route::post('/users/bulk-make_admin', [AdminController::class, 'bulkMakeAdmin'])->name('users.bulk-make-admin');

    // Currency management
    Route::get('/currencies', [AdminController::class, 'currencies'])->name('currencies.index');
    Route::put('/currencies/{currency}', [AdminController::class, 'updateCurrency'])->name('currencies.update');

    // Language management
    Route::get('/languages', [AdminController::class, 'languages'])->name('languages.index');
    Route::put('/languages/{language}', [AdminController::class, 'updateLanguage'])->name('languages.update');

    // Enhanced User Management
    Route::get('/users/{user}/edit', [App\Http\Controllers\Admin\UserManagementController::class, 'edit'])->name('users.edit');
    Route::put('/users/{user}/update', [App\Http\Controllers\Admin\UserManagementController::class, 'update'])->name('users.update');
    Route::put('/users/{user}/password', [App\Http\Controllers\Admin\UserManagementController::class, 'updatePassword'])->name('users.update-password');
    Route::post('/users/{user}/add-balance', [App\Http\Controllers\Admin\UserManagementController::class, 'addBalance'])->name('users.add-balance');
    Route::post('/users/{user}/deduct-balance', [App\Http\Controllers\Admin\UserManagementController::class, 'deductBalance'])->name('users.deduct-balance');
    Route::get('/users/{user}/wallet-history', [App\Http\Controllers\Admin\UserManagementController::class, 'walletHistory'])->name('users.wallet-history');

    // Enhanced Stage Management
    Route::get('/stages', [App\Http\Controllers\Admin\StageManagementController::class, 'index'])->name('stages.index');
    Route::get('/stages/create', [App\Http\Controllers\Admin\StageManagementController::class, 'create'])->name('stages.create');
    Route::post('/stages', [App\Http\Controllers\Admin\StageManagementController::class, 'store'])->name('stages.store');
    Route::get('/stages/{stage}/show', [App\Http\Controllers\Admin\StageManagementController::class, 'show'])->name('stages.show');
    Route::get('/stages/{stage}/edit', [App\Http\Controllers\Admin\StageManagementController::class, 'edit'])->name('stages.edit');
    Route::put('/stages/{stage}', [App\Http\Controllers\Admin\StageManagementController::class, 'update'])->name('stages.update');
    Route::put('/stages/{stage}/pricing', [App\Http\Controllers\Admin\StageManagementController::class, 'updatePricing'])->name('stages.update-pricing');
    Route::put('/stages/{stage}/benefits', [App\Http\Controllers\Admin\StageManagementController::class, 'updateBenefits'])->name('stages.update-benefits');
    Route::post('/stages/{stage}/toggle-status', [App\Http\Controllers\Admin\StageManagementController::class, 'toggleStatus'])->name('stages.toggle-status');
    Route::delete('/stages/{stage}', [App\Http\Controllers\Admin\StageManagementController::class, 'destroy'])->name('stages.destroy');
    Route::post('/stages/reorder', [App\Http\Controllers\Admin\StageManagementController::class, 'reorder'])->name('stages.reorder');

    // Project Types Management
    Route::resource('project-types', App\Http\Controllers\ProjectTypeController::class);

    // Financial management
    Route::get('/finances', [AdminController::class, 'finances'])->name('finances.index');

    // System settings
    Route::get('/settings', [AdminController::class, 'settings'])->name('settings.index');
    Route::put('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');

    // Stage rewards management
    Route::get('/stage-rewards', [AdminController::class, 'stageRewards'])->name('stage-rewards.index');
    Route::post('/stage-rewards', [AdminController::class, 'createStageReward'])->name('stage-rewards.create');
    Route::put('/stage-rewards/{reward}', [AdminController::class, 'updateStageReward'])->name('stage-rewards.update');
    Route::delete('/stage-rewards/{reward}', [AdminController::class, 'deleteStageReward'])->name('stage-rewards.delete');

    // Admin permission management (Top Admin only)
    Route::get('/permissions', [AdminController::class, 'permissions'])->name('permissions.index');
    Route::put('/permissions/{user}', [AdminController::class, 'updatePermissions'])->name('permissions.update');

    // System maintenance (Top Admin only)
    Route::get('/maintenance', [MaintenanceController::class, 'index'])->name('maintenance.index');
    Route::post('/maintenance/clear-cache', [MaintenanceController::class, 'clearCache'])->name('maintenance.clear-cache');
    Route::post('/maintenance/clear-logs', [MaintenanceController::class, 'clearLogs'])->name('maintenance.clear-logs');
    Route::post('/maintenance/optimize', [MaintenanceController::class, 'optimize'])->name('maintenance.optimize');
    Route::post('/maintenance/database', [MaintenanceController::class, 'databaseMaintenance'])->name('maintenance.database');

    // Admin notifications
    Route::get('/notifications', [NotificationController::class, 'adminIndex'])->name('notifications.index');
    Route::post('/notifications/{notification}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::get('/notifications/recent', [NotificationController::class, 'getRecent'])->name('notifications.recent');
    Route::get('/notifications/count', [NotificationController::class, 'getUnreadCount'])->name('notifications.count');

    // Payment gateway management
    Route::get('/payments', [AdminController::class, 'payments'])->name('payments.index');
    Route::post('/payments/initialize', [AdminController::class, 'initializePayments'])->name('payments.initialize');
    Route::put('/payments/{gateway}/toggle', [AdminController::class, 'togglePaymentGateway'])->name('payments.toggle');
    Route::put('/payments/{gateway}/configure', [AdminController::class, 'configurePaymentGateway'])->name('payments.configure');

    // Withdrawal management
    Route::get('/withdrawals', [WithdrawalController::class, 'index'])->name('withdrawals.index');
    Route::get('/withdrawals/create', [WithdrawalController::class, 'create'])->name('withdrawals.create');
    Route::post('/withdrawals', [WithdrawalController::class, 'store'])->name('withdrawals.store');
    Route::delete('/withdrawals/{withdrawal}/cancel', [WithdrawalController::class, 'cancel'])->name('withdrawals.cancel');

    // Approval system routes
    Route::prefix('approvals')->name('approvals.')->group(function () {
        // User approvals
        Route::get('/users', [App\Http\Controllers\ApprovalController::class, 'users'])->name('users.index');
        Route::post('/users/{user}/approve', [App\Http\Controllers\ApprovalController::class, 'approveUser'])->name('users.approve');
        Route::post('/users/{user}/reject', [App\Http\Controllers\ApprovalController::class, 'rejectUser'])->name('users.reject');

        // Activation approvals
        Route::get('/activations', [App\Http\Controllers\ApprovalController::class, 'activations'])->name('activations.index');
        Route::post('/activations/{activation}/approve', [App\Http\Controllers\ApprovalController::class, 'approveActivation'])->name('activations.approve');
        Route::post('/activations/{activation}/reject', [App\Http\Controllers\ApprovalController::class, 'rejectActivation'])->name('activations.reject');

        // Commission approvals
        Route::get('/commissions', [App\Http\Controllers\ApprovalController::class, 'commissions'])->name('commissions.index');
        Route::post('/commissions/{commission}/approve', [App\Http\Controllers\ApprovalController::class, 'approveCommission'])->name('commissions.approve');
        Route::post('/commissions/{commission}/reject', [App\Http\Controllers\ApprovalController::class, 'rejectCommission'])->name('commissions.reject');
        Route::post('/commissions/bulk-approve', [App\Http\Controllers\ApprovalController::class, 'bulkApproveCommissions'])->name('commissions.bulk-approve');
        Route::post('/commissions/bulk-reject', [App\Http\Controllers\ApprovalController::class, 'bulkRejectCommissions'])->name('commissions.bulk-reject');
    });

    // Custom content management
    Route::get('/custom-content', function () { return view('admin.custom-content'); })->name('custom-content.index');
    Route::post('/custom-content', function () { return redirect()->back()->with('success', 'Content updated successfully!'); })->name('custom-content.update');

    // Stage activations management
    Route::get('/stage-activations', function () { return view('admin.stage-activations'); })->name('stage-activations.index');
    Route::post('/stage-activations/{activation}/approve', function () { return response()->json(['success' => true]); })->name('stage-activations.approve');
    Route::post('/stage-activations/{activation}/reject', function () { return response()->json(['success' => true]); })->name('stage-activations.reject');
    Route::post('/stage-activations/bulk-approve', function () { return response()->json(['success' => true]); })->name('stage-activations.bulk-approve');

    // Platform customization
    Route::get('/customization', function () { return view('admin.customization.index'); })->name('customization.index');
    Route::post('/customization/branding', function () { return redirect()->back()->with('success', 'Branding updated successfully!'); })->name('customization.branding');
    Route::post('/customization/pricing', function () { return redirect()->back()->with('success', 'Pricing plans updated successfully!'); })->name('customization.pricing');
    Route::post('/customization/widgets', function () { return redirect()->back()->with('success', 'Widgets updated successfully!'); })->name('customization.widgets');
    Route::post('/customization/appearance', function () { return redirect()->back()->with('success', 'Appearance updated successfully!'); })->name('customization.appearance');
    Route::post('/customization/footer', function () { return redirect()->back()->with('success', 'Footer updated successfully!'); })->name('customization.footer');
    Route::post('/customization/system', function () { return redirect()->back()->with('success', 'System settings updated successfully!'); })->name('customization.system');

    // Profile requests management
    Route::get('/profile-requests', function () {
        return view('admin.profile-requests.index', [
            'requests' => collect([]), // Mock data
            'pendingCount' => 5
        ]);
    })->name('profile-requests.index');
    Route::get('/profile-requests/{id}', function ($id) { return response()->json(['id' => $id, 'status' => 'pending']); });
    Route::post('/profile-requests/{id}/approve', function ($id) { return response()->json(['success' => true]); })->name('profile-requests.approve');
    Route::post('/profile-requests/{id}/reject', function ($id) { return response()->json(['success' => true]); })->name('profile-requests.reject');

    // Chat monitoring system
    Route::get('/chat-monitoring', function () {
        return view('admin.chat-monitoring.index', [
            'flaggedCount' => 3,
            'criticalAlerts' => 1,
            'highAlerts' => 2,
            'totalMessages' => 1250,
            'reviewedMessages' => 1200,
            'flaggedMessages' => collect([]),
            'alertWords' => collect([]),
            'allMessages' => collect([])
        ]);
    })->name('chat-monitoring.index');
    Route::post('/chat-monitoring/messages/{id}/review', function ($id) { return response()->json(['success' => true]); });
    Route::post('/chat-monitoring/alert-words', function () { return response()->json(['success' => true]); });
    Route::post('/chat-monitoring/alert-words/{id}/toggle', function ($id) { return response()->json(['success' => true]); });
    Route::delete('/chat-monitoring/alert-words/{id}', function ($id) { return response()->json(['success' => true]); });

    // Wallet management system
    Route::get('/wallet-management', function () { return view('admin.wallet-management.index'); })->name('wallet-management.index');
    Route::post('/wallet-management/currencies', function () { return response()->json(['success' => true]); });
    Route::put('/wallet-management/currencies/{id}', function ($id) { return response()->json(['success' => true]); });
    Route::delete('/wallet-management/currencies/{id}', function ($id) { return response()->json(['success' => true]); });

    // Points and rewards management
    Route::get('/points-rewards', function () { return view('admin.points-rewards.index'); })->name('points-rewards.index');
    Route::post('/points-rewards/actions', function () { return response()->json(['success' => true]); });
    Route::put('/points-rewards/actions/{id}', function ($id) { return response()->json(['success' => true]); });

    // Achievement management
    Route::get('/achievements', function () { return view('admin.achievements.index'); })->name('achievements.index');
    Route::post('/achievements', function () { return response()->json(['success' => true]); });
    Route::put('/achievements/{id}', function ($id) { return response()->json(['success' => true]); });

    // Awareness tools management
    Route::get('/awareness-tools', function () { return view('admin.awareness-tools.index'); })->name('awareness-tools.index');
    Route::post('/awareness-tools', function () { return response()->json(['success' => true]); });
    Route::put('/awareness-tools/{id}', function ($id) { return response()->json(['success' => true]); });

    // Withheld earnings management
    Route::get('/withheld-earnings', function () {
        return view('admin.withheld-earnings.index', [
            'totalWithheld' => 15420,
            'affectedUsers' => 47,
            'autoReleased' => 8750,
            'manualReleases' => 2340
        ]);
    })->name('withheld-earnings.index');
    Route::post('/withheld-earnings/{id}/release', function ($id) { return response()->json(['success' => true]); });
    Route::post('/withheld-earnings/bulk-release', function () { return response()->json(['success' => true]); });
    Route::get('/withheld-earnings/export', function () { return response()->json(['success' => true]); });

    // Club management system
    Route::get('/club-management', function () { return view('admin.club-management.index'); })->name('club-management.index');
    Route::post('/club-management/invitations', function () { return response()->json(['success' => true]); });
    Route::post('/club-management/invitations/{id}/approve', function ($id) { return response()->json(['success' => true]); });
    Route::post('/club-management/invitations/{id}/reject', function ($id) { return response()->json(['success' => true]); });
    Route::post('/club-management/members/{id}/remove', function ($id) { return response()->json(['success' => true]); });

    // Divine Lights Rank Management
    Route::get('/rank-management', function () { return view('admin.rank-management.index'); })->name('rank-management.index');
    Route::post('/rank-management/users/{id}/update', function ($id) { return response()->json(['success' => true]); });
    Route::post('/rank-management/achievements/create', function () { return response()->json(['success' => true]); });
    Route::post('/rank-management/bulk-update', function () { return response()->json(['success' => true]); });

    // Admin Notification Alerts
    Route::get('/notifications', function () { return view('admin.notifications.index'); })->name('notifications.index');
    Route::post('/notifications/{id}/mark-read', function ($id) { return response()->json(['success' => true]); });
    Route::post('/notifications/mark-all-read', function () { return response()->json(['success' => true]); });
    Route::post('/notifications/configure', function () { return response()->json(['success' => true]); });

    // Community Management System
    Route::get('/community-management', function () { return view('admin.community-management.index'); })->name('community-management.index');
    Route::post('/community-management/projects/{id}/approve', function ($id) { return response()->json(['success' => true]); });
    Route::post('/community-management/projects/{id}/reject', function ($id) { return response()->json(['success' => true]); });
    Route::post('/community-management/content/{id}/moderate', function ($id) { return response()->json(['success' => true]); });

    // Referral Tools Management
    Route::get('/referral-tools', function () { return view('admin.referral-tools.index'); })->name('referral-tools.index');
    Route::post('/referral-tools/create', function () { return response()->json(['success' => true]); });
    Route::post('/referral-tools/{id}/update', function ($id) { return response()->json(['success' => true]); });
    Route::post('/referral-tools/bonuses/create', function () { return response()->json(['success' => true]); });

    // Comprehensive User Management System
    Route::get('/user-management', function () { return view('admin.user-management.index'); })->name('user-management.index');
    Route::get('/user-profiles', function () { return view('admin.user-profiles.index'); })->name('user-profiles.index');
    Route::get('/user-wallets', function () { return view('admin.user-wallets.index'); })->name('user-wallets.index');
    Route::get('/user-referrals', function () { return view('admin.user-referrals.index'); })->name('user-referrals.index');
    Route::get('/user-activations', function () { return view('admin.user-activations.index'); })->name('user-activations.index');
    Route::get('/user-analytics', function () { return view('admin.user-analytics.index'); })->name('user-analytics.index');

    // Wallet Management System (merged with currency management)
    Route::get('/wallet-management', function () { return view('admin.wallet-management.index'); })->name('wallet-management.index');
    Route::get('/payment-methods', function () { return view('admin.payment-methods.index'); })->name('payment-methods.index');
    Route::get('/withdrawal-management', function () { return view('admin.withdrawal-management.index'); })->name('withdrawal-management.index');

    // Enhanced Language Management
    Route::get('/languages', function () { return view('admin.languages.index'); })->name('languages.index');
    Route::post('/languages/create', function () { return response()->json(['success' => true]); });
    Route::post('/languages/{id}/update', function ($id) { return response()->json(['success' => true]); });
    Route::post('/languages/translations/import', function () { return response()->json(['success' => true]); });
    Route::get('/languages/translations/export', function () { return response()->json(['success' => true]); });

    // Backend Referral Management System
    Route::get('/referral-management', function () { return view('admin.referral-management.index'); })->name('referral-management.index');
    Route::post('/referral-management/approve/{id}', function ($id) { return response()->json(['success' => true]); });
    Route::post('/referral-management/withhold/{id}', function ($id) { return response()->json(['success' => true]); });
    Route::post('/referral-management/bulk-approve', function () { return response()->json(['success' => true]); });
    Route::post('/referral-management/bulk-withhold', function () { return response()->json(['success' => true]); });

    // Backend Virtual Tools Management System
    Route::get('/virtual-tools', function () { return view('admin.virtual-tools.index'); })->name('virtual-tools.index');
    Route::post('/virtual-tools/toggle-section', function () { return response()->json(['success' => true]); });
    Route::post('/virtual-tools/toggle-tool', function () { return response()->json(['success' => true]); });
    Route::post('/virtual-tools/configure', function () { return response()->json(['success' => true]); });

    // Stage-Specific Backend Management System
    Route::get('/stage-management/stage1', function () { return view('admin.stage-management.stage1'); })->name('stage-management.stage1');
    Route::get('/stage-management/stage2', function () { return view('admin.stage-management.stage2'); })->name('stage-management.stage2');
    Route::get('/stage-management/stage3', function () { return view('admin.stage-management.stage3'); })->name('stage-management.stage3');
    Route::get('/stage-management/stage4', function () { return view('admin.stage-management.stage4'); })->name('stage-management.stage4');
    Route::get('/stage-management/stage5', function () { return view('admin.stage-management.stage5'); })->name('stage-management.stage5');
    Route::get('/stage-management/stage6', function () { return view('admin.stage-management.stage6'); })->name('stage-management.stage6');
    Route::post('/stage-management/{stage}/activities/create', function ($stage) { return response()->json(['success' => true]); });
    Route::post('/stage-management/{stage}/rewards/create', function ($stage) { return response()->json(['success' => true]); });
    Route::post('/stage-management/{stage}/community/manage', function ($stage) { return response()->json(['success' => true]); });
    Route::post('/stage-management/{stage}/resources/create', function ($stage) { return response()->json(['success' => true]); });

    // System Maintenance
    Route::get('/system-maintenance', function () { return view('admin.system-maintenance.index'); })->name('system-maintenance.index');
    Route::post('/system-maintenance/toggle-maintenance', function () { return response()->json(['success' => true]); });
    Route::post('/system-maintenance/create-backup', function () { return response()->json(['success' => true]); });
    Route::post('/system-maintenance/restore-backup', function () { return response()->json(['success' => true]); });
    Route::post('/system-maintenance/clear-logs', function () { return response()->json(['success' => true]); });
    Route::post('/system-maintenance/export-data', function () { return response()->json(['success' => true]); });
    Route::post('/system-maintenance/import-data', function () { return response()->json(['success' => true]); });
    Route::post('/system-maintenance/reset-website', function () { return response()->json(['success' => true]); });
    Route::post('/system-maintenance/delete-website-data', function () { return response()->json(['success' => true]); });

    // Platform Customization
    Route::get('/platform-customization', function () { return view('admin.platform-customization.index'); })->name('platform-customization.index');
    Route::post('/platform-customization/save', function () { return response()->json(['success' => true]); });
    Route::post('/platform-customization/upload-logo', function () { return response()->json(['success' => true]); });
    Route::post('/platform-customization/save-pricing', function () { return response()->json(['success' => true]); });
    Route::post('/platform-customization/generate-barcode', function () { return response()->json(['success' => true]); });

    // Admin Management System
    Route::get('/admin-management', function () { return view('admin.admin-management.index'); })->name('admin-management.index');
    Route::post('/admin-management/create', function () { return response()->json(['success' => true]); });
    Route::post('/admin-management/invite', function () { return response()->json(['success' => true]); });
    Route::post('/admin-management/approve/{id}', function ($id) { return response()->json(['success' => true]); });
    Route::post('/admin-management/suspend/{id}', function ($id) { return response()->json(['success' => true]); });
    Route::post('/admin-management/permissions/update', function () { return response()->json(['success' => true]); });

    // Platform Management System Routes
    Route::prefix('platform-customization')->name('platform-customization.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'index'])->name('index');
        Route::get('/dashboard', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'dashboard'])->name('dashboard');
        Route::get('/branding', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'branding'])->name('branding');
        Route::get('/appearance', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'appearance'])->name('appearance');
        Route::get('/layout', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'layout'])->name('layout');
        Route::post('/branding', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateBranding'])->name('update-branding');
        Route::post('/colors', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateColors'])->name('update-colors');
        Route::post('/typography', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateTypography'])->name('update-typography');
        Route::post('/layout', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateLayout'])->name('update-layout');
        Route::post('/features', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateFeatures'])->name('update-features');
        Route::post('/custom-code', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateCustomCode'])->name('update-custom-code');
        Route::post('/preview', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'previewChanges'])->name('preview');
        Route::get('/export', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'exportSettings'])->name('export');
        Route::post('/import', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'importSettings'])->name('import');

        // Sub-menu data routes
        Route::get('/branding', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'getBrandingSettings'])->name('branding');
        Route::get('/appearance', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'getAppearanceSettings'])->name('appearance');
        Route::post('/appearance', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateAppearanceSettings'])->name('update-appearance');
        Route::post('/content', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateContentSettings'])->name('update-content');
        Route::post('/theme', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateTheme'])->name('update-theme');
        Route::post('/{section}/reset', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'resetSection'])->name('reset-section');
    });

    // Spin Management System Routes
    Route::prefix('spin-management')->name('spin-management.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\SpinManagementController::class, 'index'])->name('index');
        Route::get('/settings', [App\Http\Controllers\Admin\SpinManagementController::class, 'settings'])->name('settings');
        Route::post('/settings', [App\Http\Controllers\Admin\SpinManagementController::class, 'updateSettings'])->name('update-settings');
        Route::get('/prizes', [App\Http\Controllers\Admin\SpinManagementController::class, 'prizes'])->name('prizes');
        Route::post('/prizes', [App\Http\Controllers\Admin\SpinManagementController::class, 'storePrize'])->name('store-prize');
        Route::put('/prizes/{prize}', [App\Http\Controllers\Admin\SpinManagementController::class, 'updatePrize'])->name('update-prize');
        Route::delete('/prizes/{prize}', [App\Http\Controllers\Admin\SpinManagementController::class, 'destroyPrize'])->name('destroy-prize');
        Route::get('/statistics', [App\Http\Controllers\Admin\SpinManagementController::class, 'statistics'])->name('statistics');
        Route::get('/unclaimed-prizes', [App\Http\Controllers\Admin\SpinManagementController::class, 'unclaimedPrizes'])->name('unclaimed-prizes');
        Route::post('/claim-prize/{spin}', [App\Http\Controllers\Admin\SpinManagementController::class, 'claimPrize'])->name('claim-prize');
    });

    // Project Voting Management System Routes
    Route::prefix('project-voting-management')->name('project-voting-management.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ProjectVotingManagementController::class, 'index'])->name('index');
        Route::get('/settings', [App\Http\Controllers\Admin\ProjectVotingManagementController::class, 'settings'])->name('settings');
        Route::get('/projects', [App\Http\Controllers\Admin\ProjectVotingManagementController::class, 'projects'])->name('projects');
        Route::get('/statistics', [App\Http\Controllers\Admin\ProjectVotingManagementController::class, 'statistics'])->name('statistics');
        Route::get('/voters', [App\Http\Controllers\Admin\ProjectVotingManagementController::class, 'voters'])->name('voters');
    });

    // Project Management System Routes
    Route::prefix('project-management')->name('project-management.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ProjectManagementController::class, 'index'])->name('index');
        Route::get('/projects', [App\Http\Controllers\Admin\ProjectManagementController::class, 'projects'])->name('projects');
        Route::get('/tasks', [App\Http\Controllers\Admin\ProjectManagementController::class, 'tasks'])->name('tasks');
        Route::get('/teams', [App\Http\Controllers\Admin\ProjectManagementController::class, 'teams'])->name('teams');
        Route::get('/reports', [App\Http\Controllers\Admin\ProjectManagementController::class, 'reports'])->name('reports');
        Route::get('/settings', [App\Http\Controllers\Admin\ProjectManagementController::class, 'settings'])->name('settings');
    });

    Route::prefix('security-management')->name('security-management.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\SecurityManagementController::class, 'index'])->name('index');
        Route::post('/authentication', [App\Http\Controllers\Admin\SecurityManagementController::class, 'updateAuthenticationSettings'])->name('update-authentication');
        Route::post('/access-control', [App\Http\Controllers\Admin\SecurityManagementController::class, 'updateAccessControl'])->name('update-access-control');
        Route::post('/policies', [App\Http\Controllers\Admin\SecurityManagementController::class, 'updateSecurityPolicies'])->name('update-policies');
        Route::get('/logs', [App\Http\Controllers\Admin\SecurityManagementController::class, 'getSecurityLogs'])->name('logs');
        Route::get('/logs/export', [App\Http\Controllers\Admin\SecurityManagementController::class, 'exportSecurityLogs'])->name('export-logs');
        Route::get('/analytics', [App\Http\Controllers\Admin\SecurityManagementController::class, 'getSecurityAnalytics'])->name('analytics');
        Route::post('/block-ip', [App\Http\Controllers\Admin\SecurityManagementController::class, 'blockIpAddress'])->name('block-ip');
        Route::post('/unblock-ip', [App\Http\Controllers\Admin\SecurityManagementController::class, 'unblockIpAddress'])->name('unblock-ip');
        Route::post('/security-scan', [App\Http\Controllers\Admin\SecurityManagementController::class, 'runSecurityScan'])->name('security-scan');

        // Sub-menu data routes
        Route::get('/authentication', [App\Http\Controllers\Admin\SecurityManagementController::class, 'getAuthenticationSettings'])->name('authentication');
        Route::get('/access-control', [App\Http\Controllers\Admin\SecurityManagementController::class, 'getAccessControlSettings'])->name('access-control');
        Route::get('/monitoring', [App\Http\Controllers\Admin\SecurityManagementController::class, 'getMonitoringSettings'])->name('monitoring');
        Route::post('/monitoring', [App\Http\Controllers\Admin\SecurityManagementController::class, 'updateMonitoringSettings'])->name('update-monitoring');
        Route::get('/policies', [App\Http\Controllers\Admin\SecurityManagementController::class, 'getPoliciesSettings'])->name('policies');
        Route::get('/audit', [App\Http\Controllers\Admin\SecurityManagementController::class, 'getAuditSettings'])->name('audit');
        Route::post('/audit', [App\Http\Controllers\Admin\SecurityManagementController::class, 'updateAuditSettings'])->name('update-audit');
        Route::post('/run-audit', [App\Http\Controllers\Admin\SecurityManagementController::class, 'runSecurityAudit'])->name('run-audit');
        Route::get('/export-report', [App\Http\Controllers\Admin\SecurityManagementController::class, 'exportSecurityReport'])->name('export-report');
    });
    Route::prefix('financial-overview')->name('financial-overview.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'index'])->name('index');
        Route::get('/revenue-data', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'getRevenueData'])->name('revenue-data');
        Route::get('/transactions', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'getTransactions'])->name('transactions');
        Route::post('/refund', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'processRefund'])->name('refund');
        Route::post('/payment-settings', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'updatePaymentSettings'])->name('payment-settings');
        Route::post('/generate-report', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'generateReport'])->name('generate-report');
        Route::get('/analytics', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'getFinancialAnalytics'])->name('analytics');
        Route::get('/export-transactions', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'exportTransactions'])->name('export-transactions');
        Route::get('/payouts', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'getPayoutHistory'])->name('payouts');
        Route::post('/process-payout', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'processPayout'])->name('process-payout');

        // Sub-menu data routes
        Route::get('/revenue-analytics', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'getRevenueAnalytics'])->name('revenue-analytics');
        Route::get('/transactions-summary', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'getTransactionsSummary'])->name('transactions-summary');
        Route::get('/reports-list', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'getReportsList'])->name('reports-list');
        Route::get('/analytics-dashboard', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'getAnalyticsDashboard'])->name('analytics-dashboard');
        Route::post('/revenue-settings', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'updateRevenueSettings'])->name('update-revenue-settings');
        Route::post('/schedule-report', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'scheduleReport'])->name('schedule-report');
        Route::get('/export', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'exportFinancialData'])->name('export');
        Route::post('/run-analysis', [App\Http\Controllers\Admin\FinancialOverviewController::class, 'runFinancialAnalysis'])->name('run-analysis');

        // Mock routes for tab functionality
        Route::get('/revenue-analytics', function() { return response()->json(['success' => true, 'data' => []]); })->name('revenue-analytics-data');
        Route::get('/transactions-summary', function() { return response()->json(['success' => true, 'data' => []]); })->name('transactions-summary-data');
    });
    Route::prefix('stage-management-system')->name('stage-management-system.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\StageManagementController::class, 'index'])->name('index');
        Route::put('/stages/{stageId}', [App\Http\Controllers\Admin\StageManagementController::class, 'updateStage'])->name('update-stage');
        Route::post('/stages', [App\Http\Controllers\Admin\StageManagementController::class, 'createStage'])->name('create-stage');
        Route::post('/progression-rules', [App\Http\Controllers\Admin\StageManagementController::class, 'updateProgressionRules'])->name('update-progression-rules');
        Route::get('/analytics', [App\Http\Controllers\Admin\StageManagementController::class, 'getStageAnalytics'])->name('analytics');
        Route::get('/stages/{stageId}/members', [App\Http\Controllers\Admin\StageManagementController::class, 'getStageMembers'])->name('stage-members');
        Route::post('/promote-user', [App\Http\Controllers\Admin\StageManagementController::class, 'promoteUser'])->name('promote-user');
        Route::post('/demote-user', [App\Http\Controllers\Admin\StageManagementController::class, 'demoteUser'])->name('demote-user');
        Route::post('/bulk-update', [App\Http\Controllers\Admin\StageManagementController::class, 'bulkUpdateMembers'])->name('bulk-update');
        Route::get('/export', [App\Http\Controllers\Admin\StageManagementController::class, 'exportStageData'])->name('export');
        Route::get('/stages/{stageId}/requirements', [App\Http\Controllers\Admin\StageManagementController::class, 'getStageRequirements'])->name('stage-requirements');
        Route::post('/validate-progression', [App\Http\Controllers\Admin\StageManagementController::class, 'validateStageProgression'])->name('validate-progression');

        // Mock routes for tab functionality
        Route::get('/members-overview', function() { return response()->json(['success' => true, 'data' => []]); })->name('members-overview-data');
    });
    Route::prefix('helpers-management')->name('helpers-management.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\HelpersManagementController::class, 'index'])->name('index');
        Route::post('/helpers', [App\Http\Controllers\Admin\HelpersManagementController::class, 'createHelper'])->name('create-helper');
        Route::put('/helpers/{helperId}', [App\Http\Controllers\Admin\HelpersManagementController::class, 'updateHelper'])->name('update-helper');
        Route::get('/tickets', [App\Http\Controllers\Admin\HelpersManagementController::class, 'getTickets'])->name('tickets');
        Route::post('/tickets', [App\Http\Controllers\Admin\HelpersManagementController::class, 'createTicket'])->name('create-ticket');
        Route::put('/tickets/{ticketId}', [App\Http\Controllers\Admin\HelpersManagementController::class, 'updateTicket'])->name('update-ticket');
        Route::post('/assign-ticket', [App\Http\Controllers\Admin\HelpersManagementController::class, 'assignTicket'])->name('assign-ticket');
        Route::post('/tickets/{ticketId}/close', [App\Http\Controllers\Admin\HelpersManagementController::class, 'closeTicket'])->name('close-ticket');
        Route::get('/knowledge-base', [App\Http\Controllers\Admin\HelpersManagementController::class, 'getKnowledgeBase'])->name('knowledge-base');
        Route::post('/knowledge-base/articles', [App\Http\Controllers\Admin\HelpersManagementController::class, 'createKnowledgeBaseArticle'])->name('create-article');
        Route::post('/settings', [App\Http\Controllers\Admin\HelpersManagementController::class, 'updateHelpDeskSettings'])->name('update-settings');
        Route::get('/performance', [App\Http\Controllers\Admin\HelpersManagementController::class, 'getHelperPerformance'])->name('performance');
        Route::get('/export', [App\Http\Controllers\Admin\HelpersManagementController::class, 'exportHelperData'])->name('export');

        // Mock routes for tab functionality
        Route::get('/staff-overview', function() { return response()->json(['success' => true, 'data' => []]); })->name('staff-overview-data');
        Route::get('/tickets-overview', function() { return response()->json(['success' => true, 'data' => []]); })->name('tickets-overview-data');
        Route::get('/knowledge-base-overview', function() { return response()->json(['success' => true, 'data' => []]); })->name('knowledge-base-overview-data');
    });

    // Payment Management
    Route::prefix('payment-management')->name('payment-management.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\PaymentManagementController::class, 'index'])->name('index');
        Route::get('/stats', [App\Http\Controllers\Admin\PaymentManagementController::class, 'getStats'])->name('stats');
        Route::post('/export', [App\Http\Controllers\Admin\PaymentManagementController::class, 'export'])->name('export');
        Route::post('/settings', [App\Http\Controllers\Admin\PaymentManagementController::class, 'saveSettings'])->name('save-settings');
    });

    // Language Management
    Route::prefix('language-management')->name('language-management.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\LanguageManagementController::class, 'index'])->name('index');
        Route::get('/stats', [App\Http\Controllers\Admin\LanguageManagementController::class, 'getStats'])->name('stats');
        Route::post('/export', [App\Http\Controllers\Admin\LanguageManagementController::class, 'export'])->name('export');
        Route::post('/settings', [App\Http\Controllers\Admin\LanguageManagementController::class, 'saveSettings'])->name('save-settings');
        Route::post('/add-language', [App\Http\Controllers\Admin\LanguageManagementController::class, 'addLanguage'])->name('add-language');
    });

    // Website Management
    Route::get('/website-management', function () { return view('admin.website-management.index'); })->name('website-management.index');
    Route::post('/website-management/save-settings', function () { return response()->json(['success' => true]); });
    Route::post('/website-management/publish-changes', function () { return response()->json(['success' => true]); });
    Route::post('/website-management/seo/update', function () { return response()->json(['success' => true]); });
    Route::post('/website-management/security/update', function () { return response()->json(['success' => true]); });

    // Backend Management System API Routes
    Route::prefix('api')->group(function () {
        // User Management System
        Route::get('/users/stats', [App\Http\Controllers\Admin\ManagementController::class, 'getUserStats']);
        Route::get('/users/list', [App\Http\Controllers\Admin\ManagementController::class, 'getUsers']);
        Route::patch('/users/{user}/status', [App\Http\Controllers\Admin\ManagementController::class, 'updateUserStatus']);

        // Financial Management System
        Route::get('/financial/stats', [App\Http\Controllers\Admin\ManagementController::class, 'getFinancialStats']);
        Route::post('/financial/calculate-earnings', [App\Http\Controllers\Admin\ManagementController::class, 'calculateAdminEarnings']);

        // System Maintenance
        Route::post('/system/clear-cache', [App\Http\Controllers\Admin\ManagementController::class, 'clearCache']);
        Route::post('/system/optimize', [App\Http\Controllers\Admin\ManagementController::class, 'optimizeSystem']);
        Route::get('/system/info', [App\Http\Controllers\Admin\ManagementController::class, 'getSystemInfo']);

        // Security Management
        Route::get('/security/logs', [App\Http\Controllers\Admin\ManagementController::class, 'getSecurityLogs']);
        Route::post('/security/settings', [App\Http\Controllers\Admin\ManagementController::class, 'updateSecuritySettings']);

        // Points Management System
        Route::get('/points/stats', [App\Http\Controllers\Admin\PointsManagementController::class, 'getPointsStats']);
        Route::get('/points/pending-requests', [App\Http\Controllers\Admin\PointsManagementController::class, 'getPendingRequests']);
        Route::post('/points/approve/{request}', [App\Http\Controllers\Admin\PointsManagementController::class, 'approveRequest']);
        Route::post('/points/reject/{request}', [App\Http\Controllers\Admin\PointsManagementController::class, 'rejectRequest']);
        Route::post('/points/approve-all', [App\Http\Controllers\Admin\PointsManagementController::class, 'approveAllRequests']);
        Route::get('/points/sharing-activity', [App\Http\Controllers\Admin\PointsManagementController::class, 'getSharingActivity']);
        Route::post('/points/special-event', [App\Http\Controllers\Admin\PointsManagementController::class, 'createSpecialEvent']);
        Route::get('/points/special-events', [App\Http\Controllers\Admin\PointsManagementController::class, 'getSpecialEvents']);
        Route::post('/points/settings', [App\Http\Controllers\Admin\PointsManagementController::class, 'updatePointSettings']);
        Route::post('/points/award', [App\Http\Controllers\Admin\PointsManagementController::class, 'awardPoints']);

        // Stage Management System
        Route::get('/stages/stats', [App\Http\Controllers\Admin\StageManagementController::class, 'getStageStats']);
        Route::get('/stages/{stage}/details', [App\Http\Controllers\Admin\StageManagementController::class, 'getStageDetails']);
        Route::post('/stages/{stage}/settings', [App\Http\Controllers\Admin\StageManagementController::class, 'updateStageSettings']);
        Route::get('/stages/{stage}/activities', [App\Http\Controllers\Admin\StageManagementController::class, 'getStageActivities']);
        Route::post('/stages/{stage}/activities', [App\Http\Controllers\Admin\StageManagementController::class, 'createStageActivity']);
        Route::get('/stages/{stage}/resources', [App\Http\Controllers\Admin\StageManagementController::class, 'getStageResources']);
        Route::post('/stages/{stage}/resources', [App\Http\Controllers\Admin\StageManagementController::class, 'addStageResource']);
        Route::get('/stages/{stage}/members', [App\Http\Controllers\Admin\StageManagementController::class, 'getStageMembers']);
        Route::post('/stages/activate-user', [App\Http\Controllers\Admin\StageManagementController::class, 'activateStageForUser']);

        // Platform Customization
        Route::get('/platform/settings', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'getPlatformSettings']);
        Route::post('/platform/settings', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updatePlatformSettings']);
        Route::post('/platform/upload-logo', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'uploadLogo']);
        Route::get('/platform/custom-pages', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'getCustomPages']);
        Route::post('/platform/custom-pages', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'createCustomPage']);
        Route::get('/platform/widgets', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'getWidgets']);
        Route::post('/platform/widgets', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'createWidget']);
        Route::get('/platform/email-templates', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'getEmailTemplates']);
        Route::post('/platform/email-templates', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updateEmailTemplate']);
        Route::get('/platform/pricing-plans', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'getPricingPlans']);
        Route::post('/platform/pricing-plans', [App\Http\Controllers\Admin\PlatformCustomizationController::class, 'updatePricingPlans']);
    });
});
