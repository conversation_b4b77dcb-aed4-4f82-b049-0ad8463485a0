<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PetitionType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'is_default',
        'is_custom',
        'created_by',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'is_custom' => 'boolean',
    ];

    /**
     * Get the user who created this petition type.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the petition projects of this type.
     */
    public function petitionProjects()
    {
        return $this->hasMany(PetitionProject::class);
    }

    /**
     * Scope for active petition types.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default petition types.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for custom petition types.
     */
    public function scopeCustom($query)
    {
        return $query->where('is_custom', true);
    }

    /**
     * Get default petition types.
     */
    public static function getDefaults()
    {
        return [
            [
                'name' => 'Environmental Protection',
                'slug' => 'environmental-protection',
                'description' => 'Petitions related to environmental conservation and protection',
                'is_default' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Climate Action',
                'slug' => 'climate-action',
                'description' => 'Petitions focused on climate change and sustainability',
                'is_default' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Wildlife Conservation',
                'slug' => 'wildlife-conservation',
                'description' => 'Petitions for protecting wildlife and biodiversity',
                'is_default' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Renewable Energy',
                'slug' => 'renewable-energy',
                'description' => 'Petitions promoting renewable energy adoption',
                'is_default' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Pollution Control',
                'slug' => 'pollution-control',
                'description' => 'Petitions addressing air, water, and soil pollution',
                'is_default' => true,
                'sort_order' => 5,
            ],
        ];
    }

    /**
     * Automatically generate slug when creating.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($petitionType) {
            if (empty($petitionType->slug)) {
                $petitionType->slug = Str::slug($petitionType->name);
            }
        });
    }
}
