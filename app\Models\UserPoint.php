<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserPoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'points',
        'source',
        'description',
        'metadata',
        'is_redeemed',
        'redeemed_at',
        'cash_value'
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_redeemed' => 'boolean',
        'redeemed_at' => 'datetime',
        'cash_value' => 'decimal:2'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeUnredeemed($query)
    {
        return $query->where('is_redeemed', false);
    }

    public function scopeRedeemed($query)
    {
        return $query->where('is_redeemed', true);
    }

    public function scopeBySource($query, $source)
    {
        return $query->where('source', $source);
    }
}
