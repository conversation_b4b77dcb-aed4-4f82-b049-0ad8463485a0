<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AdminAuthController extends Controller
{
    /**
     * Show admin login form.
     */
    public function showLoginForm()
    {
        return view('auth.admin.login');
    }

    /**
     * Handle admin login.
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');
        
        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            
            // Check if user is admin
            if (!$user->is_admin) {
                Auth::logout();
                return back()->withErrors([
                    'email' => 'You do not have admin privileges.',
                ]);
            }

            $request->session()->regenerate();
            return redirect()->intended('/admin');
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ]);
    }

    /**
     * Show admin registration form.
     */
    public function showRegistrationForm()
    {
        return view('auth.admin.register');
    }

    /**
     * Handle admin registration.
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Check if this is the first user (top admin)
        $isFirstUser = User::count() === 0;
        
        // Get the top admin (first registered user) for auto-referral
        $topAdmin = User::where('is_top_admin', true)->first();

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_admin' => $isFirstUser, // First user becomes admin automatically
            'is_top_admin' => $isFirstUser, // First user becomes top admin
            'admin_since' => $isFirstUser ? now() : null,
            'admin_status' => $isFirstUser ? 'approved' : 'pending', // First user auto-approved
            'referred_by' => $topAdmin ? $topAdmin->id : null, // Auto-assign to top admin if not first user
            'membership_tier' => 'earthfriendly',
            'is_active' => true,
        ]);

        // Initialize wallets for the new user
        $user->initializeWallets();

        if ($isFirstUser) {
            // Auto-login the first user (top admin)
            Auth::login($user);
            return redirect('/admin')->with('success', 'Welcome! You are now the Top Administrator.');
        } else {
            return redirect('/admin/login')->with('success', 'Registration successful! Please wait for admin approval.');
        }
    }

    /**
     * Handle admin logout.
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect('/admin/login');
    }
}
