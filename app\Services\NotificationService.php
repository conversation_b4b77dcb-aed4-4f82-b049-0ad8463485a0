<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserNotification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send a notification to a user
     */
    public static function sendNotification($userId, $type, $title, $message, $data = null, $priority = 'normal', $sendEmail = true)
    {
        try {
            // Create the notification record
            $notification = UserNotification::createNotification($userId, $type, $title, $message, $data, $priority);
            
            // Send email if enabled
            if ($sendEmail) {
                self::sendEmailNotification($userId, $title, $message, $type);
            }
            
            return $notification;
        } catch (\Exception $e) {
            Log::error('Failed to send notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email notification
     */
    private static function sendEmailNotification($userId, $title, $message, $type)
    {
        try {
            $user = User::find($userId);
            if (!$user || !$user->email) {
                return false;
            }

            // For now, we'll just log the email (in production, you'd use Mail::send)
            Log::info("Email notification sent to {$user->email}: {$title}");
            
            // Example of how you would send actual emails:
            /*
            Mail::send('emails.notification', [
                'user' => $user,
                'title' => $title,
                'message' => $message,
                'type' => $type
            ], function ($mail) use ($user, $title) {
                $mail->to($user->email, $user->name)
                     ->subject($title);
            });
            */
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send email notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send stage activation notification
     */
    public static function sendStageActivationNotification($userId, $stageName, $approved = true)
    {
        if ($approved) {
            $title = "Stage Activation Approved!";
            $message = "Congratulations! Your {$stageName} activation has been approved. You can now access your new stage area and start earning points for {$stageName} activities.";
        } else {
            $title = "Stage Activation Requires Review";
            $message = "Your {$stageName} activation is being reviewed. We'll notify you once it's processed.";
        }

        return self::sendNotification($userId, 'stage_activation', $title, $message, [
            'stage_name' => $stageName,
            'approved' => $approved
        ], 'high');
    }

    /**
     * Send payment received notification
     */
    public static function sendPaymentNotification($userId, $amount, $type = 'payment', $description = '')
    {
        $title = "Payment Received";
        $message = "You received \${$amount} " . ($description ?: $type);

        return self::sendNotification($userId, 'payment_received', $title, $message, [
            'amount' => $amount,
            'payment_type' => $type,
            'description' => $description
        ], 'normal');
    }

    /**
     * Send referral notification
     */
    public static function sendReferralNotification($userId, $referralName, $bonus = null)
    {
        $title = "New Referral Joined";
        $message = "{$referralName} joined using your referral link.";
        
        if ($bonus) {
            $message .= " You earned \${$bonus} referral bonus!";
        } else {
            $message .= " You'll earn bonuses when they activate stages.";
        }

        return self::sendNotification($userId, 'referral', $title, $message, [
            'referral_name' => $referralName,
            'bonus' => $bonus
        ], 'normal');
    }

    /**
     * Send message notification
     */
    public static function sendMessageNotification($userId, $senderName, $subject)
    {
        $title = "New Message";
        $message = "You have a new message from {$senderName}: {$subject}";

        return self::sendNotification($userId, 'message', $title, $message, [
            'sender_name' => $senderName,
            'subject' => $subject
        ], 'normal');
    }

    /**
     * Send withdrawal status notification
     */
    public static function sendWithdrawalNotification($userId, $amount, $status, $details = '')
    {
        $statusMessages = [
            'pending' => 'Your withdrawal request is being processed',
            'approved' => 'Your withdrawal has been approved',
            'completed' => 'Your withdrawal has been completed',
            'rejected' => 'Your withdrawal request was rejected'
        ];

        $title = "Withdrawal Update";
        $message = $statusMessages[$status] ?? 'Withdrawal status updated';
        
        if ($details) {
            $message .= ": {$details}";
        }

        $priority = in_array($status, ['approved', 'completed', 'rejected']) ? 'high' : 'normal';

        return self::sendNotification($userId, 'withdrawal', $title, $message, [
            'amount' => $amount,
            'status' => $status,
            'details' => $details
        ], $priority);
    }

    /**
     * Send system notification
     */
    public static function sendSystemNotification($userId, $title, $message, $priority = 'normal')
    {
        return self::sendNotification($userId, 'system', $title, $message, null, $priority);
    }

    /**
     * Send bulk notifications to multiple users
     */
    public static function sendBulkNotification($userIds, $type, $title, $message, $data = null, $priority = 'normal')
    {
        $results = [];
        
        foreach ($userIds as $userId) {
            $results[$userId] = self::sendNotification($userId, $type, $title, $message, $data, $priority);
        }
        
        return $results;
    }

    /**
     * Get unread notification count for user
     */
    public static function getUnreadCount($userId)
    {
        return UserNotification::where('user_id', $userId)->unread()->count();
    }

    /**
     * Mark notification as read
     */
    public static function markAsRead($notificationId)
    {
        $notification = UserNotification::find($notificationId);
        if ($notification) {
            $notification->markAsRead();
            return true;
        }
        return false;
    }

    /**
     * Mark all notifications as read for user
     */
    public static function markAllAsRead($userId)
    {
        return UserNotification::where('user_id', $userId)
            ->unread()
            ->update([
                'read' => true,
                'read_at' => now()
            ]);
    }
}
