<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('point_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('source')->unique(); // daily_visit, project_completion, etc.
            $table->string('name'); // Display name
            $table->text('description')->nullable();
            $table->integer('points_awarded');
            $table->boolean('is_active')->default(true);
            $table->json('conditions')->nullable(); // Additional conditions for earning points
            $table->decimal('cash_conversion_rate', 8, 4)->default(0.01); // Points to cash rate
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('point_configurations');
    }
};
