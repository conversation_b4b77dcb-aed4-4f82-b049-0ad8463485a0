<?php $__env->startSection('title', 'Edit Profile'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Edit Profile</h1>
        <p class="mt-2 text-gray-600">Update your personal information and account settings</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Information -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Profile Information</h3>
                </div>
                <form method="POST" action="<?php echo e(route('profile.update')); ?>" class="p-6 space-y-6">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <!-- Name Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700">First Name</label>
                            <input type="text" name="first_name" id="first_name" value="<?php echo e(old('first_name', $user->first_name ?: explode(' ', $user->name)[0] ?? '')); ?>"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name</label>
                            <input type="text" name="last_name" id="last_name" value="<?php echo e(old('last_name', $user->last_name ?: (count(explode(' ', $user->name)) > 1 ? implode(' ', array_slice(explode(' ', $user->name), 1)) : ''))); ?>"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" name="email" id="email" value="<?php echo e(old('email', $user->email)); ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="text" name="phone" id="phone" value="<?php echo e(old('phone', $user->phone)); ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Website -->
                    <div>
                        <label for="website" class="block text-sm font-medium text-gray-700">Website</label>
                        <input type="url" name="website" id="website" value="<?php echo e(old('website', $user->website)); ?>" 
                               placeholder="https://example.com"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['website'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Location Information -->
                    <div class="bg-gray-50 px-4 py-5 sm:p-6 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Location Information</h3>

                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                                <input type="text" name="address" id="address" value="<?php echo e(old('address', $user->address)); ?>"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                                    <input type="text" name="city" id="city" value="<?php echo e(old('city', $user->city)); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    <label for="state" class="block text-sm font-medium text-gray-700">State/Province</label>
                                    <input type="text" name="state" id="state" value="<?php echo e(old('state', $user->state)); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div>
                                <label for="country" class="block text-sm font-medium text-gray-700">Country</label>
                                <select name="country" id="country"
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option value="">Select your country</option>
                                    <option value="US" <?php echo e(old('country', $user->country) === 'US' ? 'selected' : ''); ?>>United States</option>
                                    <option value="CA" <?php echo e(old('country', $user->country) === 'CA' ? 'selected' : ''); ?>>Canada</option>
                                    <option value="GB" <?php echo e(old('country', $user->country) === 'GB' ? 'selected' : ''); ?>>United Kingdom</option>
                                    <option value="AU" <?php echo e(old('country', $user->country) === 'AU' ? 'selected' : ''); ?>>Australia</option>
                                    <option value="DE" <?php echo e(old('country', $user->country) === 'DE' ? 'selected' : ''); ?>>Germany</option>
                                    <option value="FR" <?php echo e(old('country', $user->country) === 'FR' ? 'selected' : ''); ?>>France</option>
                                    <option value="IT" <?php echo e(old('country', $user->country) === 'IT' ? 'selected' : ''); ?>>Italy</option>
                                    <option value="ES" <?php echo e(old('country', $user->country) === 'ES' ? 'selected' : ''); ?>>Spain</option>
                                    <option value="NL" <?php echo e(old('country', $user->country) === 'NL' ? 'selected' : ''); ?>>Netherlands</option>
                                    <option value="SE" <?php echo e(old('country', $user->country) === 'SE' ? 'selected' : ''); ?>>Sweden</option>
                                    <option value="NO" <?php echo e(old('country', $user->country) === 'NO' ? 'selected' : ''); ?>>Norway</option>
                                    <option value="DK" <?php echo e(old('country', $user->country) === 'DK' ? 'selected' : ''); ?>>Denmark</option>
                                    <option value="FI" <?php echo e(old('country', $user->country) === 'FI' ? 'selected' : ''); ?>>Finland</option>
                                    <option value="JP" <?php echo e(old('country', $user->country) === 'JP' ? 'selected' : ''); ?>>Japan</option>
                                    <option value="KR" <?php echo e(old('country', $user->country) === 'KR' ? 'selected' : ''); ?>>South Korea</option>
                                    <option value="SG" <?php echo e(old('country', $user->country) === 'SG' ? 'selected' : ''); ?>>Singapore</option>
                                    <option value="NZ" <?php echo e(old('country', $user->country) === 'NZ' ? 'selected' : ''); ?>>New Zealand</option>
                                    <option value="BR" <?php echo e(old('country', $user->country) === 'BR' ? 'selected' : ''); ?>>Brazil</option>
                                    <option value="MX" <?php echo e(old('country', $user->country) === 'MX' ? 'selected' : ''); ?>>Mexico</option>
                                    <option value="IN" <?php echo e(old('country', $user->country) === 'IN' ? 'selected' : ''); ?>>India</option>
                                    <option value="CN" <?php echo e(old('country', $user->country) === 'CN' ? 'selected' : ''); ?>>China</option>
                                    <option value="ZA" <?php echo e(old('country', $user->country) === 'ZA' ? 'selected' : ''); ?>>South Africa</option>
                                    <option value="NG" <?php echo e(old('country', $user->country) === 'NG' ? 'selected' : ''); ?>>Nigeria</option>
                                    <option value="KE" <?php echo e(old('country', $user->country) === 'KE' ? 'selected' : ''); ?>>Kenya</option>
                                    <option value="EG" <?php echo e(old('country', $user->country) === 'EG' ? 'selected' : ''); ?>>Egypt</option>
                                    <option value="AE" <?php echo e(old('country', $user->country) === 'AE' ? 'selected' : ''); ?>>United Arab Emirates</option>
                                    <option value="SA" <?php echo e(old('country', $user->country) === 'SA' ? 'selected' : ''); ?>>Saudi Arabia</option>
                                    <option value="TR" <?php echo e(old('country', $user->country) === 'TR' ? 'selected' : ''); ?>>Turkey</option>
                                    <option value="RU" <?php echo e(old('country', $user->country) === 'RU' ? 'selected' : ''); ?>>Russia</option>
                                    <option value="PL" <?php echo e(old('country', $user->country) === 'PL' ? 'selected' : ''); ?>>Poland</option>
                                    <option value="CZ" <?php echo e(old('country', $user->country) === 'CZ' ? 'selected' : ''); ?>>Czech Republic</option>
                                    <option value="HU" <?php echo e(old('country', $user->country) === 'HU' ? 'selected' : ''); ?>>Hungary</option>
                                    <option value="GR" <?php echo e(old('country', $user->country) === 'GR' ? 'selected' : ''); ?>>Greece</option>
                                    <option value="PT" <?php echo e(old('country', $user->country) === 'PT' ? 'selected' : ''); ?>>Portugal</option>
                                    <option value="IE" <?php echo e(old('country', $user->country) === 'IE' ? 'selected' : ''); ?>>Ireland</option>
                                    <option value="BE" <?php echo e(old('country', $user->country) === 'BE' ? 'selected' : ''); ?>>Belgium</option>
                                    <option value="CH" <?php echo e(old('country', $user->country) === 'CH' ? 'selected' : ''); ?>>Switzerland</option>
                                    <option value="AT" <?php echo e(old('country', $user->country) === 'AT' ? 'selected' : ''); ?>>Austria</option>
                                    <option value="OTHER" <?php echo e(old('country', $user->country) === 'OTHER' ? 'selected' : ''); ?>>Other</option>
                                </select>
                                <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Bio -->
                    <div>
                        <label for="bio" class="block text-sm font-medium text-gray-700">Bio</label>
                        <textarea name="bio" id="bio" rows="4" 
                                  placeholder="Tell us about yourself and your sustainable living journey..."
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"><?php echo e(old('bio', $user->bio)); ?></textarea>
                        <?php $__errorArgs = ['bio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>

            <!-- Change Password -->
            <div class="bg-white shadow rounded-lg mt-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Change Password</h3>
                </div>
                <form method="POST" action="<?php echo e(route('profile.update-password')); ?>" class="p-6 space-y-6">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <!-- Current Password -->
                    <div>
                        <label for="current_password" class="block text-sm font-medium text-gray-700">Current Password</label>
                        <input type="password" name="current_password" id="current_password" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- New Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">New Password</label>
                        <input type="password" name="password" id="password" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                        <input type="password" name="password_confirmation" id="password_confirmation" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            Update Password
                        </button>
                    </div>
                </form>
            </div>

            <!-- Bank Details -->
            <div class="bg-white shadow rounded-lg mt-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Bank Details</h3>
                    <p class="text-sm text-gray-600">Add your bank details for withdrawals</p>
                </div>
                <form method="POST" action="<?php echo e(route('profile.update-bank-details')); ?>" class="p-6 space-y-6">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <!-- Bank Name -->
                    <div>
                        <label for="bank_name" class="block text-sm font-medium text-gray-700">Bank Name</label>
                        <input type="text" name="bank_name" id="bank_name" value="<?php echo e(old('bank_name', $user->bank_name)); ?>"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['bank_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Account Holder Name -->
                    <div>
                        <label for="account_holder_name" class="block text-sm font-medium text-gray-700">Account Holder Name</label>
                        <input type="text" name="account_holder_name" id="account_holder_name" value="<?php echo e(old('account_holder_name', $user->account_holder_name)); ?>"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['account_holder_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Account Number -->
                    <div>
                        <label for="account_number" class="block text-sm font-medium text-gray-700">Account Number</label>
                        <input type="text" name="account_number" id="account_number" value="<?php echo e(old('account_number', $user->account_number)); ?>"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['account_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Routing Number and SWIFT Code -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="routing_number" class="block text-sm font-medium text-gray-700">Routing Number</label>
                            <input type="text" name="routing_number" id="routing_number" value="<?php echo e(old('routing_number', $user->routing_number)); ?>"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <?php $__errorArgs = ['routing_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="swift_code" class="block text-sm font-medium text-gray-700">SWIFT Code</label>
                            <input type="text" name="swift_code" id="swift_code" value="<?php echo e(old('swift_code', $user->swift_code)); ?>"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <?php $__errorArgs = ['swift_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- IBAN -->
                    <div>
                        <label for="iban" class="block text-sm font-medium text-gray-700">IBAN (International Bank Account Number)</label>
                        <input type="text" name="iban" id="iban" value="<?php echo e(old('iban', $user->iban)); ?>"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                        <?php $__errorArgs = ['iban'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Bank Address -->
                    <div>
                        <label for="bank_address" class="block text-sm font-medium text-gray-700">Bank Address</label>
                        <textarea name="bank_address" id="bank_address" rows="3"
                                  placeholder="Enter the bank's full address"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"><?php echo e(old('bank_address', $user->bank_address)); ?></textarea>
                        <?php $__errorArgs = ['bank_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <?php if($user->hasBankDetails()): ?>
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Bank Details Status</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>
                                        <?php if($user->bank_details_verified): ?>
                                            ✅ Your bank details have been verified and can be used for withdrawals.
                                        <?php else: ?>
                                            ⏳ Your bank details are pending verification. You'll be notified once verified.
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="flex justify-end">
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            Update Bank Details
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Profile Summary -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Profile Summary</h3>
                </div>
                <div class="p-6">
                    <div class="text-center mb-6">
                        <div class="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-indigo-600"><?php echo e(substr($user->name, 0, 1)); ?></span>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900"><?php echo e($user->name); ?></h4>
                        <p class="text-sm text-gray-600">
                            <?php if($user->isEarthFriendlyMember()): ?>
                                Earth-Friendly Member
                            <?php else: ?>
                                Light Member
                            <?php endif; ?>
                        </p>
                    </div>

                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Member Since</span>
                            <span class="text-sm font-medium text-gray-900"><?php echo e($user->created_at->format('M Y')); ?></span>
                        </div>
                        
                        <?php if($user->isLightMember()): ?>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Active Stages</span>
                            <span class="text-sm font-medium text-gray-900"><?php echo e($user->activeStageActivations->count()); ?></span>
                        </div>
                        <?php endif; ?>

                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Earnings</span>
                            <span class="text-sm font-medium text-green-600">$<?php echo e(number_format($user->total_earnings, 2)); ?></span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Referrals</span>
                            <span class="text-sm font-medium text-gray-900"><?php echo e($user->total_referrals); ?></span>
                        </div>
                    </div>

                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <a href="<?php echo e(url('/profile')); ?>"
                           class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            View Public Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\lestel site\lestel\resources\views/profile/edit.blade.php ENDPATH**/ ?>