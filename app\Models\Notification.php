<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'user_id',
        'title',
        'message',
        'data',
        'priority',
        'category',
        'is_read',
        'is_push_sent',
        'read_at',
        'expires_at',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'is_push_sent' => 'boolean',
        'read_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user that owns the notification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for unread notifications.
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for read notifications.
     */
    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    /**
     * Scope for user notifications.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for admin notifications.
     */
    public function scopeForAdmins($query)
    {
        return $query->where('type', 'admin');
    }

    /**
     * Scope for system notifications.
     */
    public function scopeSystem($query)
    {
        return $query->where('type', 'system');
    }

    /**
     * Scope for priority notifications.
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope for category notifications.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for non-expired notifications.
     */
    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(): void
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    /**
     * Mark notification as unread.
     */
    public function markAsUnread(): void
    {
        $this->update([
            'is_read' => false,
            'read_at' => null,
        ]);
    }

    /**
     * Check if notification is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Get priority color class.
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'urgent' => 'text-red-600 bg-red-100',
            'high' => 'text-orange-600 bg-orange-100',
            'normal' => 'text-blue-600 bg-blue-100',
            'low' => 'text-gray-600 bg-gray-100',
            default => 'text-blue-600 bg-blue-100',
        };
    }

    /**
     * Get category icon.
     */
    public function getCategoryIconAttribute(): string
    {
        return match ($this->category) {
            'approval' => 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
            'payment' => 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
            'system' => 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
            'security' => 'M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z',
            default => 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
        };
    }

    /**
     * Create a user notification.
     */
    public static function createForUser(
        User $user,
        string $title,
        string $message,
        string $category = null,
        string $priority = 'normal',
        array $data = [],
        \DateTime $expiresAt = null
    ): self {
        return self::create([
            'type' => 'user',
            'user_id' => $user->id,
            'title' => $title,
            'message' => $message,
            'category' => $category,
            'priority' => $priority,
            'data' => $data,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Create an admin notification.
     */
    public static function createForAdmins(
        string $title,
        string $message,
        string $category = null,
        string $priority = 'normal',
        array $data = [],
        \DateTime $expiresAt = null
    ): self {
        return self::create([
            'type' => 'admin',
            'user_id' => null,
            'title' => $title,
            'message' => $message,
            'category' => $category,
            'priority' => $priority,
            'data' => $data,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Create a system notification.
     */
    public static function createSystem(
        string $title,
        string $message,
        string $priority = 'normal',
        array $data = [],
        \DateTime $expiresAt = null
    ): self {
        return self::create([
            'type' => 'system',
            'user_id' => null,
            'title' => $title,
            'message' => $message,
            'category' => 'system',
            'priority' => $priority,
            'data' => $data,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Get unread count for user.
     */
    public static function getUnreadCountForUser(User $user): int
    {
        return self::forUser($user->id)
            ->unread()
            ->active()
            ->count();
    }

    /**
     * Get unread count for admins.
     */
    public static function getUnreadCountForAdmins(): int
    {
        return self::forAdmins()
            ->unread()
            ->active()
            ->count();
    }

    /**
     * Mark all as read for user.
     */
    public static function markAllAsReadForUser(User $user): void
    {
        self::forUser($user->id)
            ->unread()
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
    }

    /**
     * Clean up expired notifications.
     */
    public static function cleanupExpired(): int
    {
        return self::where('expires_at', '<', now())->delete();
    }
}
