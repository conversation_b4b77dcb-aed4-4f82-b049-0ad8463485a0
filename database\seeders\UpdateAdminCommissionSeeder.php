<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MembershipStage;

class UpdateAdminCommissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stages = [
            1 => ['admin_commission' => 2.50],
            2 => ['admin_commission' => 5.00],
            3 => ['admin_commission' => 10.00],
            4 => ['admin_commission' => 17.50],
            5 => ['admin_commission' => 25.00],
            6 => ['admin_commission' => 50.00]
        ];

        foreach ($stages as $sortOrder => $data) {
            MembershipStage::where('sort_order', $sortOrder)->update($data);
        }

        $this->command->info('Updated stages with admin commission values');
    }
}
