@extends('layouts.admin')

@section('title', 'Stage 1 Management - Individual Awareness')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Stage 1 Management - Individual Awareness</h1>
                <p class="text-gray-600 mt-1">Comprehensive management for Stage 1 activation features</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="exportStageData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Export Data
                </button>
                <button onclick="stageSettings()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Stage Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Stage Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">3,247</h3>
                    <p class="text-sm text-gray-600">Stage 1 Activations</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">1,847</h3>
                    <p class="text-sm text-gray-600">Completed Activities</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">$24,890</h3>
                    <p class="text-sm text-gray-600">Total Rewards</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">456</h3>
                    <p class="text-sm text-gray-600">Community Posts</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">89</h3>
                    <p class="text-sm text-gray-600">Resources</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" x-data="{ activeTab: 'activities' }">
                <button @click="activeTab = 'activities'" :class="activeTab === 'activities' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Activities Management
                </button>
                <button @click="activeTab = 'rewards'" :class="activeTab === 'rewards' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Rewards System
                </button>
                <button @click="activeTab = 'community'" :class="activeTab === 'community' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Community Features
                </button>
                <button @click="activeTab = 'resources'" :class="activeTab === 'resources' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Resources Library
                </button>
                <button @click="activeTab = 'analytics'" :class="activeTab === 'analytics' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Stage Analytics
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-data="{ activeTab: 'activities' }">
        <!-- Activities Management Tab -->
        <div x-show="activeTab === 'activities'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Stage 1 Activities</h3>
                        <button onclick="createActivity()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Create Activity
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Leadership Activities -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Leadership</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    12 Activities
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Completed:</span>
                                    <span class="font-medium text-green-600">847</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">In Progress:</span>
                                    <span class="font-medium text-yellow-600">234</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Rewards Earned:</span>
                                    <span class="font-medium text-gray-900">$8,470</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="manageLeadershipActivities()" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Manage Activities
                                </button>
                            </div>
                        </div>

                        <!-- Knowledge Activities -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Knowledge</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    15 Activities
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Completed:</span>
                                    <span class="font-medium text-green-600">1,234</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">In Progress:</span>
                                    <span class="font-medium text-yellow-600">456</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Rewards Earned:</span>
                                    <span class="font-medium text-gray-900">$12,340</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="manageKnowledgeActivities()" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Manage Activities
                                </button>
                            </div>
                        </div>

                        <!-- Action Activities -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Action</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                    8 Activities
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Completed:</span>
                                    <span class="font-medium text-green-600">567</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">In Progress:</span>
                                    <span class="font-medium text-yellow-600">123</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Rewards Earned:</span>
                                    <span class="font-medium text-gray-900">$5,670</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="manageActionActivities()" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Manage Activities
                                </button>
                            </div>
                        </div>

                        <!-- Awareness Activities -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">Awareness</h4>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                    10 Activities
                                </span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Completed:</span>
                                    <span class="font-medium text-green-600">789</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">In Progress:</span>
                                    <span class="font-medium text-yellow-600">234</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Rewards Earned:</span>
                                    <span class="font-medium text-gray-900">$7,890</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button onclick="manageAwarenessActivities()" class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Manage Activities
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rewards System Tab -->
        <div x-show="activeTab === 'rewards'" class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Stage 1 Rewards Configuration</h3>
                        <button onclick="createReward()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            Create Reward
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Reward Configuration -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-medium text-gray-900">Activity Completion Reward</h4>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                    <button onclick="editReward(1)" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Reward Amount</label>
                                    <input type="text" value="$25.00" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" readonly>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Points Awarded</label>
                                    <input type="text" value="250" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" readonly>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Trigger</label>
                                    <input type="text" value="Complete any activity" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportStageData() {
    alert('Export Stage 1 data functionality will be implemented');
}

function stageSettings() {
    alert('Stage 1 settings functionality will be implemented');
}

function createActivity() {
    alert('Create Stage 1 activity functionality will be implemented');
}

function manageLeadershipActivities() {
    alert('Manage leadership activities functionality will be implemented');
}

function manageKnowledgeActivities() {
    alert('Manage knowledge activities functionality will be implemented');
}

function manageActionActivities() {
    alert('Manage action activities functionality will be implemented');
}

function manageAwarenessActivities() {
    alert('Manage awareness activities functionality will be implemented');
}

function createReward() {
    alert('Create Stage 1 reward functionality will be implemented');
}

function editReward(id) {
    alert(`Edit reward ${id} functionality will be implemented`);
}
</script>
@endsection
