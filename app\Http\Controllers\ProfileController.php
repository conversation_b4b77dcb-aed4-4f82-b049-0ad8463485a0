<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Show the profile edit form.
     */
    public function edit()
    {
        $user = Auth::user();
        return view('profile.edit', compact('user'));
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone' => ['nullable', 'string', 'max:20'],
            'bio' => ['nullable', 'string', 'max:500'],
            'location' => ['nullable', 'string', 'max:255'],
            'website' => ['nullable', 'url', 'max:255'],
            'address' => ['nullable', 'string', 'max:255'],
            'city' => ['nullable', 'string', 'max:100'],
            'state' => ['nullable', 'string', 'max:100'],
            'country' => ['nullable', 'string', 'max:3'],
        ]);

        $user->update([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'name' => $request->first_name . ' ' . $request->last_name, // Keep for backward compatibility
            'email' => $request->email,
            'phone' => $request->phone,
            'bio' => $request->bio,
            'location' => $request->location,
            'website' => $request->website,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
        ]);

        return redirect()->route('profile.edit')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('profile.edit')
            ->with('success', 'Password updated successfully!');
    }

    /**
     * Update the user's bank details.
     */
    public function updateBankDetails(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'bank_name' => ['required', 'string', 'max:255'],
            'account_holder_name' => ['required', 'string', 'max:255'],
            'account_number' => ['required', 'string', 'max:50'],
            'routing_number' => ['nullable', 'string', 'max:20'],
            'swift_code' => ['nullable', 'string', 'max:20'],
            'iban' => ['nullable', 'string', 'max:50'],
            'bank_address' => ['nullable', 'string', 'max:500'],
        ]);

        $user->update([
            'bank_name' => $request->bank_name,
            'account_holder_name' => $request->account_holder_name,
            'account_number' => $request->account_number,
            'routing_number' => $request->routing_number,
            'swift_code' => $request->swift_code,
            'iban' => $request->iban,
            'bank_address' => $request->bank_address,
            'bank_details_verified' => false, // Reset verification when details change
        ]);

        return redirect()->route('profile.edit')
            ->with('success', 'Bank details updated successfully!');
    }

    /**
     * Show the user's public profile.
     */
    public function show($id = null)
    {
        $user = $id ? \App\Models\User::findOrFail($id) : Auth::user();

        // Get user's activated stages
        $activatedStages = $user->activeStageActivations()->get();

        // Get public stats (removed active_referrals for privacy)
        $stats = [
            'total_referrals' => $user->referrals()->count(),
            'total_earnings' => $user->total_earnings,
            'activated_stages_count' => $activatedStages->count(),
            'member_since' => $user->created_at,
        ];

        return view('profile.show', compact('user', 'activatedStages', 'stats'));
    }
}
