<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MembershipStage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'min_referrals',
        'max_referrals',
        'min_years',
        'max_years',
        'commission_rate',
        'activation_bonus',
        'admin_commission',
        'description',
        'benefits',
        'is_active',
        'sort_order',
        'activation_price',
        'monthly_fee',
        'requires_approval',
        'max_activations_per_user',
    ];

    protected function casts(): array
    {
        return [
            'commission_rate' => 'decimal:2',
            'activation_bonus' => 'decimal:2',
            'admin_commission' => 'decimal:2',
            'activation_price' => 'decimal:2',
            'monthly_fee' => 'decimal:2',
            'benefits' => 'array',
            'is_active' => 'boolean',
            'requires_approval' => 'boolean',
        ];
    }

    /**
     * Get users in this membership stage.
     */
    public function users()
    {
        return $this->hasMany(User::class, 'membership_stage', 'slug');
    }

    /**
     * Scope to get active stages.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get stages ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get the stage requirements text.
     */
    public function getRequirementsTextAttribute(): string
    {
        if ($this->max_referrals) {
            return "{$this->min_referrals}-{$this->max_referrals} referrals";
        }
        
        return "{$this->min_referrals}+ referrals";
    }

    /**
     * Check if a user qualifies for this stage.
     */
    public function userQualifies(User $user): bool
    {
        $referralCount = $user->total_active_referrals;
        
        if ($referralCount < $this->min_referrals) {
            return false;
        }
        
        if ($this->max_referrals && $referralCount > $this->max_referrals) {
            return false;
        }
        
        return true;
    }

    /**
     * Get the next stage in the hierarchy.
     */
    public function getNextStageAttribute(): ?MembershipStage
    {
        return self::active()
            ->where('sort_order', '>', $this->sort_order)
            ->ordered()
            ->first();
    }

    /**
     * Get the previous stage in the hierarchy.
     */
    public function getPreviousStageAttribute(): ?MembershipStage
    {
        return self::active()
            ->where('sort_order', '<', $this->sort_order)
            ->orderBy('sort_order', 'desc')
            ->first();
    }
}
