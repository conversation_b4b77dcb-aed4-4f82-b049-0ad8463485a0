<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CrowdfundContribution extends Model
{
    use HasFactory;

    protected $fillable = [
        'crowdfund_campaign_id', 'user_id', 'amount', 'contribution_type',
        'is_anonymous', 'status', 'transaction_id', 'message',
        'expected_return', 'expected_return_date', 'return_paid'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'expected_return' => 'decimal:2',
        'expected_return_date' => 'date',
        'is_anonymous' => 'boolean',
        'return_paid' => 'boolean',
    ];

    public function campaign()
    {
        return $this->belongsTo(CrowdfundCampaign::class, 'crowdfund_campaign_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeInvestments($query)
    {
        return $query->where('contribution_type', 'investment');
    }

    public function scopeDonations($query)
    {
        return $query->where('contribution_type', 'donation');
    }

    public function scopeLoans($query)
    {
        return $query->where('contribution_type', 'loan');
    }

    public function getDisplayNameAttribute()
    {
        return $this->is_anonymous ? 'Anonymous' : $this->user->name;
    }
}
