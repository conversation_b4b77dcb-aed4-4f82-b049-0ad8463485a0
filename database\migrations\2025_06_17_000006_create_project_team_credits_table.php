<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_team_credits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('community_projects')->onDelete('cascade');
            $table->string('member_name');
            $table->string('role');
            $table->text('contribution');
            $table->string('contact_info')->nullable();
            $table->string('social_links')->nullable();
            $table->integer('credit_points')->default(0);
            $table->timestamps();
            
            $table->index(['project_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_team_credits');
    }
};
