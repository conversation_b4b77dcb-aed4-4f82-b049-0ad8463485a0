<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Profile requests system
        Schema::create('profile_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('requester_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('requested_user_id')->constrained('users')->onDelete('cascade');
            $table->text('explanation');
            $table->json('requested_fields'); // phone, email, address, etc.
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('admin_notes')->nullable();
            $table->timestamps();
        });

        // Chat monitoring system
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('receiver_id')->constrained('users')->onDelete('cascade');
            $table->text('message');
            $table->boolean('is_flagged')->default(false);
            $table->json('flagged_words')->nullable();
            $table->boolean('admin_reviewed')->default(false);
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamps();
        });

        // Alert words for chat monitoring
        Schema::create('alert_words', function (Blueprint $table) {
            $table->id();
            $table->string('word');
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Enhanced currency management
        Schema::create('currency_management', function (Blueprint $table) {
            $table->id();
            $table->string('code', 3)->unique();
            $table->string('name');
            $table->string('symbol');
            $table->string('icon')->nullable();
            $table->decimal('exchange_rate', 15, 8)->default(1.********);
            $table->boolean('is_active')->default(true);
            $table->boolean('allow_withdrawal')->default(true);
            $table->decimal('withdrawal_fee', 10, 2)->default(0.00);
            $table->decimal('conversion_fee_percentage', 5, 2)->default(0.00);
            $table->boolean('conversion_fee_enabled')->default(false);
            $table->json('withdrawal_methods')->nullable(); // bank, gateway, etc.
            $table->timestamps();
        });

        // Wallet conversion history
        Schema::create('wallet_conversions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('from_currency_id')->constrained('currency_management')->onDelete('cascade');
            $table->foreignId('to_currency_id')->constrained('currency_management')->onDelete('cascade');
            $table->decimal('from_amount', 15, 8);
            $table->decimal('to_amount', 15, 8);
            $table->decimal('exchange_rate', 15, 8);
            $table->decimal('conversion_fee', 10, 2)->default(0.00);
            $table->string('transaction_id')->unique();
            $table->enum('status', ['pending', 'completed', 'failed'])->default('pending');
            $table->timestamps();
        });

        // Invoice system
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->decimal('amount', 15, 2);
            $table->foreignId('currency_id')->constrained('currency_management')->onDelete('cascade');
            $table->enum('status', ['pending', 'paid', 'cancelled', 'overdue'])->default('pending');
            $table->enum('payment_method', ['gateway', 'bank_transfer', 'wallet'])->nullable();
            $table->json('payment_details')->nullable();
            $table->timestamp('due_date')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
        });

        // Bank transfer details for admin
        Schema::create('bank_transfer_settings', function (Blueprint $table) {
            $table->id();
            $table->string('bank_name');
            $table->string('account_name');
            $table->string('account_number');
            $table->string('routing_number')->nullable();
            $table->string('swift_code')->nullable();
            $table->text('instructions')->nullable();
            $table->boolean('is_active')->default(true);
            $table->foreignId('currency_id')->constrained('currency_management')->onDelete('cascade');
            $table->timestamps();
        });

        // Points and rewards management
        Schema::create('points_management', function (Blueprint $table) {
            $table->id();
            $table->string('action_type'); // daily_visit, referral, project_completion, etc.
            $table->integer('points_awarded');
            $table->text('description');
            $table->boolean('is_active')->default(true);
            $table->json('conditions')->nullable(); // specific conditions for earning
            $table->timestamps();
        });

        // Achievement management
        Schema::create('achievement_management', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('icon')->nullable();
            $table->string('badge_color')->default('#4F46E5');
            $table->json('requirements'); // conditions to unlock
            $table->integer('points_reward')->default(0);
            $table->decimal('cash_reward', 10, 2)->default(0.00);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // User achievements tracking
        Schema::create('user_achievements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('achievement_id')->constrained('achievement_management')->onDelete('cascade');
            $table->timestamp('earned_at');
            $table->boolean('points_claimed')->default(false);
            $table->boolean('cash_claimed')->default(false);
            $table->timestamps();
        });

        // Awareness tools management
        Schema::create('awareness_tools', function (Blueprint $table) {
            $table->id();
            $table->string('tool_name');
            $table->string('tool_type'); // banner, link, creative, etc.
            $table->text('description')->nullable();
            $table->string('file_path')->nullable();
            $table->string('link_url')->nullable();
            $table->json('settings')->nullable(); // tool-specific settings
            $table->boolean('is_active')->default(true);
            $table->integer('click_count')->default(0);
            $table->timestamps();
        });

        // Add date of birth to users table
        Schema::table('users', function (Blueprint $table) {
            $table->date('date_of_birth')->nullable()->after('email');
            $table->string('preferred_currency', 3)->default('USD')->after('date_of_birth');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['date_of_birth', 'preferred_currency']);
        });

        Schema::dropIfExists('awareness_tools');
        Schema::dropIfExists('user_achievements');
        Schema::dropIfExists('achievement_management');
        Schema::dropIfExists('points_management');
        Schema::dropIfExists('bank_transfer_settings');
        Schema::dropIfExists('invoices');
        Schema::dropIfExists('wallet_conversions');
        Schema::dropIfExists('currency_management');
        Schema::dropIfExists('alert_words');
        Schema::dropIfExists('chat_messages');
        Schema::dropIfExists('profile_requests');
    }
};
