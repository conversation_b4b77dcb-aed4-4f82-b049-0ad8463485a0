<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'source',
        'name',
        'description',
        'points_awarded',
        'is_active',
        'conditions',
        'cash_conversion_rate'
    ];

    protected $casts = [
        'conditions' => 'array',
        'is_active' => 'boolean',
        'cash_conversion_rate' => 'decimal:4'
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeBySource($query, $source)
    {
        return $query->where('source', $source);
    }
}
