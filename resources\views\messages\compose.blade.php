@extends('layouts.app')

@section('title', 'Compose Message')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Compose Message</h1>
                <p class="text-gray-600 mt-1">Send a message to another community member</p>
            </div>
            <a href="{{ route('messages.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L4.414 9H17a1 1 0 110 2H4.414l5.293 5.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Messages
            </a>
        </div>
    </div>

    <!-- Compose Form -->
    <div class="bg-white shadow rounded-lg">
        <form method="POST" action="{{ route('messages.send') }}">
            @csrf
            
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">New Message</h3>
            </div>

            <div class="px-6 py-4 space-y-6">
                <!-- Recipient -->
                <div>
                    <label for="recipient" class="block text-sm font-medium text-gray-700 mb-2">
                        To
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="recipient" 
                               name="recipient" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="Start typing a name or email..."
                               autocomplete="off">
                        
                        <!-- Dropdown for suggestions -->
                        <div id="recipient-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden">
                            <div class="py-1">
                                <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-medium text-gray-700">SM</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Sarah Miller</div>
                                            <div class="text-xs text-gray-500"><EMAIL></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-medium text-gray-700">JD</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">John Doe</div>
                                            <div class="text-xs text-gray-500"><EMAIL></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-medium text-gray-700">MC</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Mike Chen</div>
                                            <div class="text-xs text-gray-500"><EMAIL></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @error('recipient')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Subject -->
                <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                        Subject
                    </label>
                    <input type="text" 
                           id="subject" 
                           name="subject" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Enter message subject"
                           required>
                    @error('subject')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Message -->
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                        Message
                    </label>
                    <textarea id="message" 
                              name="message" 
                              rows="8" 
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Type your message here..."
                              required></textarea>
                    @error('message')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Message Templates -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Quick Templates
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <button type="button" class="template-btn text-left p-3 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <div class="font-medium text-sm text-gray-900">Welcome Message</div>
                            <div class="text-xs text-gray-500 mt-1">Welcome new community members</div>
                        </button>
                        <button type="button" class="template-btn text-left p-3 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <div class="font-medium text-sm text-gray-900">Project Invitation</div>
                            <div class="text-xs text-gray-500 mt-1">Invite to collaborate on projects</div>
                        </button>
                        <button type="button" class="template-btn text-left p-3 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <div class="font-medium text-sm text-gray-900">Thank You</div>
                            <div class="text-xs text-gray-500 mt-1">Express gratitude and appreciation</div>
                        </button>
                    </div>
                </div>

                <!-- Priority -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Priority
                    </label>
                    <select name="priority" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="normal">Normal</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>

                <!-- Email Notification -->
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="send_email" 
                           name="send_email" 
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                           checked>
                    <label for="send_email" class="ml-2 block text-sm text-gray-700">
                        Send email notification to recipient
                    </label>
                </div>
            </div>

            <!-- Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
                <div class="flex space-x-3">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        Send Message
                    </button>
                    <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zM8 8a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zm1 3a1 1 0 100 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                        </svg>
                        Save Draft
                    </button>
                </div>
                <div class="text-sm text-gray-500">
                    <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    Messages are private and secure
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Message templates
    const templates = {
        'Welcome Message': {
            subject: 'Welcome to our Environmental Community!',
            message: 'Hi there!\n\nWelcome to our amazing environmental community! I\'m excited to have you join us in making a positive impact on our planet.\n\nFeel free to explore the different stages, participate in projects, and connect with other like-minded members. If you have any questions, don\'t hesitate to reach out.\n\nLooking forward to working together for a greener future!\n\nBest regards'
        },
        'Project Invitation': {
            subject: 'Invitation to Collaborate on Environmental Project',
            message: 'Hello!\n\nI hope this message finds you well. I\'ve been working on an exciting environmental project and thought you might be interested in collaborating.\n\nThe project focuses on [project details] and I believe your skills and passion would be a great addition to our team.\n\nWould you like to learn more about this opportunity? I\'d be happy to discuss the details with you.\n\nBest regards'
        },
        'Thank You': {
            subject: 'Thank You for Your Support!',
            message: 'Dear [Name],\n\nI wanted to take a moment to express my heartfelt gratitude for your support and contribution to our environmental initiatives.\n\nYour involvement makes a real difference in our community and helps us move closer to our sustainability goals.\n\nThank you for being such an important part of our mission!\n\nWith appreciation'
        }
    };

    // Handle template selection
    document.querySelectorAll('.template-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const templateName = this.querySelector('.font-medium').textContent;
            const template = templates[templateName];
            
            if (template) {
                document.getElementById('subject').value = template.subject;
                document.getElementById('message').value = template.message;
            }
        });
    });

    // Handle recipient suggestions
    const recipientInput = document.getElementById('recipient');
    const suggestions = document.getElementById('recipient-suggestions');
    
    recipientInput.addEventListener('focus', function() {
        if (this.value.length > 0) {
            suggestions.classList.remove('hidden');
        }
    });
    
    recipientInput.addEventListener('input', function() {
        if (this.value.length > 2) {
            suggestions.classList.remove('hidden');
        } else {
            suggestions.classList.add('hidden');
        }
    });
    
    document.addEventListener('click', function(e) {
        if (!recipientInput.contains(e.target) && !suggestions.contains(e.target)) {
            suggestions.classList.add('hidden');
        }
    });
    
    // Handle suggestion selection
    suggestions.addEventListener('click', function(e) {
        const suggestion = e.target.closest('.cursor-pointer');
        if (suggestion) {
            const name = suggestion.querySelector('.font-medium').textContent;
            recipientInput.value = name;
            suggestions.classList.add('hidden');
        }
    });
});
</script>
@endsection
