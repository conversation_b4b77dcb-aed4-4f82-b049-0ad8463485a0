<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_volunteers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('community_projects')->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('skills_offered')->nullable();
            $table->text('availability')->nullable();
            $table->text('message')->nullable();
            $table->enum('status', ['pending', 'approved', 'declined', 'completed'])->default('pending');
            $table->datetime('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->integer('hours_contributed')->default(0);
            $table->text('contribution_notes')->nullable();
            $table->timestamps();
            
            $table->unique(['project_id', 'user_id']);
            $table->index(['project_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_volunteers');
    }
};
