<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserPoint;
use App\Models\PointConfiguration;
use App\Models\DailyVisit;
use Carbon\Carbon;

class PointsService
{
    /**
     * Award points to a user for a specific action
     */
    public function awardPoints(User $user, string $source, ?string $description = null, array $metadata = [])
    {
        $config = PointConfiguration::where('source', $source)->where('is_active', true)->first();
        
        if (!$config) {
            return false;
        }

        // Check if user is eligible for these points
        if (!$this->isEligibleForPoints($user, $source, $metadata)) {
            return false;
        }

        $userPoint = UserPoint::create([
            'user_id' => $user->id,
            'points' => $config->points_awarded,
            'source' => $source,
            'description' => $description ?? $config->description,
            'metadata' => $metadata
        ]);

        // Mark daily visit as awarded if applicable
        if ($source === 'daily_visit') {
            DailyVisit::where('user_id', $user->id)
                ->whereDate('visit_date', today())
                ->update(['points_awarded' => true]);
        }

        return $userPoint;
    }

    /**
     * Check if user is eligible for points from a specific source
     */
    private function isEligibleForPoints(User $user, string $source, array $metadata = []): bool
    {
        switch ($source) {
            case 'daily_visit':
                return $this->isEligibleForDailyVisitPoints($user);
            case 'project_completion':
                return $this->isEligibleForProjectPoints($user, $metadata);
            case 'referral':
                return $this->isEligibleForReferralPoints($user, $metadata);
            default:
                return true;
        }
    }

    /**
     * Check if user is eligible for daily visit points
     */
    private function isEligibleForDailyVisitPoints(User $user): bool
    {
        $today = today();
        
        // Check if user already received points today
        $alreadyAwarded = UserPoint::where('user_id', $user->id)
            ->where('source', 'daily_visit')
            ->whereDate('created_at', $today)
            ->exists();

        return !$alreadyAwarded;
    }

    /**
     * Check if user is eligible for project completion points
     */
    private function isEligibleForProjectPoints(User $user, array $metadata): bool
    {
        if (!isset($metadata['project_id'])) {
            return false;
        }

        // Check if user already received points for this project
        $alreadyAwarded = UserPoint::where('user_id', $user->id)
            ->where('source', 'project_completion')
            ->where('metadata->project_id', $metadata['project_id'])
            ->exists();

        return !$alreadyAwarded;
    }

    /**
     * Check if user is eligible for referral points
     */
    private function isEligibleForReferralPoints(User $user, array $metadata): bool
    {
        if (!isset($metadata['referred_user_id'])) {
            return false;
        }

        // Check if user already received points for this referral
        $alreadyAwarded = UserPoint::where('user_id', $user->id)
            ->where('source', 'referral')
            ->where('metadata->referred_user_id', $metadata['referred_user_id'])
            ->exists();

        return !$alreadyAwarded;
    }

    /**
     * Record daily visit and award points if eligible
     */
    public function recordDailyVisit(User $user)
    {
        $today = today();
        
        $visit = DailyVisit::firstOrCreate(
            [
                'user_id' => $user->id,
                'visit_date' => $today
            ],
            [
                'visit_count' => 1,
                'points_awarded' => false
            ]
        );

        // If this is not the first visit today, increment count
        if (!$visit->wasRecentlyCreated) {
            $visit->increment('visit_count');
        }

        // Award points if eligible and not already awarded
        if (!$visit->points_awarded && $this->isEligibleForDailyVisitPoints($user)) {
            $this->awardPoints($user, 'daily_visit', 'Daily visit reward');
        }

        return $visit;
    }

    /**
     * Redeem points for cash
     */
    public function redeemPoints(User $user, int $pointsToRedeem)
    {
        $availablePoints = $user->total_points;
        
        if ($pointsToRedeem > $availablePoints) {
            throw new \Exception('Insufficient points to redeem');
        }

        $config = PointConfiguration::where('source', 'daily_visit')->first();
        $conversionRate = $config ? $config->cash_conversion_rate : 0.01;
        
        $cashValue = $pointsToRedeem * $conversionRate;

        // Get unredeemed points in order (oldest first)
        $unredeemedPoints = UserPoint::where('user_id', $user->id)
            ->where('is_redeemed', false)
            ->orderBy('created_at')
            ->get();

        $remainingToRedeem = $pointsToRedeem;
        $totalCashValue = 0;

        foreach ($unredeemedPoints as $point) {
            if ($remainingToRedeem <= 0) break;

            if ($point->points <= $remainingToRedeem) {
                // Redeem entire point entry
                $point->update([
                    'is_redeemed' => true,
                    'redeemed_at' => now(),
                    'cash_value' => $point->points * $conversionRate
                ]);
                $remainingToRedeem -= $point->points;
                $totalCashValue += $point->points * $conversionRate;
            } else {
                // Partial redemption - split the point entry
                $redeemedPortion = UserPoint::create([
                    'user_id' => $user->id,
                    'points' => $remainingToRedeem,
                    'source' => $point->source,
                    'description' => $point->description . ' (Redeemed portion)',
                    'metadata' => $point->metadata,
                    'is_redeemed' => true,
                    'redeemed_at' => now(),
                    'cash_value' => $remainingToRedeem * $conversionRate
                ]);

                // Update original point entry
                $point->update([
                    'points' => $point->points - $remainingToRedeem
                ]);

                $totalCashValue += $remainingToRedeem * $conversionRate;
                $remainingToRedeem = 0;
            }
        }

        // Add cash to user's wallet
        $user->increment('available_balance', $totalCashValue);

        return [
            'points_redeemed' => $pointsToRedeem,
            'cash_value' => $totalCashValue,
            'new_balance' => $user->fresh()->available_balance
        ];
    }

    /**
     * Get user's points summary
     */
    public function getUserPointsSummary(User $user)
    {
        // Get redeemed points total
        $redeemedPoints = UserPoint::where('user_id', $user->id)
            ->where('is_redeemed', true)
            ->sum('points');

        return [
            'total_points' => $user->total_points,
            'redeemed_points' => $redeemedPoints,
            'points_by_source' => UserPoint::where('user_id', $user->id)
                ->where('is_redeemed', false)
                ->selectRaw('source, SUM(points) as total')
                ->groupBy('source')
                ->get()
                ->pluck('total', 'source'),
            'recent_points' => UserPoint::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get(),
            'daily_visit_streak' => $this->getDailyVisitStreak($user)
        ];
    }

    /**
     * Get user's daily visit streak
     */
    private function getDailyVisitStreak(User $user): int
    {
        $streak = 0;
        $currentDate = today();

        while (true) {
            $visit = DailyVisit::where('user_id', $user->id)
                ->whereDate('visit_date', $currentDate)
                ->first();

            if (!$visit) {
                break;
            }

            $streak++;
            $currentDate = $currentDate->subDay();
        }

        return $streak;
    }
}
