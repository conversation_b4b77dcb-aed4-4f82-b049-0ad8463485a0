<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SpinSetting;
use App\Models\SpinPrize;
use App\Models\UserSpin;
use App\Models\UserSpinProgress;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SpinManagementController extends Controller
{
    /**
     * Display the spin management dashboard
     */
    public function index()
    {
        $settings = SpinSetting::current();
        
        // Get statistics
        $stats = [
            'total_spins_today' => UserSpin::whereDate('created_at', today())->count(),
            'total_winners_today' => UserSpin::whereDate('created_at', today())->where('is_winner', true)->count(),
            'total_active_prizes' => SpinPrize::where('is_active', true)->count(),
            'unclaimed_prizes' => UserSpin::where('is_winner', true)->where('prize_claimed', false)->count(),
        ];

        // Get recent winners
        $recentWinners = UserSpin::with('user')
            ->where('is_winner', true)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get users close to winning
        $usersCloseToWin = UserSpinProgress::with('user')
            ->where('current_spin_count', '>=', $settings->spins_required_to_win - 3)
            ->orderBy('current_spin_count', 'desc')
            ->limit(10)
            ->get();

        // Get daily statistics for the last 7 days
        $dailyStats = DB::table('spin_statistics')
            ->where('date', '>=', today()->subDays(6))
            ->orderBy('date', 'desc')
            ->get();

        return view('admin.spin-management.index', compact(
            'settings',
            'stats',
            'recentWinners',
            'usersCloseToWin',
            'dailyStats'
        ));
    }

    /**
     * Display spin settings
     */
    public function settings()
    {
        $settings = SpinSetting::current();
        $deliveryMethods = [
            'events' => 'At Events',
            'cash' => 'Cash Payment',
            'pickup_station' => 'Pickup Station',
            'digital' => 'Digital Delivery',
            'instant' => 'Instant (Points)',
        ];

        return view('admin.spin-management.settings', compact('settings', 'deliveryMethods'));
    }

    /**
     * Update spin settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'spins_required_to_win' => 'required|integer|min:1|max:100',
            'spin_cost_points' => 'required|integer|min:1|max:10000',
            'is_active' => 'boolean',
            'prize_delivery_methods' => 'array',
        ]);

        try {
            $settings = SpinSetting::current();
            $settings->update([
                'spins_required_to_win' => $request->spins_required_to_win,
                'spin_cost_points' => $request->spin_cost_points,
                'is_active' => $request->boolean('is_active'),
                'prize_delivery_methods' => $request->prize_delivery_methods ?? [],
            ]);

            Log::channel('admin')->info('Spin settings updated', [
                'admin_id' => auth()->id(),
                'settings' => $request->only(['spins_required_to_win', 'spin_cost_points', 'is_active']),
            ]);

            return redirect()->back()->with('success', 'Spin settings updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating spin settings: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update spin settings.');
        }
    }

    /**
     * Display prizes management
     */
    public function prizes()
    {
        $prizes = SpinPrize::orderBy('weight', 'desc')->get();
        $deliveryMethods = [
            'events' => 'At Events',
            'cash' => 'Cash Payment',
            'pickup_station' => 'Pickup Station',
            'digital' => 'Digital Delivery',
            'instant' => 'Instant (Points)',
        ];

        return view('admin.spin-management.prizes', compact('prizes', 'deliveryMethods'));
    }

    /**
     * Store a new prize
     */
    public function storePrize(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'emoji' => 'nullable|string|max:10',
            'delivery_method' => 'required|in:events,cash,pickup_station,digital,instant',
            'cash_value' => 'nullable|numeric|min:0|max:10000',
            'points_value' => 'nullable|integer|min:0|max:100000',
            'weight' => 'required|integer|min:1|max:100',
            'max_per_day' => 'nullable|integer|min:1|max:1000',
            'max_per_user' => 'nullable|integer|min:1|max:100',
            'is_active' => 'boolean',
        ]);

        try {
            SpinPrize::create($request->all());

            Log::channel('admin')->info('New spin prize created', [
                'admin_id' => auth()->id(),
                'prize_name' => $request->name,
            ]);

            return redirect()->back()->with('success', 'Prize created successfully.');
        } catch (\Exception $e) {
            Log::error('Error creating spin prize: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to create prize.');
        }
    }

    /**
     * Update a prize
     */
    public function updatePrize(Request $request, SpinPrize $prize)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'emoji' => 'nullable|string|max:10',
            'delivery_method' => 'required|in:events,cash,pickup_station,digital,instant',
            'cash_value' => 'nullable|numeric|min:0|max:10000',
            'points_value' => 'nullable|integer|min:0|max:100000',
            'weight' => 'required|integer|min:1|max:100',
            'max_per_day' => 'nullable|integer|min:1|max:1000',
            'max_per_user' => 'nullable|integer|min:1|max:100',
            'is_active' => 'boolean',
        ]);

        try {
            $prize->update($request->all());

            Log::channel('admin')->info('Spin prize updated', [
                'admin_id' => auth()->id(),
                'prize_id' => $prize->id,
                'prize_name' => $request->name,
            ]);

            return redirect()->back()->with('success', 'Prize updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating spin prize: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update prize.');
        }
    }

    /**
     * Delete a prize
     */
    public function destroyPrize(SpinPrize $prize)
    {
        try {
            $prize->delete();

            Log::channel('admin')->info('Spin prize deleted', [
                'admin_id' => auth()->id(),
                'prize_id' => $prize->id,
                'prize_name' => $prize->name,
            ]);

            return redirect()->back()->with('success', 'Prize deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Error deleting spin prize: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to delete prize.');
        }
    }

    /**
     * Display spin statistics
     */
    public function statistics()
    {
        // Get overall statistics
        $overallStats = [
            'total_spins' => UserSpin::count(),
            'total_winners' => UserSpin::where('is_winner', true)->count(),
            'total_cash_given' => UserSpin::where('is_winner', true)->sum('cash_value'),
            'total_points_given' => UserSpin::where('is_winner', true)->sum('points_value'),
            'unclaimed_prizes' => UserSpin::where('is_winner', true)->where('prize_claimed', false)->count(),
        ];

        // Get top winners
        $topWinners = User::select('users.*', DB::raw('COUNT(user_spins.id) as win_count'), DB::raw('SUM(user_spins.cash_value) as total_cash_won'))
            ->join('user_spins', 'users.id', '=', 'user_spins.user_id')
            ->where('user_spins.is_winner', true)
            ->groupBy('users.id')
            ->orderBy('win_count', 'desc')
            ->limit(10)
            ->get();

        // Get users close to winning
        $settings = SpinSetting::current();
        $usersCloseToWin = UserSpinProgress::with('user')
            ->where('current_spin_count', '>', 0)
            ->orderBy('current_spin_count', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($progress) use ($settings) {
                $progress->spins_remaining = max(0, $settings->spins_required_to_win - $progress->current_spin_count);
                return $progress;
            });

        // Get daily statistics for the last 30 days
        $dailyStats = DB::table('spin_statistics')
            ->where('date', '>=', today()->subDays(29))
            ->orderBy('date', 'desc')
            ->get();

        // Get prize distribution
        $prizeDistribution = UserSpin::select('prize_name', DB::raw('COUNT(*) as count'))
            ->where('is_winner', true)
            ->whereNotNull('prize_name')
            ->groupBy('prize_name')
            ->orderBy('count', 'desc')
            ->get();

        return view('admin.spin-management.statistics', compact(
            'overallStats',
            'topWinners',
            'usersCloseToWin',
            'dailyStats',
            'prizeDistribution'
        ));
    }

    /**
     * Display unclaimed prizes
     */
    public function unclaimedPrizes()
    {
        $unclaimedPrizes = UserSpin::with('user')
            ->where('is_winner', true)
            ->where('prize_claimed', false)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.spin-management.unclaimed-prizes', compact('unclaimedPrizes'));
    }

    /**
     * Mark prize as claimed
     */
    public function claimPrize(Request $request, UserSpin $spin)
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $spin->claimPrize($request->admin_notes);

            Log::channel('admin')->info('Prize marked as claimed', [
                'admin_id' => auth()->id(),
                'spin_id' => $spin->id,
                'user_id' => $spin->user_id,
                'prize_name' => $spin->prize_name,
            ]);

            return redirect()->back()->with('success', 'Prize marked as claimed successfully.');
        } catch (\Exception $e) {
            Log::error('Error claiming prize: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to mark prize as claimed.');
        }
    }
}
