<?php

namespace App\Http\Controllers;

use App\Models\Withdrawal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class WithdrawalController extends Controller
{
    public function __construct()
    {
        // Middleware handled in routes
    }

    /**
     * Show withdrawal request form.
     */
    public function create()
    {
        $user = Auth::user();
        
        // Check minimum withdrawal amount (you can make this configurable)
        $minWithdrawal = 50.00;
        
        return view('withdrawals.create', compact('user', 'minWithdrawal'));
    }

    /**
     * Store a new withdrawal request.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'amount' => 'required|numeric|min:50|max:' . $user->available_balance,
            'payment_method' => 'required|string|in:bank_transfer,paypal,crypto',
            'payment_details' => 'required|array',
        ]);

        // Validate payment details based on method
        $this->validatePaymentDetails($request->payment_method, $request->payment_details);

        DB::transaction(function () use ($request, $user) {
            // Deduct amount from available balance
            $user->available_balance -= $request->amount;
            $user->save();

            // Create withdrawal request
            Withdrawal::create([
                'user_id' => $user->id,
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'payment_details' => $request->payment_details,
                'status' => 'pending',
            ]);
        });

        return redirect()->route('wallet.index')
            ->with('success', 'Withdrawal request submitted successfully! It will be processed within 3-5 business days.');
    }

    /**
     * Show withdrawal history.
     */
    public function index()
    {
        $user = Auth::user();
        
        $withdrawals = $user->withdrawals()
            ->latest()
            ->paginate(20);

        return view('withdrawals.index', compact('withdrawals'));
    }

    /**
     * Cancel a pending withdrawal.
     */
    public function cancel(Withdrawal $withdrawal)
    {
        // Check if user owns this withdrawal
        if ($withdrawal->user_id !== Auth::id()) {
            abort(403);
        }

        // Can only cancel pending withdrawals
        if ($withdrawal->status !== 'pending') {
            return redirect()->back()
                ->with('error', 'Only pending withdrawals can be cancelled.');
        }

        DB::transaction(function () use ($withdrawal) {
            // Refund the amount to user's available balance
            $user = $withdrawal->user;
            $user->available_balance += $withdrawal->amount;
            $user->save();

            // Update withdrawal status
            $withdrawal->status = 'cancelled';
            $withdrawal->processed_at = now();
            $withdrawal->admin_notes = 'Cancelled by user';
            $withdrawal->save();
        });

        return redirect()->back()
            ->with('success', 'Withdrawal cancelled and amount refunded to your balance.');
    }

    /**
     * Validate payment details based on payment method.
     */
    private function validatePaymentDetails(string $method, array $details): void
    {
        switch ($method) {
            case 'bank_transfer':
                request()->validate([
                    'payment_details.account_name' => 'required|string|max:255',
                    'payment_details.account_number' => 'required|string|max:50',
                    'payment_details.bank_name' => 'required|string|max:255',
                    'payment_details.routing_number' => 'nullable|string|max:50',
                ]);
                break;
                
            case 'paypal':
                request()->validate([
                    'payment_details.paypal_email' => 'required|email|max:255',
                ]);
                break;
                
            case 'crypto':
                request()->validate([
                    'payment_details.wallet_address' => 'required|string|max:255',
                    'payment_details.currency' => 'required|string|in:BTC,ETH,USDT',
                ]);
                break;
        }
    }
}
